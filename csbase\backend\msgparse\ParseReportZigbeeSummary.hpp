#ifndef __PARSE_REPORT_ZIGBEE_SUMMARY_MSG_H__
#define __PARSE_REPORT_ZIGBEE_SUMMARY_MSG_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "DclientMsgSt.h"

namespace akcs_msgparse
{

/*
<Msg>
<Type>ReportZigbeeSummary</Type>
<Params>
<Device>
    <ZigbeeDeviceID>14520404</ZigbeeDeviceID>   
    <Version>1758745320 </Version>
</Device>
<Device>
    <ZigbeeDeviceID>245544524</ZigbeeDeviceID>
    <Version>1758745320 </Version>
</Device>
</Params>
</Msg>
*/

static int ParseReportZigbeeSummaryMsg(char *buf, std::vector<SOCKET_MSG_ZIGBEE_DEVICE_SUMMARY> &zigbee_devices)
{
    if (buf == nullptr)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportZigbeeSummaryMsg text: \n" << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (nullptr == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    // 主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "Mismatched " << XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* params_node = root_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM);
    if (nullptr == params_node)
    {
        AK_LOG_WARN << "Params Node is NULL";
        return -1;
    }

    TiXmlElement* device_node = nullptr;
    for (device_node = params_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM_DEVICE); device_node; device_node = device_node->NextSiblingElement(XML_NODE_NAME_MSG_PARAM_DEVICE))
    {

        SOCKET_MSG_ZIGBEE_DEVICE_SUMMARY zigbee_device;
        memset(&zigbee_device, 0, sizeof(zigbee_device));
        TiXmlElement* item_node = nullptr;
        for (item_node = device_node->FirstChildElement(); item_node; item_node = item_node->NextSiblingElement())
        {
            if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_ZIGBEE_DEVICE_ID) == 0)
            {
                TransUtf8ToTchar(item_node->GetText(), zigbee_device.zigbee_device_id, sizeof(zigbee_device.zigbee_device_id) / sizeof(TCHAR));
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_COMMON_VERSION) == 0)
            {
                TransUtf8ToTchar(item_node->GetText(), zigbee_device.version, sizeof(zigbee_device.version) / sizeof(TCHAR));
            }
        }
        zigbee_devices.push_back(zigbee_device);
    }

    return 0;
}

}

#endif 