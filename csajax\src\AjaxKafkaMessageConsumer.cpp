#include "AjaxKafkaMessageConsumer.h"
#include "util.h"
#include "AkLogging.h"
#include "ServiceConf.h"
#include "AjaxMsgParser.h"
#include "AjaxMessageDispatcher.h"

extern SERVICE_CONF g_service_conf;

// 先不区分事件类型
void HandleAjaxKafkaNotifyMsg::Init()
{
    
}

void HandleAjaxKafkaNotifyMsg::StartKafkaConsumer()
{
    // 一个云一个消费者组
    std::string consumer_group = std::string(g_service_conf.kafka_ajax_notify_msg_consumer_group) + "_gw_" + std::to_string(g_service_conf.gateway_num);

    kafka_.SetParma(
        g_service_conf.kafka_ajax_global_broker_ip, g_service_conf.kafka_ajax_notify_msg_topic,
        consumer_group, g_service_conf.kafka_ajax_notify_msg_thream_num
    );

    kafka_.SetConsumerCb(
        std::bind(
            &HandleAjaxKafkaNotifyMsg::HandleKafkaMessage, this, std::placeholders::_1,
            std::placeholders::_2, std::placeholders::_3, std::placeholders::_4
        )
    );

    kafka_.Start();
}

bool HandleAjaxKafkaNotifyMsg::HandleKafkaMessage(uint64_t partition, uint64_t offset, const std::string& key, const std::string& msg)
{
    AjaxMsgParser parser(msg);
    if (!parser.ParseOk())
    {
        AK_LOG_WARN << "HandleAjaxKafkaNotifyMsg::HandleKafkaMessage parse error. parse status=" << parser.GetParseStatus() << ", msg=" << msg;
        return true;
    }

    AjaxMessageDispatcher::Dispatch(parser.GetMsgKvList());
    return true;
}



