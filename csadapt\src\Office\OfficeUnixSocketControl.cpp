#include "util.h"
#include "stdafx.h"
#include <functional>
#include "AKCSView.h"
#include "AdaptDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "AK.Adapt.pb.h"
#include "AK.Crontab.pb.h"
#include "AK.BackendCommon.pb.h"
#include "BackendP2PMsgControl.h"
#include "AkcsMsgDef.h"
#include "AK.Server.pb.h"
#include "AkcsPduBase.h"
#include "OfficeIPCControl.h"
#include "AK.AdaptOffice.pb.h"
#include "AK.BackendCommon.pb.h"
#include "AK.CrontabOffice.pb.h"
#include "BackendP2PMsgControl.h"
#include "OfficeUnixSocketControl.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


extern CSADAPT_CONF gstCSADAPTConf;
OfficeUnixMsgControl* OfficeUnixMsgControl::office_unix_msg_instance_ = nullptr;

OfficeUnixMsgControl* OfficeUnixMsgControl::Instance()
{
    if (!office_unix_msg_instance_)
    {
        office_unix_msg_instance_ = new OfficeUnixMsgControl();
    }
    return office_unix_msg_instance_;
}

OfficeUnixMsgControl::OfficeUnixMsgControl()
{

}

int OfficeUnixMsgControl::OnSocketMsg(void* msg_buf, unsigned int len)
{
    uint32_t id = NTOHL(*((unsigned int*)((unsigned char*)msg_buf + 4)));
    uint32_t project_type = NTOHL(*((unsigned int*)((unsigned char*)msg_buf + 8)));
    //办公
    if (project_type != project::OFFICE && project_type != project::OFFICE_NEW)
    {
        AK_LOG_WARN << "Message Type is not OFFICE";
        return 0;
    }

    switch (id)
    {
        case MSG_P2A_NOTIFY_OFFICE_CREATE_UID:
        {
            OnCreateUid(msg_buf, len);
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_ACCOUNT_RENEW:
        {
            OnAccountRenew(msg_buf, len);
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_PM_ACCOUNT_WILL_EXPIRE:
        {
            OnPMAccountWillExpire(msg_buf, len);
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_ACCOUNT_RESETPWD:
        {
            OnResetPwd(msg_buf, len);
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_ACCOUNT_CHANGEPWD:
        {
            OnChangePwd(msg_buf, len);
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_PM_ACCOUNT_EXPIRE:
        {
            OnPMAccountExpire(msg_buf, len);
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_PM_FEATURE_WILL_EXPIRE:
        {
            OnPMFeatureWillExpire(msg_buf, len);
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_INSTALLER_FEATURE_WILL_EXPIRE:
        {
            OnInstallerFeatureWillExpire(msg_buf, len);
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_PM_FEATURE_EXPIRE:
        {
            OnPMFeatureExpire(msg_buf, len);
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_INSTALLER_FEATURE_EXPIRE:
        {
            OnInstallerFeatureExpire(msg_buf, len);
            break;
        }
        case MSG_P2A_OFFICE_ADD_NEW_SITE:
        {
            OnUserAddNewSite(msg_buf, len);
            break;
        }
        case MSG_P2A_OFFICE_ALARM_DEAL:
        {
            OnOfficeAlarmDeal(msg_buf, len);
            break;
        }
        case MSG_P2A_NEWOFFICE_ALARM_DEAL:
        {
            OnNewOfficeAlarmDeal(msg_buf, len);
            break;
        }
        default:
        {
            //TODO,chenyc:响应消息类型不匹配(由于具体消息ID未知,故只能由通用消息头带回)
            AK_LOG_WARN << "Failed to match msg id:" << id;
            return -1;
        }
    }
    return 0;
}

//创建帐号
void OfficeUnixMsgControl::OnCreateUid(void* msg_buf, unsigned int len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnCreateUid The param is NULL";
        return;
    }

    CSP2A_USER_CREATE_INFO stUserCreateInfo;
    CSP2A_USER_CREATE_INFO* pstUserCreateInfo = &stUserCreateInfo;
    memset(pstUserCreateInfo, 0, sizeof(CSP2A_USER_CREATE_INFO));
    AK::Adapt::PerCreateUser msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstUserCreateInfo->szEmail, sizeof(pstUserCreateInfo->szEmail), msg.email().c_str());
    Snprintf(pstUserCreateInfo->szPwd, sizeof(pstUserCreateInfo->szPwd), msg.pwd().c_str());
    Snprintf(pstUserCreateInfo->szQRCodeBody, sizeof(pstUserCreateInfo->szQRCodeBody), msg.qrcode_body().c_str());
    Snprintf(pstUserCreateInfo->szQRCodeUrlPath, sizeof(pstUserCreateInfo->szQRCodeUrlPath), msg.qrcode_url().c_str());
    Snprintf(pstUserCreateInfo->szUser, sizeof(pstUserCreateInfo->szUser), msg.user().c_str());

    AK_LOG_INFO << "Request office createuid:" << pstUserCreateInfo->szUser << " email:" << pstUserCreateInfo->szEmail;
    if (strlen(pstUserCreateInfo->szUser) <= 0 || strlen(pstUserCreateInfo->szEmail) <= 0 || strlen(pstUserCreateInfo->szPwd) <= 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }

    // 假邮箱标识
    pstUserCreateInfo->is_fake = GetAKCSViewInstance()->FindFakeEmail(pstUserCreateInfo->szEmail);

    //查询区域管理员试用时长，更新到账号下
    //GetPersonalAccountInstance()->DaoUpdateFreeDays(pstUserCreateInfo->szUser);
    //给csmain发送通知,新建用户需要发送邮箱
    if (OfficeIPCControl::GetInstance()->SendOfficeCreateUidMail(pstUserCreateInfo) != 0)
    {
        AK_LOG_WARN << "Send create uid mail to csmain failed";
        return;
    }
}

void OfficeUnixMsgControl::OnAccountRenew(void* msg_buf, unsigned int len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnAccountRenew The param is NULL";
        return;
    }

    CSP2A_PM_INFO stPmInfo;
    CSP2A_PM_INFO* pstPmInfo = &stPmInfo;
    memset(pstPmInfo, 0, sizeof(CSP2A_PM_INFO));
    AK::AdaptOffice::PMOfficeAccountRenew msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstPmInfo->szCommunity, sizeof(pstPmInfo->szCommunity), msg.community().c_str());
    Snprintf(pstPmInfo->szEmail, sizeof(pstPmInfo->szEmail), msg.email().c_str());
    Snprintf(pstPmInfo->szName, sizeof(pstPmInfo->szName), msg.pm_name().c_str());
    Snprintf(pstPmInfo->list, sizeof(pstPmInfo->list), msg.list().c_str());
    Snprintf(pstPmInfo->szDisUUID, sizeof(pstPmInfo->szDisUUID), msg.dis_uuid().c_str());
    pstPmInfo->nAccountNum = msg.account_num();

    AK_LOG_INFO << "Request notify pm office account renew Email, pm community: " << pstPmInfo->szCommunity;
    if (strlen(pstPmInfo->szEmail) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csroute
    if (OfficeIPCControl::GetInstance()->SendPMOfficeRenewEmail(pstPmInfo) != 0)
    {
        AK_LOG_WARN << "SendPerResetPwdMail failed";
        return;
    }
}

void OfficeUnixMsgControl::OnPMAccountWillExpire(void* msg_buf, unsigned int len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnPMAccountWillExpire The param is NULL";
        return;
    }

    CSP2A_PM_INFO stPmInfo;
    CSP2A_PM_INFO* pstPmInfo = &stPmInfo;
    memset(pstPmInfo, 0, sizeof(CSP2A_PM_INFO));
    AK::Crontab::PMAppWillBeExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstPmInfo->szCommunity, sizeof(pstPmInfo->szCommunity), msg.community().c_str());
    Snprintf(pstPmInfo->szEmail, sizeof(pstPmInfo->szEmail), msg.email().c_str());
    Snprintf(pstPmInfo->szName, sizeof(pstPmInfo->szName), msg.name().c_str());
    Snprintf(pstPmInfo->list, sizeof(pstPmInfo->list), msg.list().c_str());
    pstPmInfo->nAccountNum = msg.account_num();
    pstPmInfo->nBefore = msg.before();

    AK_LOG_INFO << "Request notify pm office account will expire Email, pm community: " << pstPmInfo->szCommunity;
    if (strlen(pstPmInfo->szEmail) == 0 || strlen(pstPmInfo->szCommunity) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csroute
    if (OfficeIPCControl::GetInstance()->SendPmOfficeAccountWillExpire(pstPmInfo) != 0)
    {
        AK_LOG_WARN << "SendPerResetPwdMail failed";
        return;
    }
}

void OfficeUnixMsgControl::OnResetPwd(void* msg_buf, unsigned int len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnResetPasswd The param is NULL";
        return;
    }
    CSP2A_USER_EAMIL_INFO stUserEmailInfo;
    CSP2A_USER_EAMIL_INFO* pstUserEmailInfo = &stUserEmailInfo;
    memset(pstUserEmailInfo, 0, sizeof(CSP2A_USER_EAMIL_INFO));
    AK::Adapt::ResetPasswd msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstUserEmailInfo->szEmail, sizeof(pstUserEmailInfo->szEmail), msg.email().c_str());
    Snprintf(pstUserEmailInfo->szToken, sizeof(pstUserEmailInfo->szToken), msg.token().c_str());
    Snprintf(pstUserEmailInfo->szUser, sizeof(pstUserEmailInfo->szUser), msg.user().c_str());
    Snprintf(pstUserEmailInfo->szRoleType, sizeof(pstUserEmailInfo->szRoleType), msg.role_type().c_str());

    AK_LOG_INFO << "Request office reset passwd notify. uid:" << pstUserEmailInfo->szUser << " email:" << pstUserEmailInfo->szEmail;
    if (strlen(pstUserEmailInfo->szUser) <= 0 || strlen(pstUserEmailInfo->szEmail) <= 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }

    //给csmain发送通知,用户重置密码需要发送邮箱
    if (OfficeIPCControl::GetInstance()->SendOfficePerResetPwdMail(pstUserEmailInfo) != 0)
    {
        AK_LOG_WARN << "SendPerResetPwdMail failed";
        return;
    }
}

void OfficeUnixMsgControl::OnChangePwd(void* msg_buf, unsigned int len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnChangePwd The param is NULL";
        return;
    }

    CSP2A_USER_CREATE_INFO stUserCreateInfo;
    CSP2A_USER_CREATE_INFO* pstUserCreateInfo = &stUserCreateInfo;
    memset(pstUserCreateInfo, 0, sizeof(CSP2A_USER_CREATE_INFO));
    AK::Adapt::PerChangePwd msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstUserCreateInfo->szEmail, sizeof(pstUserCreateInfo->szEmail), msg.email().c_str());
    Snprintf(pstUserCreateInfo->szPwd, sizeof(pstUserCreateInfo->szPwd), msg.pwd().c_str());
    Snprintf(pstUserCreateInfo->szQRCodeBody, sizeof(pstUserCreateInfo->szQRCodeBody), msg.qrcode_body().c_str());
    Snprintf(pstUserCreateInfo->szQRCodeUrlPath, sizeof(pstUserCreateInfo->szQRCodeUrlPath), msg.qrcode_url().c_str());
    Snprintf(pstUserCreateInfo->szUser, sizeof(pstUserCreateInfo->szUser), msg.user().c_str());

    AK_LOG_INFO << "Request office personal change pwd, email:" << pstUserCreateInfo->szEmail << " user:" << pstUserCreateInfo->szUser;
    if (strlen(pstUserCreateInfo->szUser) <= 0 || strlen(pstUserCreateInfo->szEmail) <= 0 || strlen(pstUserCreateInfo->szQRCodeUrlPath) <= 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csmain发送通知,用户修改密码需要发送邮箱
    if (OfficeIPCControl::GetInstance()->SendOfficePerChangePwdMail(pstUserCreateInfo) != 0)
    {
        AK_LOG_WARN << "SendPerChangePwdMail failed";
        return;
    }
}

void OfficeUnixMsgControl::OnPMAccountExpire(void* msg_buf, unsigned int len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnPMAccountExpire The param is NULL";
        return;
    }

    CSP2A_PM_INFO stPmInfo;
    CSP2A_PM_INFO* pstPmInfo = &stPmInfo;
    memset(pstPmInfo, 0, sizeof(CSP2A_PM_INFO));
    AK::CrontabOffice::PMAppExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstPmInfo->szCommunity, sizeof(pstPmInfo->szCommunity), msg.community().c_str());
    Snprintf(pstPmInfo->szEmail, sizeof(pstPmInfo->szEmail), msg.email().c_str());
    Snprintf(pstPmInfo->szName, sizeof(pstPmInfo->szName), msg.name().c_str());
    Snprintf(pstPmInfo->list, sizeof(pstPmInfo->list), msg.list().c_str());
    pstPmInfo->nAccountNum = msg.account_num();

    AK_LOG_INFO << "Request notify pm office account expire Email, pm community: " << pstPmInfo->szCommunity;
    if (strlen(pstPmInfo->szEmail) == 0 || strlen(pstPmInfo->szCommunity) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csroute
    if (OfficeIPCControl::GetInstance()->SendPmOfficeAccountExpire(pstPmInfo) != 0)
    {
        AK_LOG_WARN << "SendPmOfficeAccountExpire failed";
        return;
    }
}

void OfficeUnixMsgControl::OnPMFeatureWillExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPMFeatureWillExpire The param is NULL";
        return;
    }

    CSP2A_PM_EXPIRE stpmexpire;
    CSP2A_PM_EXPIRE* pmexpire = &stpmexpire;
    memset(pmexpire, 0, sizeof(CSP2A_PM_EXPIRE));
    AK::Crontab::PmFeatureWillExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pmexpire->email, sizeof(pmexpire->email), msg.email().c_str());
    Snprintf(pmexpire->username, sizeof(pmexpire->username), msg.name().c_str());
    Snprintf(pmexpire->community, sizeof(pmexpire->community), msg.location().c_str());
    pmexpire->nbefore = msg.before();

    AK_LOG_INFO << "Office Request PM Feature will expire, PM: " << pmexpire->username;
    if (strlen(pmexpire->email) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csroute
    if (OfficeIPCControl::GetInstance()->SendPmFeatureWillExpire(pmexpire) != 0)
    {
        AK_LOG_WARN << "SendPMFeatureWillExpire failed";
        return;
    }
}

void OfficeUnixMsgControl::OnInstallerFeatureWillExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnInstallerFeatureWillExpire The param is NULL";
        return;
    }

    CSP2A_INSTALLER_EXPIRE stinstallerexpire;
    CSP2A_INSTALLER_EXPIRE* pstinstallerexpire = &stinstallerexpire;
    memset(pstinstallerexpire, 0, sizeof(CSP2A_INSTALLER_EXPIRE));
    AK::Crontab::InstallerFeatureWillExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstinstallerexpire->szEmail, sizeof(pstinstallerexpire->szEmail), msg.email().c_str());
    Snprintf(pstinstallerexpire->szUserName, sizeof(pstinstallerexpire->szUserName), msg.name().c_str());
    Snprintf(pstinstallerexpire->community, sizeof(pstinstallerexpire->community), msg.location().c_str());
    pstinstallerexpire->nBefore = msg.before();

    AK_LOG_INFO << "Office Request Installer feature will expire, Installer: " << pstinstallerexpire->szUserName;
    if (strlen(pstinstallerexpire->szEmail) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csroute
    if (OfficeIPCControl::GetInstance()->SendInstallerFeatureWillExpire(pstinstallerexpire) != 0)
    {
        AK_LOG_WARN << "SendInstallerFeatureWillExpire failed";
        return;
    }
}

void OfficeUnixMsgControl::OnPMFeatureExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPMFeatureWillExpire The param is NULL";
        return;
    }

    CSP2A_PM_EXPIRE stpmexpire;
    CSP2A_PM_EXPIRE* pstpmexpire = &stpmexpire;
    memset(pstpmexpire, 0, sizeof(CSP2A_PM_EXPIRE));
    AK::Crontab::PmFeatureWillExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstpmexpire->email, sizeof(pstpmexpire->email), msg.email().c_str());
    Snprintf(pstpmexpire->username, sizeof(pstpmexpire->username), msg.name().c_str());
    Snprintf(pstpmexpire->community, sizeof(pstpmexpire->community), msg.location().c_str());
    pstpmexpire->nbefore = msg.before();

    AK_LOG_INFO << "Office Request PM Feature will expire, PM: " << pstpmexpire->username;
    if (strlen(pstpmexpire->email) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csroute
    if (OfficeIPCControl::GetInstance()->SendPmFeatureExpire(pstpmexpire) != 0)
    {
        AK_LOG_WARN << "SendPMFeatureWillExpire failed";
        return;
    }
}

void OfficeUnixMsgControl::OnInstallerFeatureExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnInstallerFeatureWillExpire The param is NULL";
        return;
    }

    CSP2A_INSTALLER_EXPIRE stinstallerexpire;
    CSP2A_INSTALLER_EXPIRE* pstinstallerexpire = &stinstallerexpire;
    memset(pstinstallerexpire, 0, sizeof(CSP2A_INSTALLER_EXPIRE));
    AK::Crontab::InstallerFeatureWillExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstinstallerexpire->szEmail, sizeof(pstinstallerexpire->szEmail), msg.email().c_str());
    Snprintf(pstinstallerexpire->szUserName, sizeof(pstinstallerexpire->szUserName), msg.name().c_str());
    Snprintf(pstinstallerexpire->community, sizeof(pstinstallerexpire->community), msg.location().c_str());
    pstinstallerexpire->nBefore = msg.before();

    AK_LOG_INFO << "Office Request Installer feature will expire, Installer: " << pstinstallerexpire->szUserName;
    if (strlen(pstinstallerexpire->szEmail) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csroute
    if (OfficeIPCControl::GetInstance()->SendInstallerFeatureExpire(pstinstallerexpire) != 0)
    {
        AK_LOG_WARN << "SendInstallerFeatureWillExpire failed";
        return;
    }
}

void OfficeUnixMsgControl::OnUserAddNewSite(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnUserAddNewSite The param is NULL";
        return;
    }

    CSP2A_PER_ADD_NEWSITE per_add_new_site;
    memset(&per_add_new_site, 0, sizeof(CSP2A_PER_ADD_NEWSITE));
    AK::Adapt::PerAddNewSite msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(per_add_new_site.userinfo_uuid, sizeof(per_add_new_site.userinfo_uuid), msg.userinfo_uuid().c_str());
    Snprintf(per_add_new_site.project_name, sizeof(per_add_new_site.project_name), msg.project_name().c_str());
    Snprintf(per_add_new_site.email, sizeof(per_add_new_site.email), msg.email().c_str());

    ResidentPerAccount account;
    memset(&account, 0, sizeof(ResidentPerAccount));
    if (0 == dbinterface::ResidentPersonalAccount::GetMainSiteAccountByUserInfoUUID(per_add_new_site.userinfo_uuid, account))
    {
        Snprintf(per_add_new_site.name, sizeof(per_add_new_site.name), account.name);
        Snprintf(per_add_new_site.main_site_account, sizeof(per_add_new_site.main_site_account), account.account);
    }

    AK_LOG_INFO << "OnUserAddNewSite, user: " << per_add_new_site.name;
    if (strlen(per_add_new_site.email) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    OfficeIPCControl::GetInstance()->SendUserAddNewSite(per_add_new_site);
}

// 告警处理流程：APP->slim->csadapt->csrouter->csoffice->csmain->设备
void OfficeUnixMsgControl::OnOfficeAlarmDeal(void* pMsgBuf, unsigned int nMsgLen)
{
    AK::Adapt::PersonalAlarmDeal alarm_msg;
    CHECK_PB_PARSE_MSG(alarm_msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));

    AK::BackendCommon::BackendP2PBaseMessage base = BackendP2PMsgControl::CreateP2PBaseMsg(
        AKCS_M2R_GROUP_OFFICE_ALARM_DEAL_NOTIFY_MSG,
        TransP2PMsgType::TO_APP_UID,
        alarm_msg.user(),
        BackendP2PMsgControl::DevProjectTypeToDevType(project::OFFICE),
        project::OFFICE
    );

    AK::Server::P2PAlarmDealNotifyMsg p2p_msg;
    p2p_msg.set_area_node(alarm_msg.area_node());
    p2p_msg.set_user(alarm_msg.user());
    p2p_msg.set_alarm_id(alarm_msg.alarm_id());
    p2p_msg.set_result(alarm_msg.result());
    p2p_msg.set_alarm_time(alarm_msg.alarm_time());

    base.mutable_p2palarmdealnotifymsg2()->CopyFrom(p2p_msg);
    BackendP2PMsgControl::PushMsg2Route(&base, project::OFFICE);
    return;
}

// 告警处理流程：APP->slim->csadapt->csrouter->csoffice->csmain->设备
void OfficeUnixMsgControl::OnNewOfficeAlarmDeal(void* pMsgBuf, unsigned int nMsgLen)
{
    AK::Adapt::PersonalAlarmDeal alarm_msg;
    CHECK_PB_PARSE_MSG(alarm_msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    
    AK::BackendCommon::BackendP2PBaseMessage base = BackendP2PMsgControl::CreateP2PBaseMsg(
        AKCS_M2R_GROUP_NEWOFFICE_ALARM_DEAL_NOTIFY_MSG,
        TransP2PMsgType::TO_APP_UID,
        alarm_msg.user(),
        BackendP2PMsgControl::DevProjectTypeToDevType(project::OFFICE_NEW),
        project::OFFICE_NEW
    );
    
    AK::Server::P2PAlarmDealNotifyMsg p2p_msg;
    p2p_msg.set_area_node(alarm_msg.area_node());
    p2p_msg.set_user(alarm_msg.user());
    p2p_msg.set_alarm_id(alarm_msg.alarm_id());
    p2p_msg.set_result(alarm_msg.result());
    p2p_msg.set_alarm_time(alarm_msg.alarm_time());
    
    AK_LOG_INFO << "OnNewOfficeAlarmDeal alarm_id: " << alarm_msg.alarm_id() 
                << ", area_node: " << alarm_msg.area_node() << ", user: " << alarm_msg.user();
    base.mutable_p2palarmdealnotifymsg2()->CopyFrom(p2p_msg);
    BackendP2PMsgControl::PushMsg2Route(&base, project::OFFICE_NEW);
    return;
}

