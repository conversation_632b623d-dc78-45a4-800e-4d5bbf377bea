#ifndef MESSAGE_QUEUE_H
#define MESSAGE_QUEUE_H

#include <queue>
#include <mutex>
#include <condition_variable>
#include <string>
#include <atomic>

// 线程安全的消息队列
class MessageQueue {
public:
    MessageQueue() : stop_(false) {}
    ~MessageQueue() { ShutDown(); }
    void Push(const std::string& message);
    std::string Pop();
    bool Empty() const;
    size_t Size() const;

private:
    void ShutDown();

    std::atomic<bool> stop_;
    std::queue<std::string> queue_;
    mutable std::mutex mutex_;
    std::condition_variable condition_;
};

#endif // MESSAGE_QUEUE_H 