#ifndef __DB_PERSONAL_APP_TMP_KEY_H__
#define __DB_PERSONAL_APP_TMP_KEY_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "util.h"
#include "AkcsCommonDef.h"

static const uint32_t kWeek_day[7] = {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40}; //从周日开始，到周六结束
static const uint32_t kRelay_num[4] = {0x01, 0x02, 0x04, 0x08}; //对应4个门控relay



//含string的结构体不能通过memset初始化
typedef struct PersonalTempKeyUserInfo_T
{
    std::string name;
    std::string creator;
    std::string node;
    std::string per_uuid;
    int type;
    
}PersonalTempKeyUserInfo;


namespace dbinterface
{

class PersonalAppTmpKey
{
public:
    enum SchedType
    {
        ONCE_SCHED = 0, //单次计划
        DAILY_SCHED = 1, //日计划
        WEEKLY_SCHED = 2, //周计划
        EACH_DOOR_ONCE_SCHED = 3, //每道门开一次的单次计划
    };

    enum TableType
    {
        PER_TMP_KEY = 0,
        PUB_TMP_KEY = 1,
    };

    enum DcliCheckResult
    {
        CHECK_SUCCESS = 0,
        CHECK_ERROR = 1
    };
        
    PersonalAppTmpKey();
    ~PersonalAppTmpKey();
    static int GetUserInfoFromAppTempKey(const std::string& node, const std::string& code, PersonalTempKeyUserInfo &tmpkey_info);
    static std::string GetNameFromAppTmpkeyForCommunityPubWork(const std::string& code, int mng_id, const std::string& mac, std::string& creator);
    static int GetUserInfoFromAppTempKeyForCommunityPubWork(int grade, const std::string& code, int unit_id, int mng_id, PersonalTempKeyUserInfo &tmpkey_info);
    static std::string GetNameFromAppTmpkeyForOfficePubWork(const std::string& code, int mng_id, const std::string& mac, std::string& creator);
    static std::string GetNameAndNodeFromTmpkeyForCommunityPubPersonal(int grade, const std::string& code, int unit_id, int mng_id, std::string& node, std::string& creator);
    static bool CheckPersonalAppTmpKeyByPerDev(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, std::map<std::string, AKCS_DST>& dst);
    static bool CheckPersonalAppTmpKeyByPubDev(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, std::map<std::string, AKCS_DST>& dst);
    static bool CheckPubAppTmpKey(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, std::map<std::string, AKCS_DST>& dst);
    static bool CheckTmpKeyBySingleTenantPubDev(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey,              std::map<std::string, AKCS_DST>& dst);
    static int AddTempKey(const SOCKET_MSG_DEV_REPORT_VISITOR& dev_visitor_info, const std::vector<std::string>& dev, std::map<std::string, AKCS_DST>& dst);
    static int UpdateAccessTimes(const SOCKET_MSG_DEV_REPORT_ACCESS_TIMES& report_access_times);
    static void UpdateAccessTimes(const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& tmp_key);
    static bool IsPersonalAppTmpKeyValid(CRldb* rldb_conn, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, const std::string& time_zone,  std::map<std::string, AKCS_DST>& dst);
    static bool IsPubAppTmpKeyValid(CRldb* rldb_conn, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, const std::string& time_zone, std::map<std::string, AKCS_DST>& dst);
    static void GetUnitAptByRoomID(int room_id, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey);
    static void GetUnitAptByRoomUUID(const std::string& room_uuid, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey);
    static std::string GetAccessFloorByTmpkey(const std::string& tmpkey, const std::string& node, int &is_follow_my_access, const DEVICE_SETTING& deviceSetting, const std::string& user_unit_uuid);
private:
    
};

}
#endif
