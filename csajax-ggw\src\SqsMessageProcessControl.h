#ifndef SQS_MESSAGE_PROCESS_CONTROL
#define SQS_MESSAGE_PROCESS_CONTROL

#include "MessageQueue.h"
#include <thread>
#include <string>
#include <mutex>

class SqsMessageProcessControl
{
public:

    SqsMessageProcessControl() : stop_(false) {}
    ~SqsMessageProcessControl();

    static SqsMessageProcessControl* GetInstance();

    void Init(int thread_num);

    bool AddMessageProcessTask(const std::string& message);

    void ProcessMessage();

    int GetMessageQueueSize() { return message_queue_.Size(); }

private:
    bool stop_;
    MessageQueue message_queue_;
    std::vector<std::thread> threads_;
    static SqsMessageProcessControl* instance_;

};

SqsMessageProcessControl* GetSqsMessageProcessControlInstance();

#endif