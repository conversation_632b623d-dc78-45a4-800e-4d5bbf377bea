#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "InsAppFeedback.h"

namespace dbinterface {

static const std::string ins_app_feedback_info_sec = " AccountUserInfoUUID,ContactEmail,Content,FileList,UUID ";

void InsAppFeedback::GetInsAppFeedbackFromSql(InsAppFeedbackInfo& ins_app_feedback_info, CRldbQuery& query)
{
    Snprintf(ins_app_feedback_info.account_user_info_uuid, sizeof(ins_app_feedback_info.account_user_info_uuid), query.GetRowData(0));
    Snprintf(ins_app_feedback_info.contact_email, sizeof(ins_app_feedback_info.contact_email), query.GetRowData(1));
    Snprintf(ins_app_feedback_info.content, sizeof(ins_app_feedback_info.content), query.GetRowData(2));
    Snprintf(ins_app_feedback_info.file_list, sizeof(ins_app_feedback_info.file_list), query.GetRowData(3));
    Snprintf(ins_app_feedback_info.uuid, sizeof(ins_app_feedback_info.uuid), query.GetRowData(4));
    return;
}

int InsAppFeedback::GetInsAppFeedbackByUUID(const std::string& uuid, InsAppFeedbackInfo& ins_app_feedback_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << ins_app_feedback_info_sec << " from InsAppFeedback where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetInsAppFeedbackFromSql(ins_app_feedback_info, query);
    }
    else
    {
        AK_LOG_WARN << "get InsAppFeedbackInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}



}