#ifndef __CSYUNJI_ROBOT_RPC_CLIENT_CLIENT_MNG_H__
#define __CSYUNJI_ROBOT_RPC_CLIENT_CLIENT_MNG_H__
#include <list>
#include <string>
#include <map>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "YunJiRobotClient.h"

class YunJiRobotRpcClientMng : public boost::noncopyable
{
public:
    YunJiRobotRpcClientMng()
    {}
    ~YunJiRobotRpcClientMng()
    {}
	static YunJiRobotRpcClientMng* Instance();
    void AddYunJiRobotRpcSrv(const std::string &cspbx_rpc_addr, const YunJiRobotRpcClientPtr& cspbx_rpc_cli);
    void UpdateYunJiRobotRpcSrv(const std::set<std::string> &cspbx_rpc_addrs); 
    YunJiRobotRpcClientPtr GetRpcClientInstance(const std::string &logic_srv_id);
    YunJiRobotRpcClientPtr GetRpcRandomClientInstance();
private:
    static YunJiRobotRpcClientMng* instance_;
    std::mutex rpc_clis_mutex_; 
    std::atomic<uint64_t> current_index_{0};
    std::map<std::string/*ip:port*/, YunJiRobotRpcClientPtr> csyunjirobot_rpc_clis_;
};

#endif //__CSCALL_RPC_CLIENT_CLIENT_MNG_H__