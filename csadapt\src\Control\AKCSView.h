#ifndef __AKCS_VIEW_H__
#define __AKCS_VIEW_H__

#include <string>
#include "AkcsWebMsgSt.h"
#include "json/json.h"
#include "AKCSMsg.h"
#include <mutex>
#include <deque>
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/Account.h"
#include "dbinterface/DistributorInfo.h"

//前置声明
typedef struct PRIVATE_KEY_T PRIVATE_KEY;
typedef PRIVATE_KEY RF_KEY;

enum DoorControlType
{
    CLOSE = 0,
    OPEN = 1
};



class CAKCSView
{
public:
    CAKCSView();
    ~CAKCSView();

    static CAKCSView* GetInstance();

    //远程重启设备
    void OnDeviceReboot(void* pMsgBuf, unsigned int nMSgLen);
    void OnDeviceReset(void* pMsgBuf, unsigned int nMsgLen);
    void OnAddUser(void* pMsgBuf, unsigned int nMsgLen);
    void OnDelUser(void* pMsgBuf, unsigned int nMsgLen);
    void OnResetPasswd(void* pMsgBuf, unsigned int nMsgLen);

    //同一联动系统中的用户在界面上或者app上处理告警的时候通知csmain去通知设备告警处理的结果
    void OnPersonalAlarmDeal(void* pMsgBuf, unsigned int nMsgLen);

    //个人终端用户删除图片
    void OnPersonalDelPic(void* pMsgBuf, unsigned int nMsgLen);
    //界面上创建新账号
    void OnCreateUid(void* pMsgBuf, unsigned int nMsgLen);
    //终端用户主动修改密码
    void OnChangePwd(void* pMsgBuf, unsigned int nMsgLen);
    void OnSendMessage(void* pMsgBuf, unsigned int nMsgLen);

    //社区警告被处理
    void OnCommunityAlarmDeal(void* pMsgBuf, unsigned int nMsgLen);

    //设备过期
    void OnAppExpire(void* pMsgBuf, unsigned int nMsgLen);
    void OnAppWillBeExpire(void* pMsgBuf, unsigned int nMsgLen);
    void OnFreeTrialWillBeExpire(void* pMsgBuf, unsigned int nMsgLen);

    //更新联动的联系人列表
    void UpdateNodeContactlist(const std::string& strUser);
    void UpdatePerPublicDevContactlist(const std::string& strUser);
    //csmain 通知csadapt重新写联系人配置
    void UpdateContactListByCsmain(const char* pMac);
    void OnPerPublicDevDelVirtAccount(void* pMsgBuf, unsigned int nMsgLen);
    void NotifyDelCommunityPics(uint32_t mng_id);
    void NotifyDelPersonalPics(const std::string& pic_url);
    void NotifyPerDelDevPics(const std::string& mac);
    void UpdateMacConfigByCsmain(int changetype, const char* pMac);
    void OnDelVideoStorageSched(void* pMsgBuf, unsigned int nMsgLen);
    void OnAddVideoStorageSched(void* pMsgBuf, unsigned int nMsgLen);
    void OnDelVideoStorage(void* pMsgBuf, unsigned int nMsgLen);
    void OnShareTempKeyEmail(void* pMsgBuf, unsigned int nMsgLen);
    void OnAccountActiveEmail(void* pMsgBuf, unsigned int nMsgLen);
    void OnRemoteOpenDoor(void* pMsgBuf, unsigned int nMsgLen);
    void OnRemoteOpenSecurityRelay(void *msg_buf, unsigned int msg_len);
    void OnCreatePropertyWorkEmail(void* pMsgBuf, unsigned int nMsgLen);
    void OnPmEmergencyDoorControl(void *msg_buf, unsigned int msg_len);

    //v4.5
    void OnUidRenewServer(void* pMsgBuf, unsigned int nMsgLen);
    void OnPMAccountWillExpire(void* pMsgBuf, unsigned int nMsgLen);

    //alexa登陆
    void OnAlexaLogin(void* pMsgBuf, unsigned int nMsgLen);
    //alexa设置arming
    void OnAlexaSetArming(void* pMsgBuf, unsigned int nMsgLen);
    //5.0 落地过期邮件通知
    void OnPhoneExpire(void* pMsgBuf, unsigned int nMsgLen);
    void OnPhoneWillExpire(void* pMsgBuf, unsigned int nMsgLen);
    void OnInstallerAppWillExpire(void* pMsgBuf, unsigned int nMsgLen);
    void OnInstallerPhoneWillExpire(void* pMsgBuf, unsigned int nMsgLen);
    void OnInstallerAppExpire(void* pMsgBuf, unsigned int nMsgLen);
    void OnInstallerPhoneExpire(void* pMsgBuf, unsigned int nMsgLen);
    void OnCreateRemoteDevContorl(void* pMsgBuf, unsigned int nMsgLen);

    void OnPMFeatureWillExpire(void* pMsgBuf, unsigned int nMsgLen);
    void OnInstallerFeatureWillExpire(void* pMsgBuf, unsigned int nMsgLen);

    //FACESERVER发消息通知AKCS让设备去下载图片
    void OnFaceServerPicDownload(void* msg_buf, unsigned int msg_len);
    //FACESERVER发消息通知AKCS让多台设备去下载图片
    void OnFaceServerPicBatchDownload(void* msg_buf, unsigned int msg_len);
    //FACESERVER发消息通知AKCS让设备修改人脸数据
    void OnFaceServerPicModify(void* msg_buf, unsigned int msg_len);
    //FACESERVER发消息通知AKCS让多台设备修改人脸数据
    void OnFaceServerPicBatchModify(void* msg_buf, unsigned int msg_len);
    //FACESERVER发消息通知AKCS让设备删除人脸数据
    void OnFaceServerPicDelete(void* msg_buf, unsigned int msg_len);
    //FACESERVER发消息通知AKCS让多台设备删除人脸数据
    void OnFaceServerPicBatchDelete(void* msg_buf, unsigned int msg_len);
    //发送短信验证码
    void OnSendSmsCode(void* msg_buf, unsigned int msg_len);
    void OnPmExportLog(void* msg_buf, unsigned int msg_len);
    void OnFeedbackNotify(void* msg_buf, unsigned int msg_len);
    void OnInsAppFeedback(void* msg_buf, unsigned int msg_len);
    void OnUpdateMacConfig(void* msg_buf, unsigned int msg_len);


    /**
     *  设备批量Once Autop下发
     * @param msg_buf 
     * @param msg_len 
     */
    void OnOnceAutopNotify(void* msg_buf, unsigned int msg_len);

    /**
     * 删除APP账号通知
     * 
     * @param msg_buf 
     * @param msg_len 
     */
    void OnDelAppAccountNotify(void* msg_buf, unsigned int msg_len);
    void OnPMAppAccountWillBeExpire(void* pMsgBuf, unsigned int nMsgLen);
    void OnPMAppAccountExpire(void* pMsgBuf, unsigned int nMsgLen);
    void OnPMAccountActiveEmail(void* msg_buf, unsigned int msg_len);
    void PushLinKerExpire(const SOCKET_MSG_LINKER_EXPIRE_MESSAGE& expire_msg, const LINKER_NORMAL_MSG &linker_msg);
    void OnThirdPartyLockNotify(void* pMsgBuf, unsigned int nMsgLen);
    void OnUserAddNewSite(void* pMsgBuf, unsigned int nMsgLen);
    void OnPmWebLinkNewSites(void* pMsgBuf, unsigned int nMsgLen);
    void OnPmWebCreateUid(void* pMsgBuf, unsigned int nMsgLen);
    void OnPmWebChangePwd(void* pMsgBuf, unsigned int nMsgLen);
    void OnSendCommonSmsCode(void *msg_buf, unsigned int msg_len);
    void OnSendCommonEmailCode(void *msg_buf, unsigned int msg_len);
    void OnPcapCaptureNotify(void* msg_buf, unsigned int msg_len);
    void OnSendEmailNotify(void* msg_buf, unsigned int msg_len);
    void OnSendCrontabEmailNotify(void* msg_buf, unsigned int msg_len);
    int checkUserEnableSmartHome(std::string account, int expire_type, int notify_type, int before);
    int FindFakeEmail(std::string email);
    void OnKitAccountLogOff(void* msg_buf, unsigned int msg_len);
    void OnSipPcapCaptureNotify(void* msg_buf, unsigned int msg_len);
    void OnOpenApiHealthCheckNotify(void* msg_buf, unsigned int msg_len);
    void OnDeviceRemoteOpenDoor(void* pMsgBuf, unsigned int nMsgLen);
    void OnRequestDeviceCapture(void* msg_buf, unsigned int msg_len);
    void OnSmartLockUpdate(void* msg_buf, unsigned int msg_len);
    void OnSmartLockHttpUpMessageRoute(void* msg_buf, unsigned int msg_len);
    void OnSmartLockUnlockSL50(void* msg_buf, unsigned int msg_len);
    int CheckEnableSmarthome(const ResidentPerAccount &account);    
private:
    int OneDeviceDoorControl(DEVICE_SETTING* cur_device_setting, int type);
    void RegularyAutopOneDevice(const std::string &mac);
    std::string WriteCodeToRedis(const std::string &account, const std::string &code);

    int GetEmailDiff(std::string email_before, std::string email_after);
    void OnPmAppRenewServer(ResidentPerAccount& account);
    int EmergencyNotifyHandle(const dbinterface::AccountInfo& project_info, const EmergencyDoorControlInfo& emergency_info);
    int EmergencyDoorControlHandle(const dbinterface::AccountInfo& project_info, const EmergencyDoorControlInfo& emergency_info);
    void SendEmergencyNotify(const dbinterface::AccountInfo& project_info, int control_type, int project_type);
    void GetCommunityAllAppList(const std::string& project_uuid, std::set<std::string>& app_list);
    bool CheckEmergencyNeedNotify(int project_id, int project_type);
    void InsertEmegencyNotifyAlarmLog(const dbinterface::AccountInfo& project_info, int control_type);

    bool CheckPersonalSmarthome(const ResidentPerAccount &account);
    bool CheckCommunitySmarthome(const ResidentPerAccount &account);
    int CheckEnableSmarthomeByEmail(const std::string &email);
    int CheckEnableSmarthomeByUid(const std::string &uid);
private:
    CAKCSView(const CAKCSView&);
    CAKCSView& operator = (const CAKCSView&);

private:

    static CAKCSView* instance;
    std::mutex email_lock_;
    std::deque <std::string> fake_emails_;
};

CAKCSView* GetAKCSViewInstance();

#endif //__AKCS_VIEW_H__
