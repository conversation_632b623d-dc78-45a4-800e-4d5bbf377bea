#ifndef __RESIDENT_PERSONAL_ACCOUNT_H__
#define __RESIDENT_PERSONAL_ACCOUNT_H__
#include <string>
#include <memory>
#include <map>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include <assert.h>
#include <set>
#include <boost/noncopyable.hpp>
#include <time.h>
#include "AkcsCommonDef.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/DbCommonSt.h"
#include "dbinterface/AccountUserInfo.h"
#include "util_time.h"
#include "AKCSMsg.h"

enum SipAccountType
{
    PERSONAL_SIP,
    GROUP_SIP,    
};


typedef struct ResidentPerAccountAdapt_T
{   
    int init_status;
    char sipgroup[32];
    int call_type;
    int call_loop;
    int enable_motion;
    int motion_time;
    char employee_id[32];
    int flags;
    int  phone_status; //0:不使用落地呼叫，1:使用
}ResidentPersonalAccountCnf;

typedef struct ResidentPersonalAccount_T
{
    uint32_t id;
    char account[32];
    char uuid[64];
    char name[128];
    uint32_t parent_id;
    uint32_t unit_id;
    uint32_t phone_status;
    char phone[32];
    char phone2[32];
    char phone3[32];
    char phone_code[16];
    char phone_with_phonecode[64];
    char sip_account[32];
    char call_seq[128];
    int ip_direct;
    int active;
    int is_expire;
    int role;
    int is_init;
    int is_show_tmpkey;
    int only_apt;
    int room_id;
    int switch_value;
    char passwd[65];
    char language[32];
    int enable_smarthome;
    char parent_uuid[64];
    uint32_t is_feature_expire;//单住户是高级功能是否过期、社区是用户落地是否过期
    char timezone[36];
    time_t exp_stamp;
    time_t phone_exp_stamp;
    time_t cur_stamp;
    char expire_time[20];
    char phone_expire_time[20];
    char room_number[64];
    int lastread_message_id;
    char user_info_uuid[64];
    char sip_group[32];
    char nfc_code[32];
    char ble_code[32];
    char firstname[128];
    char lastname[128];
    char unit_uuid[64];
    char ringtone[64];
    csmain::DeviceType conn_type;//COMMUNITY_APP....
    ResidentPersonalAccountCnf cnf; 
    int app_login_status;//app是否登录过的标识
    char community_room_uuid[64];
    int akcs_last_sql_index;//当前account_sec使用的最后一个下标
    int strong_alarm;

    //TODO：如果是类就很好处理
    char* getEmail() const
    {
        PerAccountUserInfo per_account_user;
        if (0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByUUID(user_info_uuid, per_account_user))
        {
            snprintf(temp_email, sizeof(temp_email), "%s", per_account_user.email);
        }
        return temp_email;
    }
    char* getMobileNumber() const
    {
        PerAccountUserInfo per_account_user;
        if (0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByUUID(user_info_uuid, per_account_user))
        {
            snprintf(temp_mobile_number, sizeof(temp_mobile_number), "%s", per_account_user.mobile_number);
        }
        return temp_mobile_number;
    }
    ResidentPersonalAccount_T()
    {
        memset(this, 0, sizeof(*this));
    }
private:    
    mutable char temp_email[64];
    mutable char temp_mobile_number[32];    
}ResidentPerAccount;

using ResidentPerAccountMap = std::map<std::string/*uid*/, ResidentPerAccount>;
using ResidentUUIDPerAccountMap = std::map<std::string/*uuid*/, ResidentPerAccount>;
using ResidentPerAccountSlaveMap= std::multimap<std::string/*main uuid*/, ResidentPerAccount>;



typedef struct PersonalFaceKey_T
{
    char account_name[128];
    char account_uuid[64];
}PersonalFaceKeyInfo;

typedef struct PersonalNfcKey_T
{
    char account_name[128];
    char account_uuid[64];
}PersonalNfcKeyInfo;

typedef struct PersonalAccountPhoneInfo_T{
    char name[256];
    char node[16];
    char phone[32];  
    char account[16];
    char parent_uuid[64];
    int role;
    int match_num;       //匹配phone的长度
    int node_switch;
    int node_active;
    unsigned int mng_id;//社区id或者个人管理员id
    unsigned int unit_id;
    uint32_t node_landline_expire;
    uint32_t node_account_expire;
    char account_uuid[64];
    char user_info_uuid[64];
	PersonalAccountPhoneInfo_T()
	{
		memset(this, 0, sizeof(*this));
	}
}PersonalPhoneInfo;

// EmailInfo 结构体定义
typedef struct EmailInfo_T
{
    int role;
    char username[128];// 数据库字段长度
    char community[128];
    char oem_name[64];

    EmailInfo_T() 
    {
        memset(this, 0, sizeof(*this));
    }
}EmailInfo;



namespace dbinterface
{

class ResidentPersonalAccount : private boost::noncopyable
{
public:
    ResidentPersonalAccount();
    ~ResidentPersonalAccount();

    enum SwitchType
    {
        ALLOW_PIN = 0,
        FEATURE_PLAN = 1,
    };
    
    enum OfficeAccountFlags
    {
        EnableCall = 0,
    };
    
    static int GetUidAccount(const std::string &uid, ResidentPerAccount& account);
    static int GetUserAccount(const std::string &struser, ResidentPerAccount& account);
    static int GetUserAccountFromMaster(const std::string &struser, ResidentPerAccount& account);
    static int GetPhoneAccount(const std::string &phone, ResidentPerAccount& account, PhoneUserType user_type);
    static int GetEmailAccount(const std::string &email, ResidentPerAccount& account);
    static int GetAttendantListByUid(const std::string &uid, std::set<std::string>& uids);
    static int GetAccountByID(int id, ResidentPerAccount& account);
    static int GetUUIDAccount(const std::string &uuid, ResidentPerAccount& account);
    static int GetRoomIDAccount(int room_id, ResidentPerAccount& account);
    static int GetAccountListByUserInfoUUID(const std::string &userinfo_uuid, ResidentPerAccountList& account);
    static bool TestFlag(ResidentPerAccount& account, int flag);
    //新增account与uuid互转的函数，常用，没必要每次都去申请整个结构体内存
    static int GetUUIDByAccount(const std::string &account, std::string& uuid);
    static int GetAccountByUUID(const std::string& uuid, std::string& account);
    static int CheckAccountIsExpire(const std::string& account);
    static int GetExpireTimeByUUID(const std::string &account, std::string& expire_time, int type);
    static int GetExpireTime(std::string &expire_time, ResidentPerAccount& account);
    static int GetPhoneExpireTime(std::string &phone_expire_time, ResidentPerAccount& account);
	static int UpdateAppLoginStatus(const std::string& user);
    static int GetAppPayMode(const std::string& uid);
    static int GetAppPushMode(const std::string& uid, int role);
    static bool CheckJpRedirect(const std::string &account);
	static int GetPhoneInfoList(const std::string& phone, std::vector<PersonalPhoneInfo> &phone_info_list);
    static int GetPhoneInfoByMngID(const std::string& phone, unsigned int mng_id, PersonalPhoneInfo &phone_info);
    static int GetPhoneInfoByNode(const std::string& phone, const std::string &node, PersonalPhoneInfo &phone_info);
    static std::string GetNFcNameForPerDevNFC(const std::string& node, const std::string& code);
    static int GetFaceInfoByNodeAndName(const std::string &node, const std::string &name, PersonalFaceKeyInfo& key_info);
    static int GetNfcInfoByNodeAndCode(const std::string& node, const std::string& code, PersonalNfcKeyInfo &key_info);
    static std::string GetNFCNameAndNodeForUnitPubDev(int unitID, const std::string& code, std::string& node);
    static std::string GetNFCNameAndNodeForPubDev(int mngID, const std::string& code, std::string& node);
    static std::string GetNodeTimeZoneStr(const std::string& node);
    static int GetNodeActiveByPhone(const std::string& phone, const std::string& caller);
    static std::string GetPhoneBySip(const std::string& sip, int type, std::string &phone_code);
    static int GetPersoanlAttendantListByUid(const std::string &node, int role, ResidentPerAccountList& account_list);
    static int GetNodeUidListByNode(const std::string &node, std::vector<std::string>& uid_list);
    static void GetNodeAccountsStrByNode(const std::string &node, std::string& accounts_str);
    static int GetAttendantUserInfoListByParentUUID(const std::string& parent_uuid, ResidentPerAccountList& attendant_list);
    static int GetCommPubMainList(int mng_id, ResidentPerAccountList& account_list);
    static int GetCommUnitMainList(int unit_id, ResidentPerAccountList& account_list);
    static int GetCommPmApplistByMngID(int mng_id, ResidentPerAccountList& account_list);
    static int InitAccountByUid(const std::string& uid, ResidentPerAccount &account);
    static std::string GetUserInfoUUIDByAccount(const std::string& account);
    static int GetNodesByUserInfoUUID(const std::string &userinfo_uuid, PersonalAccountNodeInfoMap& nodes);
    static void UpdateLanguageByUserInfoUUID(const std::string &language, const std::string &userinfo_uuid);
    static int GetMainSiteAccountByUserInfoUUID(const std::string &userinfo_uuid, ResidentPerAccount& account);
    static int GetUserInfoByAccount(const std::string &account, ResidentPerAccount& user_info);
    static int GetAttendantUserInfoListByNode(const std::string& node, ResidentPerAccountList& attendant_list);
    static int GetDevTimeZoneConfig(const short grade, const uint mng_id, const int flag ,const std::string& node, std::string& time_zone, int& time_format);
    static int GetNfcCodeByAccount(const std::string &account, std::string &nfccode);

    static int GetAlarmReminderStatusByAccount(const std::string& account, int &alarm_reminder_status);
    static int GetAccountListByPhoneList(const std::vector<std::string>& phone_list, ResidentPerAccountList& account_list);
    //获取项目下所有主账号account列表
    static int GetNodeListByProjectUUID(const std::string& project_uuid, std::set<std::string>& node_list);
    //获取当前账号对应时区时间
    static std::string GetAccountCurrentTimeString(const std::string& account, const std::map<std::string, AKCS_DST>& timezone_config);
    //获取帐号某一时间戳对应时区时间
    static std::string GetAccountTimeStringByTimestamp(const std::string& account, int64_t timestamp, const std::map<std::string, AKCS_DST>& timezone_config);
    static int GetNickNameAndNodeAndMngIDByUid(const std::string& uid, std::string& name, std::string& node, int& manager_id);
    static int GetNickNameAndNodeByUid(const std::string& uid, std::string& name, std::string& node, 
         std::string &db_delivery_uuid, std::string &projectuuid, int &mng_id);
    static std::string GetTimeZoneByUUID(const std::string& uuid);
    static int InitAccountById(int id, ResidentPerAccount &account);
    static int GetCommunityAccountByProjectID(uint32_t project_id, ResidentPerAccountMap &community_main_account_list, 
         ResidentPerAccountSlaveMap &community_slave_account_list, MapUnitCommunitAccountList &unit_main_map, ResidentUUIDPerAccountMap &community_uuid_account_list);
    static int UpdateNodeTimeZoneByUUID(const std::string& account, const std::string& timezone);
    static int GetAttendantListUUIDByUid(const std::string &node, std::vector<ResidentPerAccount>& accounts);
    static int GetNodeByAccount(const std::string& account, std::string& node);
    
    // 获取住宅用户邮件信息的方法
    static void GetEmailInfoByAccount(EmailInfo& email_info, const std::string& user);
    static int GetNodeUUIDByAccount(const std::string& account, std::string& node_uuid);
    static bool IsCommunityAptEnableSmartHome(const ResidentPerAccount& account);
private:
    static void GetAccountFromSql(ResidentPerAccount &account, CRldbQuery& query);
    static int InitAccountByEmail(const std::string& email, ResidentPerAccount &account);
    static int InitAccountByUser(const std::string& user, ResidentPerAccount &account);
    static int InitAccountByUidFromMasterDB(const std::string& uid, ResidentPerAccount &account);
    static int InitAccountFromMasterByUser(const std::string& user, ResidentPerAccount &account);
    static int InitAccountByRoomID(int room_id, ResidentPerAccount &account);
    static int InitAccountByUuid(const std::string& uuid, ResidentPerAccount &account);
    static int InitAccountByPhone(const std::string& phone_number, ResidentPerAccount &account, PhoneUserType user_type);
    static void InitUserInfo(ResidentPerAccount& account);
};

}
#endif
