#ifndef __PARSE_REPORT_ZIGBEE_DETAILS_HPP__
#define __PARSE_REPORT_ZIGBEE_DETAILS_HPP__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "DclientMsgSt.h"
#include "dbinterface/IndoorMonitorZigbeeDevice.h"

namespace akcs_msgparse
{

/*
<Msg>
<Type>ReportZigbeeDetails</Type>
<Params>
<Device>
    <Name>Living Room Light</Name>
    <DeviceType>0</DeviceType> // 0:灯控; 1:温控; 2:窗帘
    <ZigbeeDeviceID>1</ZigbeeDeviceID>
    <Version>1.0.1</Version>
    <OnlineStatus>1</OnlineStatus> // 0:离线，1:在线
    <Switch>1</Switch> // 开关状态，对于灯以及温控：0:开启，1:关闭。对于窗帘，0:窗帘开启中，1:窗帘关闭中，2：暂停
    <!-- 以下字段根据设备类型选择性使用 -->
    <HoldDelayTime>1</HoldDelayTime> // 灯控延迟关闭时间
    <ThermostatsMode>0</ThermostatsMode> //温控模式：0:仅加热,1:仅制冷,2:加热+制冷
    <Temperature>25.5</Temperature> //温控:当前温度
    <TargetTemperature>26</TargetTemperature> //温控:目标温度
    <MinTemperature>10.0</MinTemperature>  //温控:温控可调节最低温度
    <MaxTemperature>35</MaxTemperature>  //温控:温控可调节最高温度
    <TemperatureUnit>0</TemperatureUnit> //0=摄氏度(°C)，1=华氏度(°F)
    <HVACMode>0</HVACMode> //0:ThermostatsMode=0时，HVACMode=0，ThermostatsMode=1时，HVACMode=1，ThermostatsMode=2时，zigbee设备：0:Heating,1:Cooling
</Device>
<Device>......</Device>
</Params>
</Msg>
*/

static int ParseReportZigbeeDetailsMsg(char* msg_body, ZigbeeDeviceDetailsList& zigbee_device_details)
{
    if (msg_body == nullptr)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(msg_body, text, 4096);
    AK_LOG_INFO << "ParseReportZigbeeDetailsMsg text: \n" << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(msg_body))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (nullptr == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    // 主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "Mismatched " << XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* params_node = root_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM);
    if (nullptr == params_node)
    {
        AK_LOG_WARN << "Params Node is NULL";
        return -1;
    }

    TiXmlElement* device_node = nullptr;
    for (device_node = params_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM_DEVICE); device_node; device_node = device_node->NextSiblingElement(XML_NODE_NAME_MSG_PARAM_DEVICE))
    {

        SOCKET_MSG_ZIGBEE_DEVICE_DETAILS zigbee_device_detail;
        memset(&zigbee_device_detail, 0, sizeof(zigbee_device_detail));
        
        TiXmlElement* item_node = nullptr;
        for (item_node = device_node->FirstChildElement(); item_node; item_node = item_node->NextSiblingElement())
        {
            if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_NAME) == 0)
            {
                if (item_node->GetText())
                {
                    TransUtf8ToTchar(item_node->GetText(), zigbee_device_detail.name, sizeof(zigbee_device_detail.name) / sizeof(TCHAR));
                }
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_ZIGBEE_DEVICE_ID) == 0)
            {
                if (item_node->GetText())
                {
                    TransUtf8ToTchar(item_node->GetText(), zigbee_device_detail.zigbee_device_id, sizeof(zigbee_device_detail.zigbee_device_id) / sizeof(TCHAR));
                }
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_COMMON_VERSION) == 0)
            {
                if (item_node->GetText())
                {
                    TransUtf8ToTchar(item_node->GetText(), zigbee_device_detail.version, sizeof(zigbee_device_detail.version) / sizeof(TCHAR));
                }
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_DEVICETYPE) == 0)
            {
                if (item_node->GetText())
                {
                    zigbee_device_detail.device_type = ATOI(item_node->GetText());
                }
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_ONLINE_STATUS) == 0)
            {
                if (item_node->GetText())
                {
                    zigbee_device_detail.online_status = ATOI(item_node->GetText());
                }
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_SWITCH) == 0)
            {
                if (item_node->GetText())
                {
                    zigbee_device_detail.switch_status = ATOI(item_node->GetText());
                }
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_HOLD_DELAY_TIME) == 0)
            {
                if (item_node->GetText())
                {
                    zigbee_device_detail.hold_delay_time = ATOI(item_node->GetText());
                }
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_THERMOSTATS_MODE) == 0)
            {
                if (item_node->GetText())
                {
                    zigbee_device_detail.thermostats_mode = ATOI(item_node->GetText());
                }
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_TEMPERATURE) == 0)
            {
                if (item_node->GetText())
                {
                    TransUtf8ToTchar(item_node->GetText(), zigbee_device_detail.temperature, sizeof(zigbee_device_detail.temperature) / sizeof(TCHAR));
                }
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_TARGET_TEMPERATURE) == 0)
            {
                if (item_node->GetText())
                {
                    TransUtf8ToTchar(item_node->GetText(), zigbee_device_detail.target_temperature, sizeof(zigbee_device_detail.target_temperature) / sizeof(TCHAR));
                }
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_HVAC_MODE) == 0)
            {
                if (item_node->GetText())
                {
                    zigbee_device_detail.hvac_mode = ATOI(item_node->GetText());
                }
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_MIN_TEMPERATURE) == 0)
            {
                if (item_node->GetText())
                {
                    TransUtf8ToTchar(item_node->GetText(), zigbee_device_detail.min_temperature, sizeof(zigbee_device_detail.min_temperature) / sizeof(TCHAR));
                }
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_MAX_TEMPERATURE) == 0)
            {
                if (item_node->GetText())
                {
                    TransUtf8ToTchar(item_node->GetText(), zigbee_device_detail.max_temperature, sizeof(zigbee_device_detail.max_temperature) / sizeof(TCHAR)); 
                }
            }
            else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_TEMPERATURE_UNIT) == 0)
            {
                if (item_node->GetText())
                {
                    zigbee_device_detail.temperature_unit = ATOI(item_node->GetText());
                }
            }
        }
        zigbee_device_details.push_back(zigbee_device_detail);
    }

    return 0;
}

}

#endif // __PARSE_REPORT_ZIGBEE_DETAILS_HPP__ 