#ifndef __DB_AJAX_DEVICE_INFO_H__
#define __DB_AJAX_DEVICE_INFO_H__

#include <memory>
#include <string>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "AkcsCommonDef.h"

typedef struct AjaxDeviceInfo_T
{
    char dev_uuid[36];
    char hub_id[16];
    int login_status; // 0: 未登录, 1: 已登录

    AjaxDeviceInfo_T()
    {
        memset(this, 0, sizeof(*this));
    }
} AjaxDeviceInfoSt;

typedef std::vector<AjaxDeviceInfoSt> AjaxDeviceInfoList;

namespace dbinterface
{

class AjaxDeviceInfo
{
public:
    static int GetLoginAjaxDeviceInfoByHubID(const std::string& hub_id, AjaxDeviceInfoList& device_info_list);
    static int InsertOrUpdateAjaxDeviceInfo(const std::string& dev_uuid, const std::string& hub_id, AjaxLoginStatus login_status, const std::string& uuid);

private:
    static void GetAjaxDeviceInfoFromSql(CRldbQuery& query, AjaxDeviceInfoSt& device_info);
};


}

#endif
