#ifndef __PARSE_REPORT_ZIGBEE_STATUS_CHANGE_MSG_H__
#define __PARSE_REPORT_ZIGBEE_STATUS_CHANGE_MSG_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "DclientMsgSt.h"

namespace akcs_msgparse
{
/*
<Msg>
<Type>ZigbeeStatusChange</Type>
<Params>
    <TraceID>0123233000</TraceID>
    <ZigbeeDeviceID>1</ZigbeeDeviceID>
    <DeviceType>0</DeviceType>
    <Switch>0</Switch>
    <Temperature>24.8</Temperature>
    <TargetTemperature>26</TargetTemperature>
    <HVACMode>0</HVACMode>
</Params>
</Msg>
*/

static int ParseReportZigbeeStatusChangeMsg(char *buf, SOCKET_MSG_DEVICE_REPORT_ZIGBEE_STATUS_CHANGE &status_change)
{
    if (buf == nullptr)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportZigbeeStatusChangeMsg text: \n" << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (nullptr == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    // 主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "Mismatched " << XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* params_node = root_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM);
    if (nullptr == params_node)
    {
        AK_LOG_WARN << "Params Node is NULL";
        return -1;
    }

    TiXmlElement* item_node = nullptr;
    for (item_node = params_node->FirstChildElement(); item_node; item_node = item_node->NextSiblingElement())
    {
        if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_TRACE_ID) == 0)
        {
            if (item_node->GetText())
            {
                TransUtf8ToTchar(item_node->GetText(), status_change.trace_id, sizeof(status_change.trace_id) / sizeof(TCHAR));
            }
        }
        else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_ZIGBEE_DEVICE_ID) == 0)
        {
            if (item_node->GetText())
            {
                TransUtf8ToTchar(item_node->GetText(), status_change.zigbee_device_id, sizeof(status_change.zigbee_device_id) / sizeof(TCHAR));
            }
        }
        else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_DEVICETYPE) == 0)
        {
            if (item_node->GetText())
            {
                status_change.device_type = ATOI(item_node->GetText());
            }
        }
        else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_SWITCH) == 0)
        {
            if (item_node->GetText())
            {
                status_change.switch_status = ATOI(item_node->GetText());
            }
        }
        else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_TEMPERATURE) == 0)
        {
            if (item_node->GetText())
            {
                TransUtf8ToTchar(item_node->GetText(), status_change.temperature, sizeof(status_change.temperature) / sizeof(TCHAR));
            }
        }
        else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_TARGET_TEMPERATURE) == 0)
        {
            if (item_node->GetText())
            {
                TransUtf8ToTchar(item_node->GetText(), status_change.target_temperature, sizeof(status_change.target_temperature) / sizeof(TCHAR));
            }
        }
        else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_HVAC_MODE) == 0)
        {
            if (item_node->GetText())
            {
                status_change.hvac_mode = ATOI(item_node->GetText());
            }
        }
    }
    return 0;
}

}

#endif 