#ifndef __DB_SMART_LOCK_PRIVATE_KEY_H__
#define __DB_SMART_LOCK_PRIVATE_KEY_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

enum SmartLockPrivateKeyCreatorType
{
    USER = 1,
    PM = 2,
};

typedef struct SmartLockPrivateKeyInfo_T
{
    char uuid[36];
    char smart_lock_uuid[36];
    char pin[10];
    int status;
    char personal_account_uuid[36];
    char comm_per_private_key_uuid[36];
    char personal_private_key_uuid[36];
    int credential_id;
    int lock_report_status;

    SmartLockPrivateKeyInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} SmartLockPrivateKeyInfo;

namespace dbinterface {

class SmartLockPrivateKey
{
public:
    static int GetSmartLockPrivateKeyByCommPerPrivateKeyUUID(const std::string& comm_per_private_key_uuid, SmartLockPrivateKeyInfo& smart_lock_private_key_info);
    static int GetSmartLockPrivateKeyByPersonalPrivateKeyUUID(const std::string& personal_private_key_uuid, SmartLockPrivateKeyInfo& smart_lock_private_key_info);
    static int GetSmartLockPrivateKeyBySmartLockAndCredentialId(const std::string& smart_lock_uuid, int credential_id, SmartLockPrivateKeyInfo& smart_lock_private_key_info);
    static int DeleteSmartLockRelatedPrivateKey(const std::string& smart_lock_uuid);
    static int DeleteSmartLockCommPerPrivateKey(const std::string& comm_per_private_key_uuid);
    static int DeleteSmartLockPersonalPrivateKey(const std::string& personal_private_key_uuid);
    static int InsertOrUpdateSmartLockPrivateKey(const SmartLockPrivateKeyInfo& smartlock_private_key_info);
    static int UpdateSmartLockPrivateKeyStatus(const SmartLockPrivateKeyInfo& smartlock_private_key_info);
    static int DeleteCommunitySmartLockPrivateKeyByPinUUIDListStr(const std::string& pin_uuid_list_str);
    static int DeletePersonalSmartLockPrivateKeyByPinUUIDAndSmartLockUUIDNotInList(const std::string& pin_uuid, const std::string& smart_lock_uuid_not_in_list_str);
    static int DeletePersonalSmartLockPrivateKeyBySmartLockUUIDAndPinUUID(const std::string& smart_lock_uuid, const std::string& pin_uuid);

private:
    SmartLockPrivateKey() = delete;
    ~SmartLockPrivateKey() = delete;
    static void GetSmartLockPrivateKeyFromSql(SmartLockPrivateKeyInfo& smart_lock_private_key_info, CRldbQuery& query);
};

}
#endif