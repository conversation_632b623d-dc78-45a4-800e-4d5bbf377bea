#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "IndoorMonitorZigbeeDevice.h"
#include "UUID.h"
#include "dbinterface/SystemSettingTable.h"

namespace dbinterface {

static const std::string indoor_monitor_zigbee_device_info_sec = " UUID,DeviceUUID,ZigbeeDeviceID,Name,Version,DeviceType,OnlineStatus,Status,HoldDelay,ThermostatsMode,Temperature,TargetTemperature,MinTemperature,MaxTemperature,TemperatureUnit,HVACMode ";

void IndoorMonitorZigbeeDevice::GetIndoorMonitorZigbeeDeviceFromSql(IndoorMonitorZigbeeDeviceInfo& indoor_monitor_zigbee_device_info, CRldbQuery& query)
{
    Snprintf(indoor_monitor_zigbee_device_info.uuid, sizeof(indoor_monitor_zigbee_device_info.uuid), query.GetRowData(0));
    Snprintf(indoor_monitor_zigbee_device_info.device_uuid, sizeof(indoor_monitor_zigbee_device_info.device_uuid), query.GetRowData(1));
    Snprintf(indoor_monitor_zigbee_device_info.zigbee_device_id, sizeof(indoor_monitor_zigbee_device_info.zigbee_device_id), query.GetRowData(2));
    Snprintf(indoor_monitor_zigbee_device_info.name, sizeof(indoor_monitor_zigbee_device_info.name), query.GetRowData(3));
    Snprintf(indoor_monitor_zigbee_device_info.version, sizeof(indoor_monitor_zigbee_device_info.version), query.GetRowData(4));
    indoor_monitor_zigbee_device_info.device_type = ATOI(query.GetRowData(5));
    indoor_monitor_zigbee_device_info.online_status = ATOI(query.GetRowData(6));
    indoor_monitor_zigbee_device_info.switch_status = ATOI(query.GetRowData(7));
    indoor_monitor_zigbee_device_info.hold_delay_time = ATOI(query.GetRowData(8));
    indoor_monitor_zigbee_device_info.thermostats_mode = ATOI(query.GetRowData(9));
    Snprintf(indoor_monitor_zigbee_device_info.temperature, sizeof(indoor_monitor_zigbee_device_info.temperature), query.GetRowData(10));
    Snprintf(indoor_monitor_zigbee_device_info.target_temperature, sizeof(indoor_monitor_zigbee_device_info.target_temperature), query.GetRowData(11));
    Snprintf(indoor_monitor_zigbee_device_info.min_temperature, sizeof(indoor_monitor_zigbee_device_info.min_temperature), query.GetRowData(12));
    Snprintf(indoor_monitor_zigbee_device_info.max_temperature, sizeof(indoor_monitor_zigbee_device_info.max_temperature), query.GetRowData(13));
    
    indoor_monitor_zigbee_device_info.temperature_unit = ATOI(query.GetRowData(14));
    indoor_monitor_zigbee_device_info.hvac_mode = ATOI(query.GetRowData(15));
    return;
}

int IndoorMonitorZigbeeDevice::GetZigbeeDeviceInfoMap(const std::string& device_uuid, ZigbeeDeviceIDAndInfoMap& device_map)
{

    if (device_uuid.empty())
    {
        AK_LOG_WARN << "device uuid is empty";
        return -1;
    }

    std::stringstream sql;
    sql << "SELECT " << indoor_monitor_zigbee_device_info_sec << " FROM IndoorMonitorZigbeeDevice WHERE DeviceUUID = '" << device_uuid << "'";
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    
    CRldbQuery query(tmp_conn.get());
    query.Query(sql.str());
    
    while (query.MoveToNextRow())
    {
        IndoorMonitorZigbeeDeviceInfo device_info;
        GetIndoorMonitorZigbeeDeviceFromSql(device_info, query);
        device_map[device_info.zigbee_device_id] = device_info;
    }
    return 0;
}

int IndoorMonitorZigbeeDevice::DeleteZigbeeDevices(const std::string& device_uuid, const AkcsStringList& zigbee_device_ids)
{
    if (device_uuid.empty() || zigbee_device_ids.empty())
    {
        AK_LOG_WARN << "device uuid or zigbee device ids is empty";
        return -1;
    }

    std::string device_ids_in_clause = ListToSeparatedFormatString(zigbee_device_ids);
    std::stringstream sql;
    sql << "DELETE FROM IndoorMonitorZigbeeDevice WHERE DeviceUUID = '" << device_uuid << "' AND ZigbeeDeviceID IN (" << device_ids_in_clause << ")";
    
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    
    CRldbQuery query(tmp_conn.get());
    query.Query(sql.str());
    
    return 0;
}

int IndoorMonitorZigbeeDevice::UpdateAndInsertZigbeeDeviceDetails(const std::string& device_uuid, const ZigbeeDeviceDetailsList& zigbee_device_details)
{
    if (device_uuid.empty() || zigbee_device_details.empty())
    {
        AK_LOG_WARN << "device uuid is empty or zigbee device details is empty";
        return -1;
    }
    std::string server_tag = SystemSetting::GetServerTag();
    
    for (const auto& zigbee_device_detail : zigbee_device_details)
    {
        // 生成UUID
        std::string uuid;
        UUID::GenerateUUID(server_tag, uuid);
           
        // 准备字符串字段数据
        AkcsStringMap insert_str_datas;
        insert_str_datas.emplace("UUID", uuid);
        insert_str_datas.emplace("DeviceUUID", device_uuid);
        insert_str_datas.emplace("ZigbeeDeviceID", std::string(zigbee_device_detail.zigbee_device_id));
        insert_str_datas.emplace("Name", std::string(zigbee_device_detail.name));
        insert_str_datas.emplace("Version", std::string(zigbee_device_detail.version));
        
        insert_str_datas.emplace("Temperature", std::string(zigbee_device_detail.temperature));
        insert_str_datas.emplace("TargetTemperature", std::string(zigbee_device_detail.target_temperature));
        insert_str_datas.emplace("MinTemperature", std::string(zigbee_device_detail.min_temperature));
        insert_str_datas.emplace("MaxTemperature", std::string(zigbee_device_detail.max_temperature));
        
        // 准备整型字段数据
        AkcsStringIntMap insert_int_datas;
        insert_int_datas.emplace("DeviceType", zigbee_device_detail.device_type);
        insert_int_datas.emplace("OnlineStatus", zigbee_device_detail.online_status);
        insert_int_datas.emplace("Status", zigbee_device_detail.switch_status);
        insert_int_datas.emplace("HoldDelay", zigbee_device_detail.hold_delay_time);
        insert_int_datas.emplace("ThermostatsMode", zigbee_device_detail.thermostats_mode);
        insert_int_datas.emplace("TemperatureUnit", zigbee_device_detail.temperature_unit);
        insert_int_datas.emplace("HVACMode", zigbee_device_detail.hvac_mode);
        
        // 准备更新数据
        AkcsStringMap update_str_datas;
        update_str_datas.emplace("Name", std::string(zigbee_device_detail.name));
        update_str_datas.emplace("Version", std::string(zigbee_device_detail.version));
        update_str_datas.emplace("Temperature", std::string(zigbee_device_detail.temperature));
        update_str_datas.emplace("TargetTemperature", std::string(zigbee_device_detail.target_temperature));
        update_str_datas.emplace("MinTemperature", std::string(zigbee_device_detail.min_temperature));
        update_str_datas.emplace("MaxTemperature", std::string(zigbee_device_detail.max_temperature));
        
        AkcsStringIntMap update_int_datas;
        update_int_datas.emplace("DeviceType", zigbee_device_detail.device_type);
        update_int_datas.emplace("OnlineStatus", zigbee_device_detail.online_status);
        update_int_datas.emplace("Status", zigbee_device_detail.switch_status);
        update_int_datas.emplace("HoldDelay", zigbee_device_detail.hold_delay_time);
        update_int_datas.emplace("ThermostatsMode", zigbee_device_detail.thermostats_mode);
        update_int_datas.emplace("TemperatureUnit", zigbee_device_detail.temperature_unit);
        update_int_datas.emplace("HVACMode", zigbee_device_detail.hvac_mode);
        
        // 执行数据库插入或更新操作
        GET_DB_CONN_ERR_RETURN(db_conn, -1);
        CRldb* conn = db_conn.get();
        std::string table_name = "IndoorMonitorZigbeeDevice";
        int ret = conn->InsertOrUpdateData(table_name, insert_str_datas, insert_int_datas, update_str_datas, update_int_datas);
        
        if (ret < 0) 
        {
            AK_LOG_WARN << "Failed to insert or update zigbee device details. device_id:" << zigbee_device_detail.zigbee_device_id;
            return -1;
        }
    }
    
    return 0;
}

int IndoorMonitorZigbeeDevice::UpdateZigbeeDeviceStatus(const std::string& device_uuid, const ZigbeeDeviceInfo& device_info)
{
    if (device_uuid.empty() || strlen(device_info.zigbee_device_id) == 0)
    {
        AK_LOG_WARN << "device uuid or zigbee device id is empty";
        return -1;
    }

    // 准备更新数据
    AkcsStringMap str_datas;
    AkcsStringIntMap int_datas;
    
    str_datas.emplace("Temperature", std::string(device_info.temperature));
    str_datas.emplace("TargetTemperature", std::string(device_info.target_temperature));
    
    // 其他字段作为整数处理
    int_datas.emplace("DeviceType", device_info.device_type);
    int_datas.emplace("Status", device_info.switch_status);
    int_datas.emplace("HVACMode", device_info.hvac_mode);
    
    // 构建WHERE条件
    std::stringstream where_clause;
    where_clause << "DeviceUUID = '" << device_uuid << "' AND ZigbeeDeviceID = '" << device_info.zigbee_device_id << "'";
    
    // 调用通用更新方法
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldb* conn = db_conn.get();
    int ret = conn->UpdateData("IndoorMonitorZigbeeDevice", where_clause.str(), str_datas, int_datas);
    
    if (ret < 0)
    {
        AK_LOG_WARN << "update zigbee device status failed. device_uuid:" << device_uuid
                    << ", zigbee_device_id:" << device_info.zigbee_device_id;
        return -1;
    }
    return 0;
}

}