#include "AjaxKafkaMessageProducer.h"

AjaxNotifyMsg g_ajax_kafka_notify_msg;

void AjaxNotifyMsg::ProduceMsg(const std::string &key, const std::string& msg)
{    
    kafka_producer_->ProduceMsgWithLock(key, msg);
}

void AjaxNotifyMsg::InitKafkaProducer(const std::string &ip, const std::string &topic)
{
    kafka_producer_ = std::make_shared<AkcsKafkaProducer>(topic, ip);
}

void ProduceAjaxNotifyMsg(const std::string &key, const std::string& msg)
{
    g_ajax_kafka_notify_msg.ProduceMsg(key, msg);
}
