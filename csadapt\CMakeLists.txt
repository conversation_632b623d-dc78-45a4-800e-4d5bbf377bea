CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (adapt C CXX)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR}/src")
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(DBINTERFACE_FILES_OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR})
include(${CMAKE_CURRENT_SOURCE_DIR}/../csbase/common_scripts/dbinterface_files_list.cmake)

SET(INC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)
SET(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)
LINK_DIRECTORIES(${CSBASE_SOURCE_DIR} ${CSBASE_SOURCE_DIR}/thirdlib ${CSBASE_SOURCE_DIR}/redis/hiredis ${CSBASE_SOURCE_DIR}/evpp/lib)

AUX_SOURCE_DIRECTORY(${SRC_DIR} SRC_LIST_ADAPT)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/Basic SRC_LIST_ADAPT_BASIC)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/Cstring SRC_LIST_ADAPT_CSTRING)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Control SRC_LIST_ADAPT_CONTROL)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Control/NotifyMessageControl SRC_LIST_ADAPT_CONTROL_NM_CTL)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Model SRC_LIST_ADAPT_MODEL)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Model/Maintenance SRC_LIST_ADAPT_MODEL_MAINTENANCE)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Office SRC_LIST_ADAPT_OFFICE)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/OfficeNew SRC_LIST_ADAPT_OFFICE_NEW)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/OEM/Azer SRC_LIST_ADAPT_OEM_AZERBAIJAN)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/jsoncpp0.5/src/json SRC_LIST_BASE_JSON)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/protobuf SRC_LIST_BASE_PROTO)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/beanstalk-client SRC_LIST_BASE_BEANSTALK)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/encrypt SRC_LIST_BASE_ENCRYPT)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/zipkin SRC_LIST_BASE_ZIPKIN)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/kafka SRC_LIST_BASE_KAFKA)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/metrics SRC_LIST_BASE_METRICS)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/http SRC_LIST_BASE_HTTP)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Tinyxml SRC_LIST_BASE_TINYXML)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/SL50/DownMessage SRC_LIST_BASE_SL50_DOWNMESSAGE)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/loop SRC_LIST_BASE_LOOP)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/consistent-hash SRC_LIST_BASE_HASH)

SET(BASE_LIST_INC ${CSBASE_SOURCE_DIR} ${CSBASE_SOURCE_DIR}/mysql ${CSBASE_SOURCE_DIR}/evpp
                  ${CSBASE_SOURCE_DIR}/etcd ${CSBASE_SOURCE_DIR}/Rldb ${CSBASE_SOURCE_DIR}/encrypt ${CSBASE_SOURCE_DIR}/protobuf
                  ${CSBASE_SOURCE_DIR}/beanstalk-client ${CSBASE_SOURCE_DIR}/jsoncpp0.5/include ${CSBASE_SOURCE_DIR}/redis ${CSBASE_SOURCE_DIR}/redis/hiredis
                  ${CSBASE_SOURCE_DIR}/fdfs_client/fdfsclient ${CSBASE_SOURCE_DIR}/fdfs_client/libfdfscomm 
                  ${CSBASE_SOURCE_DIR}/grpc ${CSBASE_SOURCE_DIR}/grpc/gens ${CSBASE_SOURCE_DIR}/Tinyxml)

ADD_DEFINITIONS( -std=gnu++11 -g2 -Werror -Wno-unused-parameter -Wno-deprecated)

include_directories(${BASE_LIST_INC} ${INC_DIR}/mysql ${SRC_DIR}/Common/Basic ${SRC_DIR}/Common/Cstring 
                     ${SRC_DIR}/Common/Socket ${SRC_DIR}/Model 
                    ${SRC_DIR}/Control ${SRC_DIR}/Control/NotifyMessageControl 
                    ${SRC_DIR}/Model/Maintenance ${SRC_DIR}/Office ${SRC_DIR} ${SRC_DIR}/include ${SRC_DIR}/OEM 
                    /usr/local/boost/include /usr/local/protobuf/include /usr/local/grpc/include ${SRC_DIR}/OfficeNew
                    ${CSBASE_SOURCE_DIR}/kafka ${CSBASE_SOURCE_DIR}/metrics)

add_executable(csadapt ${SRC_LIST_ADAPT} ${prefixed_file_list}
             ${SRC_LIST_ADAPT_BASIC} ${SRC_LIST_ADAPT_CSTRING} ${SRC_LIST_BASE_RLDB} ${SRC_LIST_BASE_TINYXML}
             ${SRC_LIST_ADAPT_CONTROL} ${SRC_LIST_ADAPT_MODEL} ${SRC_LIST_BASE_REDIS} ${SRC_LIST_BASE_JSON} ${SRC_LIST_BASE_ETCD} 
             ${SRC_LIST_BASE_PROTO} ${SRC_LIST_BASE_BEANSTALK} ${SRC_LIST_ADAPT_CONTROL_NM_CTL} 
             ${SRC_LIST_BASE_ENCRYPT} ${SRC_LIST_ADAPT_MODEL_MAINTENANCE} 
             ${SRC_LIST_ADAPT_OFFICE}  ${SRC_LIST_BASE_METRICS} ${SRC_LIST_BASE_HTTP} 
             ${SRC_LIST_BASE_ZIPKIN} ${SRC_LIST_BASE_KAFKA} ${SRC_LIST_ADAPT_OEM_AZERBAIJAN} 
             ${SRC_LIST_ADAPT_OFFICE_NEW} ${SRC_LIST_BASE_LOOP} 
             ${SRC_LIST_BASE_SL50_DOWNMESSAGE}
             ${SRC_LIST_BASE_HASH})

SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)

set_target_properties(csadapt PROPERTIES LINK_FLAGS  "-Wl,--rpath=/usr/local/akcs/csadapt/lib")

target_link_libraries(csadapt pthread mysqlclient iconv event glog ssl crypto evpp boost_system cpprest etcd-cpp-api
                    protobuf hiredis csbase fdfsclient fastcommon gpr grpc grpc++ cppkafka rdkafka rdkafka++ z dl)
