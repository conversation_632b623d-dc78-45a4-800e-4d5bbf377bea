#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/PersonalAccountCommunityInfo.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include <string.h>
#include "AkLogging.h"
#include "ConnectionManager.h"
#include "AkcsCommonDef.h"
#include "util.h"
namespace dbinterface
{

static const std::string apt_building_access_floor_info_sec = "IsAllBuilding,CommunityUnitUUID,AccessFloor ";

PersonalAccountCommunityInfo::PersonalAccountCommunityInfo()
{
    
}


std::string PersonalAccountCommunityInfo::GetFloorByUUID(const std::string& uuid)
{
    std::string floor;
    std::stringstream stream_sql;
    stream_sql << "select AccessFloor from PersonalAccountCommunityInfo where PersonalAccountUUID = '" << uuid << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, "") //获取数据库连接失败时，返回空字符串
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        floor = query.GetRowData(0);
    }

    return floor;
}

std::string PersonalAccountCommunityInfo::GetFloorByAccount(const std::string& account, const ResidentDev& open_door_dev)
{
    std::string floor;
    std::stringstream stream_sql;
    
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT == open_door_dev.grade)
    {
        // 对于楼栋设备，直接使用设备的 unit_uuid
        stream_sql << "select AccessFloor from PersonalAccountCommunityInfo P left join PersonalAccount A on P.PersonalAccountUUID = A.UUID "
                   << "where A.Account = '" << account << "' and (P.IsAllBuilding = 1 or P.CommunityUnitUUID = '" << open_door_dev.unit_uuid << "')";
    }
    else if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == open_door_dev.grade)
    {
        // 对于公共设备，使用 ResidentPersonalAccount 获取用户信息
        ResidentPerAccount user_account;
        if (dbinterface::ResidentPersonalAccount::GetUidAccount(account, user_account) == 0 && strlen(user_account.unit_uuid) > 0)
        {
            // 使用获取到的 unit_uuid 进行楼层权限查询
            stream_sql << "select AccessFloor from PersonalAccountCommunityInfo P left join PersonalAccount A on P.PersonalAccountUUID = A.UUID "
                       << "where A.Account = '" << account << "' and (P.IsAllBuilding = 1 or P.CommunityUnitUUID = '" << user_account.unit_uuid << "')";
        }
    }

    GET_DB_CONN_ERR_RETURN(tmp_conn, "") //获取数据库连接失败时，返回空字符串
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        floor = query.GetRowData(0);
    }
    return floor;
}

std::string PersonalAccountCommunityInfo::GetAptBuildingAccessFloorInfoListByUUID(const std::string& user_uuid, const std::string& user_unit_uuid, std::string &apt_floor, const DEVICE_SETTING &dev)
{   
    std::stringstream stream_sql;
    stream_sql << "select " << apt_building_access_floor_info_sec << " from PersonalAccountCommunityInfo where PersonalAccountUUID = '" << user_uuid << "'";
    GET_DB_CONN_ERR_RETURN(tmp_conn, "") //获取数据库连接失败时，返回空字符串
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    // 默认楼层
    std::string floor = kDefaultFloor;

    // 处理楼栋设备或公共设备逻辑
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT == dev.grade)
    {
        //楼栋公共设备，如果是和user再同一个楼栋，则至少拥有apt_floor的权限
        if(dev.community_unit_uuid == user_unit_uuid)
        {
            floor = apt_floor;
        }
        while (query.MoveToNextRow())
        {
            int selected_is_all_building = ATOI(query.GetRowData(0));
            std::string selected_community_unit_uuid = query.GetRowData(1);
            std::string selected_building_floor = query.GetRowData(2);
            if (selected_is_all_building)
            {
                floor = GetAccessibleFloor(apt_floor, selected_building_floor);
                break;
            } 
            else if (selected_community_unit_uuid == dev.community_unit_uuid) 
            {
                
                // 若非勾选全部楼栋，则取房间所在楼栋的楼层权限+房间的楼层权限
                if (user_unit_uuid == dev.community_unit_uuid) 
                {
                    floor = GetAccessibleFloor(apt_floor, selected_building_floor);
                } 
                else 
                {
                    floor = selected_building_floor;
                }
                break;
            }
        }
    } 
    else if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == dev.grade) 
    {
        //最外围公共设备至少拥有apt_floor的权限
        floor = apt_floor;
        while (query.MoveToNextRow())
        {
            int selected_is_all_building = ATOI(query.GetRowData(0));
            std::string selected_community_unit_uuid = query.GetRowData(1);
            std::string selected_building_floor = query.GetRowData(2);

            // 公共设备LiftFloorNum：房间楼层+房间所在楼栋的可达楼层。
     
            if (selected_is_all_building || selected_community_unit_uuid == user_unit_uuid)
            {
                floor = GetAccessibleFloor(apt_floor, selected_building_floor);
                break;
            }
        }
    }



    return floor;
}



}
