#include "YunJiRobotClient.h"
#include <grpcpp/impl/codegen/status_code_enum.h> 

CompletionQueue g_yunji_robot_cq_;

void YunJiRobotRpcClient::MerchantCall(const std::string& project_uuid, const std::string& task_id, const std::string& via,
                        const std::string& target, int deposit_pin_code, int withdraw_pin_code)
{
    RobotMerchantCallRequest request;
    request.set_project_uuid(project_uuid);
    request.set_task_id(task_id);
    request.set_via(via);
    request.set_target(target);
    request.set_deposit_pin_code(deposit_pin_code);
    request.set_withdraw_pin_code(withdraw_pin_code);

    AsyncCsyunjirobotRpcClientCall* call = new AsyncCsyunjirobotRpcClientCall;
    call->s_type_ = CSYUNJIROBOT_RPC_SERVER_TYPE::MERCHANT_CALL;
    call->merchant_call_response_reader = stub_->PrepareAsyncMerchantCallHandle(&call->context, request, &g_yunji_robot_cq_);
    call->merchant_call_response_reader->StartCall();
    call->merchant_call_response_reader->Finish(&call->robot_merchant_call_reply_, &call->status, (void*)call);
    return;
}

void AsyncCompleteCsYunJiRobotRpc()
{
    void* got_tag;
    bool ok = false;

    // Block until the next result is available in the completion queue "cq".
    while (g_yunji_robot_cq_.Next(&got_tag, &ok)) {
        // The tag in this example is the memory location of the call object
        YunJiRobotRpcClient* call = static_cast<YunJiRobotRpcClient*>(got_tag); 

        // Verify that the request was completed successfully. Note that "ok"
        // corresponds solely to the request for updates introduced by Finish().
        GPR_ASSERT(ok);

        if (call->status.ok())
        {
            if(call->s_type_ == CSYUNJIROBOT_RPC_SERVER_TYPE::MERCHANT_CALL)
            {
                AK_LOG_INFO << "Async Call merchant call return, ret=" << call->robot_merchant_call_reply_.ret();
            }
        }
        else
        {
            AK_LOG_WARN << "RPC failed, please check rpc server";
        }
        delete call; 
    }
}



