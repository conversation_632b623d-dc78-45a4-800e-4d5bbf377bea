#ifndef __DB_THIRD_PARTY_LOCK_DEVICE_H__
#define __DB_THIRD_PARTY_LOCK_DEVICE_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>

typedef struct ThirdPartyLockDevice_T
{
    char personal_uuid[64];
    int lock_type;
    char lock_name[64];
    char dev_uuid[64];
    char mac[32];
    int relay;
    int auto_close;
    int lock_status;
}ThirdPartyLockDeviceInfo;

typedef std::vector<ThirdPartyLockDeviceInfo>ThirdPartyLockDevList;

enum ThirdPartyLockType
{
    QRIO,
    YALE,
    BSI,
    DORMAKABA,
    SL20,
    SALTO,
    ITEC,
    TT,
    SL50,
};
    

namespace dbinterface
{

class ThirdPartyLockDevice
{
public:
    ThirdPartyLockDevice();
    ~ThirdPartyLockDevice();
    static int GetThirdPartyLockDevlistByMac(const std::string &mac, ThirdPartyLockDevList &third_devlist);
    static int GetQrioRelayByMac(const std::string &mac);
    static int GetYaleRelayByMac(const std::string &mac);
private:
};

}
#endif
