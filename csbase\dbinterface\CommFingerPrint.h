#ifndef __DB_COMM_FINGER_H__
#define __DB_COMM_FINGRE_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>

typedef struct USER_FINGERPRINT_INFO_T
{
    char fingerprint[2048];
    char fingerprint_md5[33];
    char account[32];
    bool is_create_by_pm;

    USER_FINGERPRINT_INFO_T()
    {
        memset(this, 0, sizeof(*this));
    }

} UserFingerPrintInfo;

typedef std::list<UserFingerPrintInfo> UserFingerPrintList;
typedef std::map<std::string/*uuid*/, std::vector<UserFingerPrintInfo>> UsersFingerPrintMap;
typedef UsersFingerPrintMap::iterator UsersFingerPrintMapIter;


namespace dbinterface
{

class CommFingerPrint
{
public:
    CommFingerPrint();
    ~CommFingerPrint();
    static void GetAccountFingerPrintList(const std::string& accounts, UsersFingerPrintMap &map);
private:
};

}
#endif
