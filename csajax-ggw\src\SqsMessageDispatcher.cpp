#include "SqsMessageDispatcher.h"
#include "AkcsMsgDef.h"
#include "AkLogging.h"
#include "AjaxKafkaMessageProducer.h"
#include "ServiceConf.h"
#include "Singleton.h"
#include "gid/SnowFlakeGid.h"
#include "AjaxMsgParser.h"

extern SERVICE_CONF g_service_conf;

void SqsMessageDispatcher::Dispatch(const std::string& msg)
{
    AjaxMsgParser parser(msg);
    if (!parser.ParseOk())
    {
        AK_LOG_WARN << "ajax msg parse failed. msg=" << msg;
        return;
    }
    if (parser.GetHubID().empty())
    {
        AK_LOG_WARN << "ajax msg hub id is empty. msg=" << msg;
        return;
    }
    ProduceAjaxNotifyMsg(parser.GetHubID(), msg);
}
