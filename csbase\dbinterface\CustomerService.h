#ifndef __DB_CUSTOMER_SERVICE_H__
#define __DB_CUSTOMER_SERVICE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct CustomerServiceInfo_T
{
    char mng_account[64];
    char phone[32];
    char email[128];
    int receive_feedback;
    char email_for_rent_manager[255];
    CustomerServiceInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} CustomerServiceInfo;

namespace dbinterface {

class CustomerService
{
public:
    static int GetCustomerServiceByMngAccount(const std::string& mng_account, CustomerServiceInfo& customer_service_info);

private:
    CustomerService() = delete;
    ~CustomerService() = delete;
    static void GetCustomerServiceFromSql(CustomerServiceInfo& customer_service_info, CRldbQuery& query);
};

}
#endif