#ifndef __CSYUNJI_ROBOT_RPC_CLIENT_H__
#define __CSYUNJI_ROBOT_RPC_CLIENT_H__

#include <iostream>
#include <memory>
#include <string>

#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>
#include <thread>
#include <unistd.h>
#include <grpcpp/grpcpp.h>
#include "AK.YunJiRobot.grpc.pb.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"
#include "AkcsMonitor.h"

using grpc::Channel;
using grpc::ClientAsyncResponseReader;
using grpc::ClientContext;
using grpc::CompletionQueue;
using grpc::Status;

using AK::YunJiRobot::YunJiRobotRpcSrv;
using AK::YunJiRobot::GoodsItem;
using AK::YunJiRobot::RobotMerchantCallRequest;
using AK::YunJiRobot::RobotMerchantCallReply;

class YunJiRobotRpcClient;
typedef std::shared_ptr<YunJiRobotRpcClient> YunJiRobotRpcClientPtr;

class YunJiRobotRpcClient {
  public:
      public:
    explicit YunJiRobotRpcClient(const std::string &srv_net/*ip:port*/) {
      channel_ = grpc::CreateChannel(srv_net, grpc::InsecureChannelCredentials());
      stub_ = YunJiRobotRpcSrv::NewStub(channel_);
    }

    std::shared_ptr<Channel> channel_;

    // Assembles the client's payload and sends it to the server.
    int MerchanetCall(const std::string& project_uuid, const std::string& task_id, const std::string& via,
         const std::string& target, int deposit_pin_code, int withdraw_pin_code);

    // Out of the passed in Channel comes the stub, stored here, our view of the
    // server's exposed services.
    std::unique_ptr<YunJiRobotRpcSrv::Stub> stub_;
};

// The producer-consumer queue we use to communicate asynchronously with the
// Loop while listening for completed responses.
// Prints out the response from the server.
void AsyncCompleteCsYunJiRobotRpc();


// struct for keeping state and data information
struct AsyncCsyunjirobotRpcClientCall {
    
    CSYUNJIROBOT_RPC_SERVER_TYPE s_type_;

    // Container for the data we expect from the server.
    RobotMerchantCallReply robot_merchant_call_reply_;

    // Context for the client. It could be used to convey extra information to
    // the server and/or tweak certain RPC behaviors.
    ClientContext context;

    // Storage for the status of the RPC upon completion.
    Status status;

    //ClientAsyncResponseReader<HelloReply> 客户端异步响应读取对象
    std::unique_ptr<ClientAsyncResponseReader<RobotMerchantCallReply>> robot_merchant_call_response_reader_;
};



#endif
