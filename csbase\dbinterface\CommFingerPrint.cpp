#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/CommFingerPrint.h"
#include <string.h>
#include "AkLogging.h"
#include "ConnectionManager.h"
#include "util.h"

namespace dbinterface
{

CommFingerPrint::CommFingerPrint()
{

}

void CommFingerPrint::GetAccountFingerPrintList(const std::string& per_uuids, UsersFingerPrintMap &map)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();

    std::stringstream str_sql;
    str_sql << "select FingerPrint,FingerPrintMD5,PersonalAccountUUID From  CommFingerPrint where PersonalAccountUUID in(" << per_uuids << ");";

    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        std::string fingerprint = query.GetRowData(0);
        std::string fingerprint_md5 = query.GetRowData(1);
        std::string presonal_uuid = query.GetRowData(2);
        UsersFingerPrintMapIter iter = map.find(per_uuids);
        UserFingerPrintInfo info; // 创建指纹信息结构体
        strncpy(info.fingerprint, fingerprint.c_str(), sizeof(info.fingerprint)-1);
        strncpy(info.fingerprint_md5, fingerprint_md5.c_str(), sizeof(info.fingerprint_md5)-1);

        if (iter != map.end())
        {
            iter->second.push_back(info); // 存入结构体而非字符串
        }
        else
        {
            std::vector<UserFingerPrintInfo> list;
            list.push_back(info);
            map.insert(std::make_pair(presonal_uuid, list));
        }
    }

    ReleaseDBConn(conn);
    return;
}

}

