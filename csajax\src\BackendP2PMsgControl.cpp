#include "AkcsMsgDef.h"
#include "AkcsCommonDef.h"
#include "AkcsPduBase.h"
#include "json/json.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "BackendP2PMsgControl.h"
#include "ServiceConf.h"
#include "RouteMqProduce.h"

extern SERVICE_CONF g_service_conf;
extern RouteMQProduce* g_nsq_producer;

AK::BackendCommon::BackendP2PBaseMessage BackendP2PMsgControl::CreateP2PBaseMsg(int msgid, int type, 
   const std::string &uid, csmain::DeviceType conntype, int project_type)
{
    //设备的话 client_uid=uid
    std::string client_uid = uid;
    
    /*
    if(type == TO_APP_UID)
    {
        //多套房转换
        if(dbinterface::PersonalAccountUserInfo::GetMainAccountByAccount(uid, client_uid) != 0)
        {
            AK_LOG_WARN << "change to main site error, uid:" << uid;
        }
    }
    */
    
    AK::BackendCommon::BackendP2PBaseMessage msg;
    msg.set_type(type);
    msg.set_uid(client_uid);
    msg.set_msgid(msgid);
    msg.set_conn_type(conntype);
    msg.set_project_type(project_type);    
    return msg;
}

csmain::DeviceType BackendP2PMsgControl::DevProjectTypeToDevType(int projecttype)
{
    if(projecttype == project::PERSONAL)
    {
        return csmain::DeviceType::PERSONNAL_DEV;
    }
    else if (projecttype == project::RESIDENCE)
    {
        return csmain::DeviceType::COMMUNITY_DEV;
    }
    else if (projecttype == project::OFFICE)
    {
        return csmain::DeviceType::OFFICE_DEV;
    }
    else if (projecttype == project::OFFICE_NEW)
    {
        return csmain::DeviceType::OFFICE_DEV;
    }
    return csmain::DeviceType::COMMUNITY_NONE;
}

void BackendP2PMsgControl::PushMsg2Route(const google::protobuf::MessageLite* msg, int project_type)
{
    CAkcsPdu pdu;
    pdu.SetMsgBody(msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_BUSSNESS_P2P_MSG);
    pdu.SetSeqNum(0);
    pdu.SetProjectType(project_type);
    g_nsq_producer->OnPublish(pdu, g_service_conf.route_topic);    
}
