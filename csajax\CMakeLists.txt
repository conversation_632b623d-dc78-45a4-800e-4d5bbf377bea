CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (csajax  CXX)
SET(CSBASE_DIR ../csbase)

SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR}")
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(DBINTERFACE_FILES_OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR})
include(${CMAKE_CURRENT_SOURCE_DIR}/../csbase/common_scripts/dbinterface_files_list.cmake)

SET(DEPENDENT_LIBRARIES libcsbase.a pthread libevent.so libglog.so libmysqlclient.so libgpr.so libgrpc.so libgrpc++.so libevpp.so -lssl -lcrypto -lcpprest -letcd-cpp-api  -levpp -levent 
     -lboost_system -lprotobuf -lcurl libcppkafka.so librdkafka.so librdkafka++.so)
LINK_DIRECTORIES(${CSBASE_SOURCE_DIR} ${CSBASE_SOURCE_DIR}/thirdlib ${CSBASE_SOURCE_DIR}/redis/hiredis ${CSBASE_SOURCE_DIR}/thirdlib/oss ${CSBASE_SOURCE_DIR}/json-c /usr/local/lib ${CSBASE_SOURCE_DIR}/thirdlib/turbojpeg/lib)

AUX_SOURCE_DIRECTORY(./src SRC_LIST)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/encrypt SRC_LIST_ENCRYPT)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/jsoncpp0.5/src SRC_LIST_JSON)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/protobuf SRC_LIST_PROTOBUF)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/nsq SRC_LIST_BASE_NSQ)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/metrics SRC_LIST_BASE_METRICS)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/kafka SRC_LIST_BASE_KAFKA)

SET(BASE_LIST_INC 
     ${CSBASE_DIR} 
     ${CSBASE_DIR}/mysql/include 
     ${CSBASE_DIR}/oss/include
     ${CSBASE_DIR}/Rldb 
     ${CSBASE_DIR}/evpp 
     ${CSBASE_DIR}/etcd 
     ${CSBASE_DIR}/encrypt
     ${CSBASE_DIR}/jsoncpp0.5/include
     ${CSBASE_DIR}/protobuf
     ${CSBASE_DIR}/nsq
     ${CSBASE_DIR}/metrics 
     ${CSBASE_DIR}/grpc 
     ${CSBASE_DIR}/grpc/gens 
     ${CSBASE_DIR}/grpc/include
     ./src)

ADD_DEFINITIONS(-std=c++11 -g -Werror -Wno-unused-parameter -Wno-deprecated -Wno-deprecated-copy -Wno-shift-negative-value)
                           
include_directories(${BASE_LIST_INC} ./src /usr/local/protobuf/include /usr/local/boost/include /usr/local/grpc/include)

SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)

add_executable(csajax ${SRC_LIST} ${SRC_LIST_BASE_RLDB} ${SRC_LIST_BASE_ETCD} ${SRC_LIST_ENCRYPT} ${SRC_LIST_JSON}
${SRC_LIST_PROTOBUF} ${SRC_LIST_BASE_NSQ} ${SRC_LIST_BASE_METRICS} ${SRC_LIST_BASE_SQS} ${prefixed_file_list} ${SRC_LIST_BASE_KAFKA})

set_target_properties(csajax PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/csajax/lib")
target_link_libraries(csajax  ${DEPENDENT_LIBRARIES})
