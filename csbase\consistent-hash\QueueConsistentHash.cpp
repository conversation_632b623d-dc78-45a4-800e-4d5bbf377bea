#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AkLogging.h"
#include <sstream>
#include "util.h"
#include "ConsistentHashMap.hpp"
#include "QueueConsistentHash.h"


namespace akcs_consistent_hash
{

uint32_t QueueConsistentHash::crc32_hash(const std::string key)
{
    boost::crc_32_type ret;
    ret.process_bytes(key.c_str(), key.size());
    return ret.checksum();
}

//队列id重0开始
void QueueConsistentHash::InitQueueNumList(int queue_num)
{
    int i = 0;
    for (i = 0; i < queue_num; i++)
    {
        //构建一致性哈希的环
        {
            //当前不用加锁了，因为不会有移除节点的动作
            //std::lock_guard<std::mutex> lock(write_file_tube_consistent_mutex_);
            for (int j = 0; j < kVnodeNum; j++)
            {
                write_file_tube_consistent_hash_.insert(vnode_t(i, j));
            }
        }
    }
}

int QueueConsistentHash::GetQueueNumByKey(const std::string& key)
{
    uint32_t key_hash = crc32_hash(key);
    int queue_index = -1;
    consistent_hash_t::iterator it;
    {
        //当前不用加锁了，因为不会有移除节点的动作
        //std::lock_guard<std::mutex> lock(write_file_tube_consistent_mutex_);
        it = write_file_tube_consistent_hash_.find(key_hash);
        if (it == write_file_tube_consistent_hash_.end())
        {
            AK_LOG_WARN << "get write file tube id fialed, mng id is " << key;
            return queue_index;
        }

        queue_index = it->second.queue_index_;
    }

    return queue_index;
}

}

