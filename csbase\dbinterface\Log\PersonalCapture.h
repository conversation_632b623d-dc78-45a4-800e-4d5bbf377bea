#ifndef __DB_PERSONAL_CAPTURE_H__
#define __DB_PERSONAL_CAPTURE_H__
#include <string>
#include <memory>
#include <vector>
#include <set>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "util.h"
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "dbinterface/Log/LogSlice.h"
#include "DclientMsgSt.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "ConnectionManager.h"

namespace dbinterface
{

class PersonalCapture
{
public:
    PersonalCapture();
    ~PersonalCapture();

    //添加MotionCapture
    static int AddPersonalCapture(SOCKET_MSG_DEV_REPORT_ACTIVITY& personnal_act_log, int delivery);
    // static int AddOfflineLogCapture(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_log, int delivery);
    static int AddUidReqCapture(UIPC_MSG_CAPTURE_RTSP& personnal_request_capture, int delivery);
    static int UpdateLogCapturePicUrl(const std::string& mac, const std::string& pic_name, 
        const std::string& pic_url, const std::string& spic_url, int delivery, const std::string& db_delivery_uuid);
    static int UpdateLogCaptureThirdCameraPicUrl(const std::string& mac, const std::string& pic_name, 
        const std::string& pic_url, const std::string& spic_url, int delivery, const std::string& db_delivery_uuid);
    static int UpdateLogMotionPicUrl(const std::string& mac, const std::string& pic_name, const std::string& pic_url,
        const std::string& spic_url, int delivery, const std::string& db_delivery_uuid);
    static std::string GetLastSaveMonth(int max_save_month);
    static int CheckDeliveryInMaxMonth(std::time_t delivery_time, int max_save_month);
    static int delAkcsPicExpired(const std::string& table_name, std::string& id, std::set<std::string>& del_urls);
    static int delLOGPicExpired(const std::string& table_name, const LOG_SLICE_INFO& slice_info, 
        std::string& id, std::set<std::string>& del_urls);
    static int GetPicExpiredTables(const std::string& basic_table, const LOG_SLICE_INFO& slice_info,
        std::vector<std::string>& akcs_tables, std::vector<std::string>& log_tables);
    static void DelPicByID(CRldb* conn, const std::string& table_name, const std::string& id);
    static void DelPicByRange(CRldb* conn, const std::string& table_name, int begin_id, int end_id);
    static std::vector<std::string> GetAllAkcsTables(const char* table_name);
    static std::vector<std::string> GetAllLOGTables(const char* table_name);
    static int DelCapturePic(const std::string& mac, std::set<std::string>& del_urls);
    static int DelMotionPic(const std::string& mac, std::set<std::string>& del_urls);
    static int DelCapturePicByMngid(unsigned int mngid, std::set<std::string>& del_urls);
    static int DelMotionPicByMngid(unsigned int mngid, std::set<std::string>& del_urls);
    static void DelOneLOGTablePic(const std::string& table_name, const std::string& cond,
        std::set<std::string>& del_urls);
    static bool IsSUrlPicToBeDel(const char* surl);
    static int AddTemperatureCapture(SOCKET_MSG_DEV_REPORT_ACTIVITY& personnal_act_log);
    static int UpdateTempPicUrl(const std::string& mac, const std::string& pic_name, const std::string& pic_url, const std::string& spic_url);
    //添加MotionCapture
    static void RecordEmergencyContorlDoorLog(const std::string &device_uuid, const std::string &initiator, ACT_OPEN_DOOR_TYPE act_type, RelayStatus status_type, int delivery);
    static int UpdateVideoRecordName(const std::string& mac, const std::string& pic_name, const std::string& video_record_name, int delivery, const std::string& db_delivery_uuid);
    static DatabaseExistenceStatus GetVideoRecordInfo(const std::string& call_trace_id, const std::string& db_delivery_uuid, int delivery, std::string& video_record_name, std::string& video_record_url);
    static int UpdateLogMotionVideoUrl(const std::string& mac, const std::string& video_name, const std::string& video_url, int delivery, const std::string& db_delivery_uuid);
    static int UpdateLogCaptureVideoUrl(const std::string& mac, const std::string& video_name, const std::string& video_url, int delivery, const std::string& db_delivery_uuid);
    // 随机获取picName，解决设备未上报picName时，三方锁开门记录和设备开门记录无法关联的问题
    // 随机picName规则：mac + "-" + 生成的uuid
    static std::string GetRandomPicName(const std::string& mac, const std::string& server_tag);
private: 
    static std::string GetLogTableName(const std::string& table_name, const std::string& db_delivery_uuid, int delivery);
    static int GetLogTableHash(const std::string& db_delivery_uuid, int delivery);
    static DatabaseExistenceStatus GetPersonalCaptureTableList(const std::string& basic_table, std::vector<std::string>& tables_list);
    static std::string GetLogIndex();
};


}

#endif
