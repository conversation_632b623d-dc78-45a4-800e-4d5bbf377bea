#ifndef __DB_INS_APP_FEEDBACK_H__
#define __DB_INS_APP_FEEDBACK_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct InsAppFeedbackInfo_T
{
    char account_user_info_uuid[37];
    char contact_email[255];
    char content[2048];
    char file_list[2048];
    char uuid[36];
    InsAppFeedbackInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} InsAppFeedbackInfo;

namespace dbinterface {

class InsAppFeedback
{
public:
    static int GetInsAppFeedbackByUUID(const std::string& uuid, InsAppFeedbackInfo& ins_app_feedback_info);

private:
    InsAppFeedback() = delete;
    ~InsAppFeedback() = delete;
    static void GetInsAppFeedbackFromSql(InsAppFeedbackInfo& ins_app_feedback_info, CRldbQuery& query);
};

}
#endif