#ifndef __IPC_CONTROL_H__
#define __IPC_CONTROL_H__

#include "stdint.h"
#include <string.h>
#include "json/json.h"
#include "dbinterface/Message.h"
#include "dbinterface/OfficeMessage.h"
#include "AkcsCommonSt.h"
#include "AkcsWebMsgSt.h"

// EmailType枚举定义
enum EmailType
{
    EMAIL_CREATE_UID = 0,
    EMAIL_CHANGE_PWD = 1,
    EMAIL_RESET_PWD = 2,
    EMAIL_CHECK_CODE = 3,
    EMAIL_DEV_APP_WILLBE_EXPIRE = 4,
    EMAIL_SHARE_TMPKEY = 5,
    EMAIL_ACCOUNT_ACTIVE = 6,
    EMAIL_CREATE_PROPERTY_WORK = 7,
    EMAIL_RENEW_SERVER = 8,
    EMAIL_ADD_NEW_SITE = 9,
    EMAIL_PM_APP_CREATE_UID = 10,
    EMAIL_PM_APP_ACCOUNT_ACTIVE = 11,
    EMAIL_PM_APP_RENEW_SERVER = 12,
    EMAIL_PM_APP_RESET_PWD = 13,
    EMAIL_PM_WEB_CREATE_UID = 14,
    EMAIL_PM_WEB_CHANGE_PWD = 15,
    EMAIL_PM_ADD_NEW_SITE = 16,
    EMAIL_PM_LINK_NEW_SITES = 17,
};

typedef struct CSP2A_REBOOT_DEVICE_T  CSP2A_REBOOT_DEVICE;

//modified by chenyc,2019-03-05, 由原先的跟csmain的直接通信改成经过csroute,ipc的名称暂时不变.
class CIPCControl
{
public:
    CIPCControl();
    virtual ~CIPCControl();
    static CIPCControl* GetInstance();

    //发送重启设备请求给csmain模块
    int SendRebootDev(CSP2A_REBOOT_DEVICE* pstDeviceNetInfo);
    int SendResetDev(CSP2A_REBOOT_DEVICE* pstDeviceNetInfo);
    int GetDevConfigure(CSP2A_FROM_DEVICE_CONFIGURE* pstDeviceConf, unsigned int nSeq);
    //个人终端用户,客户端请求修改同一联动单元的设备或者app的配置信息
    int SendPerAlarmDeal(const CSP2A_PERSONNAL_DEAL_ALARM* pstAlarmDealInfo);
    //社区警告被处理通知
    int SendCommunityAlarmDeal(const CSP2A_COMMUNITY_DEAL_ALARM* pstAlarmDealInfo);
    //个人终端用户,发送请求设备状态的UDP消息给csmain进程
    int SendPersonalReportStatus(std::string strMac);
    int SendPerDevLogOutSip(const std::string& strMac);
    int SendPerUidLogOutSip(const std::string& strUid);
    int SendPerMessage();
    void SendPerResetPwdMail(CSP2A_USER_EAMIL_INFO* pstUserEmailInfo, bool is_to_master);
    int SendPerCreateUidMail(CSP2A_USER_CREATE_INFO* pstUserCreateInfo, bool is_to_master);
    int SendPerChangePwdMail(CSP2A_USER_CREATE_INFO* pstUserCreateInfo, bool is_to_master);

    //个人注册账号发送校验码 代码中未使用
    //int SendPerCheckCodeMail(CSP2A_SEND_CHECK_CODE* pstSendCheckCode);
    int SendAppExpire(const CSP2A_DEV_APP_EXPIRE* pstExpire);

    int SendDevNotExpire(const CSP2A_DEV_NOT_EXPIRE* pstExpire);
    int SendDevCleanDeviceCode(const CSP2A_DEV_CLEAN_DEVICE_CODE* pstExpire);
    int SendDevAppWillBeExpire(const CSP2A_DEV_APP_WILLBE_EXPIRE* pstExpire);
    int SendAddVsSched(CSP2A_ADD_VIDEO_STORAGE_SCHED* pstVsSchedulInfo);//csroute需要广播给所有的csmain的...
    int SendDelVsSched(const CSP2A_DEL_VIDEO_STORAGE_SCHED* pstVsSchedInfo);
    int SendDelVs(const CSP2A_DEL_VIDEO_STORAGE* pstVs);
    int SendDevChange(CSP2A_DEVICE_CHANGE_INFO* pstDevChange);
    int SendShareTmpkeyEmail(const CSP2A_SHARE_TEMKEY_INFO* pstShareTempkey);
    // int SendRemoteOpenDoor(const CSP2A_REMOTE_OPENDDOR_INFO* pstRemoteOpenDoor);
    int SendCreatePropertyWork(const CSP2A_CREATE_PROPERTY_WORK_INFO* pstCreateProperty);
    int SendAccountActiveEmail(const CSP2A_ACCOUNT_ACTIVE_INFO* pstExpire);
    //v4.5
    int SendRenewServerEmail(const std::vector<CSP2A_UID_RENEW_SRV_INFO>& uid_infos);
    int SendPmRenewServerEmail(const CSP2A_UID_PM_RENEW_SRV_INFO& uid_info);
    int SendPmEmail(const CSP2A_PM_INFO* pstPmInfo);

    int SendAlexaLogin(CSP2A_ALEXA_LOGIN_INFO* pst_alexa_login);
    int SendAlexaSetArming(CSP2A_ALEXA_SET_ARMING_INFO* pst_alexa_set_arming);

    int SendPhoneExpire(const CSP2A_DEV_APP_EXPIRE* pstExpire);
    int SendPhoneWillExpire(const CSP2A_DEV_APP_EXPIRE* pstExpire);
    int SendInstallerPhoneWillExpire(const CSP2A_INSTALLER_EXPIRE* pstExpire);
    int SendInstallerAppWillExpire(const CSP2A_INSTALLER_EXPIRE* pstExpire, std::string community);
    int SendCreateRemoteDevContorl(CSP2A_CREATE_REMOTE_DEV_CONTORL_INFO& info);
    int NotifyRefreshConnCache(CSP2A_REFRESH_CACHE& info);
    int SendDevFileChange(CSP2A_DEV_FILE_CHANGE* dev_change);

    int SendPMFeatureWillExpire(const CSP2A_PM_EXPIRE* pstExpire);
    int SendInstallerFeatureWillExpire(const CSP2A_INSTALLER_EXPIRE* pstExpire);
    int SendPmAppAccountWillBeExpireEmail(const CSP2A_PM_INFO* pstPmInfo);
    int SendPmAppAccountExpireEmail(const CSP2A_PM_INFO* pstPmInfo);

    int SendPmAccountActiveEmail(const CSP2A_ACCOUNT_ACTIVE_INFO* account_info);
    int PushLinKerText(const PersoanlMessageSend& text_msg, const LINKER_NORMAL_MSG &linker_msg);
    void FormatLinkerJsonData(const LINKER_NORMAL_MSG &linker_msg, Json::Value &item);
    int SendLinKerCommonMsg(int msg_type, const std::string &data_json, const std::string &key);
    int SendUserAddNewSite(const CSP2A_PER_ADD_NEWSITE& per_add_new_site);
    int SendPmWebLinkNewSites(const CSP2A_PM_LINK_NEWSITES& pm_link_new_sites);
    int SendPmWebCreateUidMail(const CSP2A_USER_CREATE_INFO& user_create_info);
    int SendPmWebChangePwdMail(const CSP2A_USER_CREATE_INFO& user_create_info);
    int SendCommonEmailCode(const CSP2A_SEND_VERFICATION_CODE& verification_code);
    int SendCommonSmsCode(const CSP2A_SEND_VERFICATION_CODE& verification_code);
    void SendRequestDevDelLog(const std::string& mac);

    std::string getEmailLanguage(const EmailType email_type, const std::string& email);
    
    // installer app反馈邮件处理
    int SendInsAppFeedbackEmail(const std::string& feedback_uuid);
    
    // 通用的邮件发送逻辑
    void sendEmailNotification(const Json::Value& root_value, const Json::Value& item_data, const std::string& email);

private:
    static CIPCControl* instance;
};

CIPCControl* GetIPCControlInstance();
#endif //__IPC_CONTROL_H__

