#ifndef AKCS_SQS_MSG_PARSE_H_
#define AKCS_SQS_MSG_PARSE_H_

#include <map> 
#include <string>
#include <list>
#include "json/json.h"

typedef std::map<std::string, std::string> SqsMsgKvList;

class AjaxMsgParser
{
public:
    enum ParseMsgStatus
    {
        FAIL = 0,
        OK = 1,
        EXPIRED = 2,
    };

    AjaxMsgParser(const std::string& msg);
    bool ParseOk()
    {
        return status_ == OK;
    }
    void Parse(const std::string& msg);

    bool CheckHubIdExists();

    const SqsMsgKvList& GetMsgKvList() const
    {
        return msg_kv_list_;
    }

    int GetParseStatus()
    {
        return (int)status_;
    }

private:
    void ParseSqsMessageInfoKv(const Json::Value& body);
    
    // 剔除重复字段，降低消息体长度
    Json::Value GetRemovedAddtionalDataJson(const Json::Value& original_body);

    bool IsAjaxMsgExpired(uint64_t timestamp_us);

    std::string    msg_type_;
    std::string    trace_id_;
    uint64_t       timestamp_us_;
    ParseMsgStatus status_;
    SqsMsgKvList   msg_kv_list_;
};




#endif

