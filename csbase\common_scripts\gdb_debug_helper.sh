#!/bin/bash
set -euo pipefail
# 用法: bash gdb_debug_helper.sh 组件名
if [ $# -ne 1 ]; then
  echo "用法: $0 <组件名>"
  echo "示例: bash gdb_debug_helper.sh csadapt"
  exit 1
fi

COMPONENT="$1"

# 1. 获取运行中该组件的镜像名+版本
IMAGE_FULL=$(docker ps | grep "$COMPONENT" | awk '{print $2}' | head -n 1)
if [ -z "$IMAGE_FULL" ]; then
  echo "未找到正在运行的组件: $COMPONENT"
  exit 2
fi

# 2. 拼接_debug
DEBUG_IMAGE="${IMAGE_FULL}_debug"

# 3. 获取debug镜像中的存档文件
mkdir -p /root/cpp_ubuntu20_debug
docker run --rm -v /root/cpp_ubuntu20_debug:/root/cpp_ubuntu20_debug "$DEBUG_IMAGE" cp -rf /archive /root/cpp_ubuntu20_debug

# 4. 启动gdb容器（先判断是否已存在）
if docker ps | grep -q gdb_docker_ubuntu20; then
  echo "gdb_docker_ubuntu20 容器已存在"
else
  docker run -itd -e TZ=Asia/Shanghai --restart=always --net=host -v /root/cpp_ubuntu20_debug:/root/cpp_ubuntu20_debug -v /var/core:/var/core --name gdb_docker_ubuntu20 registry.cn-hangzhou.aliyuncs.com/ak_system/gdb_docker_ubuntu20:1.0 /bin/bash
fi

# 5. rmi掉临时debug镜像
docker rmi "$DEBUG_IMAGE"

# 6. 提示后续操作
echo "--------------------------------------------------------------------------------------"
echo "接下来你可以执行以下命令进入gdb容器进行调试："
echo "docker exec -it gdb_docker_ubuntu20 /bin/bash"
echo "gdb /root/cpp_ubuntu20_debug/archive/$COMPONENT/bin/$COMPONENT core_xxxxx"
echo "调试完毕后记得exit退出容器哦"

