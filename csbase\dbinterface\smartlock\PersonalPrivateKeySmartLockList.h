#ifndef __DB_PERSONAL_PRIVATE_KEY_SMART_LOCK_LIST_H__
#define __DB_PERSONAL_PRIVATE_KEY_SMART_LOCK_LIST_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include <unordered_map>
#include <set>
#include <unordered_set>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct PersonalPrivateKeySmartLockListInfo_T
{
    char uuid[36];
    char smart_lock_uuid[36];
    char personal_private_key_uuid[36];
    char personal_account_uuid[36];
    char main_user_uuid[36];
    PersonalPrivateKeySmartLockListInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} PersonalPrivateKeySmartLockListInfo;

typedef std::unordered_set<std::string> SmartLockSupportPinList;
typedef std::unordered_map<std::string, SmartLockSupportPinList> SmartLockSupportPinListMap;

namespace dbinterface {

class PersonalPrivateKeySmartLockList
{
public:
    static int GetPersonalPrivateKeySmartLockListBySmartLockUUID(const std::string& smart_lock_uuid, PersonalPrivateKeySmartLockListInfo& personal_private_key_smart_lock_list_info);
    static int GetSmartLockUUIDListByPinUUID(const std::string& pin_uuid, std::vector<std::string>& smart_lock_uuid_list);
    static int GetSmartLockSupportPinListByNode(const std::string& node, SmartLockSupportPinListMap& smart_lock_support_pin_list_map);
private:
    PersonalPrivateKeySmartLockList() = delete;
    ~PersonalPrivateKeySmartLockList() = delete;
    static void GetPersonalPrivateKeySmartLockListFromSql(PersonalPrivateKeySmartLockListInfo& personal_private_key_smart_lock_list_info, CRldbQuery& query);
};

}
#endif