#include "Metric.h"
#include "AkLogging.h"
#include "ConfigFileReader.h"
#include "EtcdCliMng.h"
#include "RouteMqProduce.h"
#include "ConnectionPool.h"

extern RouteMQProduce* g_nsq_producer;
extern CAkEtcdCliManager* g_etcd_cli_mng;

#define VERSION_CONF_FILE "/usr/local/akcs/csajax/conf/version.conf"

void InitMetricInstance()
{
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        return;
    }

    //版本信息
    CConfigFileReader tag_config_file(VERSION_CONF_FILE);
    std::string branch_or_tag_version = tag_config_file.GetConfigName("branch_or_tag");
    static long version_metric = (long)ATOI(branch_or_tag_version.c_str());

    // 添加 metric 指标
    metric_service->AddMetric(
        "version_metric",
        "version description",
        "version_metric{team=\"app_backend\"}",
        []() -> long { return version_metric; }
    );

    metric_service->AddMetric(
        "db_get_conn_failed_count",
        "DB GetConnection failed count",
        "csajax_db_get_conn_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "nsq_check",
        "nsq producer status",
        "csajax_nsq_check_error",
        []() -> long { return (long)(g_nsq_producer->Status() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "db_conn_check",
        "db conn status",
        "csajax_db_conn_check_error",
        []() -> long { return (long)(GetDBConnPollInstance()->CheckDBConnNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "etcd_check",
        "etcd server status",
        "csajax_etcd_check_error",
        []() -> long { return (long)(g_etcd_cli_mng->CheckEtcdCliStatus() ? 0 : 1); }
    );
}


