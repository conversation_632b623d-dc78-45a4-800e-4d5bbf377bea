#include "AjaxMsgParser.h"
#include "AkLogging.h"
#include <chrono>

#define AJAX_MSG_EXPIRED_TIME_MS 120000

AjaxMsgParser::AjaxMsgParser(const std::string& msg) : status_(FAIL)
{
    Parse(msg);
}

void AjaxMsgParser::Parse(const std::string& msg)
{
    Json::Reader reader;
    Json::Value root;
    Json::Value data;

    AK_LOG_INFO << "AjaxMsgParser msg=" << msg;

    if (!reader.parse(msg, root))
    {
        AK_LOG_WARN << "AjaxMsgParser parse error. msg=" << msg << " error msg=" << reader.getFormatedErrorMessages();
        return;
    }

    ParseSqsMessageInfoKv(root);

    if (!msg_kv_list_.empty())
    {
        status_ = OK;
    }
}

//解析body
void AjaxMsgParser::ParseSqsMessageInfoKv(const Json::Value& body)
{
    if (!body.isObject() || !body.isMember("event"))
    {
        AK_LOG_WARN << "ajax msg body wrong.";
        return;
    }

    Json::Value event = body["event"];

    // 时间戳
    if (!event.isObject() || !event.isMember("timestamp"))
    {
        AK_LOG_WARN << "ajax msg event wrong. not have timestamp.";
        return;
    }
    
    // 先解析为字符串，再转换为uint64_t
    std::string timestamp_str = event["timestamp"].asString();
    uint64_t timestamp_us = 0;
    try {
        timestamp_us = std::stoull(timestamp_str);
    } catch (const std::exception& e) {
        AK_LOG_WARN << "ajax msg timestamp parse error. timestamp=" << timestamp_str << " error=" << e.what();
        return;
    }

    if (IsAjaxMsgExpired(timestamp_us))
    {
        AK_LOG_WARN << "ajax msg expired. timestamp=" << timestamp_us;
        status_ = EXPIRED;
        return;
    }

    // hub id
    if (!event.isMember("hubId"))
    {
        AK_LOG_WARN << "ajax msg event wrong. not have hubId.";
        return;
    }
    
    msg_kv_list_.emplace("hub_id", event["hubId"].asString());

    Json::FastWriter writer;
    // body
    msg_kv_list_.emplace("body", writer.write(body));
}

Json::Value AjaxMsgParser::GetRemovedAddtionalDataJson(const Json::Value& original_body)
{
    // 如果不是对象，直接返回原始值
    if (!original_body.isObject())
    {
        return original_body;
    }
    
    // 创建一个副本以便修改
    Json::Value result = original_body;
    
    // 检查是否存在event字段
    if (result.isMember("event") && result["event"].isObject())
    {
        // 检查event中是否存在additionalData字段
        if (result["event"].isMember("additionalData"))
        {
            // 若存在，移除additionalData字段
            result["event"].removeMember("additionalData");
        }
    }
    
    return result;
}   

bool AjaxMsgParser::CheckHubIdExists()
{
    return true;
}

bool AjaxMsgParser::IsAjaxMsgExpired(uint64_t timestamp_us)
{
    // 获取当前时间戳（毫秒）
    auto now = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::system_clock::now().time_since_epoch()).count();
    
    uint64_t current_timestamp = static_cast<uint64_t>(now);

    // 计算时间差（毫秒）
    // 对于13位的timestamp（毫秒级），uint64_t可以安全存储
    uint64_t time_diff = 0;
    if (current_timestamp >= timestamp_us) {
        // 正常情况：当前时间大于消息时间
        time_diff = current_timestamp - timestamp_us;
    } else {
        return false; // 一般不会出现这种情况
    }
        
    AK_LOG_INFO << "Current timestamp=" << current_timestamp << ", Message timestamp=" << timestamp_us 
                << ", Time diff=" << time_diff << "ms";
    
    // 如果时间差大于2分钟，返回true（表示已过期）
    if (time_diff > AJAX_MSG_EXPIRED_TIME_MS)
    {
        return true;
    }
    
    return false;
}
