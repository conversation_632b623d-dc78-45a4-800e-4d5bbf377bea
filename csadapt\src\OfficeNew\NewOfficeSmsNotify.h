#ifndef __MSG_HANDLE_SMS_NOTIFY_H__
#define __MSG_HANDLE_SMS_NOTIFY_H__

#include <unordered_map> 
#include "json/json.h"
#include "AK.Adapt.pb.h"
#include "AkcsPduBase.h"
#include "AdaptDef.h"
#include "AKCSView.h"
#include "AkcsMsgDef.h"
#include "AkLogging.h"
#include "AdaptMQProduce.h"
#include "KafkaParseWebMsg.h"

class NewOfficeSmsNotify
{
public:
    NewOfficeSmsNotify(){}
    static void Handle(const std::string& msg, const std::string& msg_type, const KakfaMsgKV &kv, const std::string& oem_name);
    static void SendCommonSmsCode(const KakfaMsgKV &kv);
    static void SendDeleteAppAccountCode(const KakfaMsgKV &kv);
    static void SendAdminAppResetPWD(const KakfaMsgKV &kv);
 };


#endif

