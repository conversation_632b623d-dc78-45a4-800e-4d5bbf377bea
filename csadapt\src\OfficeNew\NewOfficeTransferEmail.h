#ifndef MSG_HANDLE_TRANSFER_EMAIL_H_
#define MSG_HANDLE_TRANSFER_EMAIL_H_

#include <unordered_map> 
#include "json/json.h"
#include "AK.Adapt.pb.h"
#include "AkcsPduBase.h"
#include "AdaptDef.h"
#include "AKCSView.h"
#include "AkcsMsgDef.h"
#include "AdaptMQProduce.h"
#include "KafkaParseWebMsg.h"

class NewOfficeTransferEmail
{
public:
    NewOfficeTransferEmail(){}
    static void Handle(const std::string& msg, const std::string& msg_type, const KakfaMsgKV &kv, const std::string& oem_name);
};


#endif

