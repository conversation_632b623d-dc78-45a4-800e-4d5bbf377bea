#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeFingerPrint.h"

namespace dbinterface {

static const std::string fingerprint_info_sec = " UUID,AccountUUID,PersonalAccountUUID,FingerPrint,FingerPrintMD5 ";

void UserFingerPrint::GetFingerPrintFromSql(FingerPrintInfo& fingerprint_info, CRldbQuery& query)
{
    Snprintf(fingerprint_info.uuid, sizeof(fingerprint_info.uuid), query.GetRowData(0));
    Snprintf(fingerprint_info.account_uuid, sizeof(fingerprint_info.account_uuid), query.GetRowData(1));
    Snprintf(fingerprint_info.personal_account_uuid, sizeof(fingerprint_info.personal_account_uuid), query.GetRowData(2));
    Snprintf(fingerprint_info.fingerprint, sizeof(fingerprint_info.fingerprint), query.GetRowData(3));
    Snprintf(fingerprint_info.fingerprint_md5, sizeof(fingerprint_info.fingerprint), query.GetRowData(4));
    return;
}

int UserFingerPrint::GetFingerPrintByProjectUUID(const std::string& project_uuid, UserFingerPrintMap& fingerprint_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << fingerprint_info_sec << " from OfficeFingerPrint where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        FingerPrintInfo info;
        GetFingerPrintFromSql(info, query);
        fingerprint_info.insert(std::make_pair(info.personal_account_uuid, info));
    } 
    return 0;   
}

}

