#include <stdio.h>
#include "IPCControl.h"
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include "util_cstring.h"
#include "AdaptDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "json/json.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/Account.h"
#include "dbinterface/DistributorInfo.h"
#include "dbinterface/CommunityInfo.h"
#include "AK.Adapt.pb.h"
#include "UnixSocketControl.h"
#include "AKCSView.h"
#include "AdaptMQProduce.h"
#include "AK.Server.pb.h"
#include "AK.Linker.pb.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "AkcsCommonDef.h"
#include "AK.BackendCommon.pb.h"
#include "BackendP2PMsgControl.h"
#include "dbinterface/ProjectInfo.h"
#include "dbinterface/InsAppFeedback.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/AccountMap.h"
#include "dbinterface/CustomerService.h"
#include "dbinterface/SubDisMngList.h"
#include "dbinterface/Account.h"
#include "dbinterface/InsAppFeedbackReceiverList.h"



#define IPC_RECONNECT_INTERVAL  1000
#define IPC_SELECT_TIMEOUT      2000

extern CSADAPT_CONF gstCSADAPTConf;
extern RouteMQProduce* g_nsq_producer;

CIPCControl* GetIPCControlInstance()
{
    return CIPCControl::GetInstance();
}

CIPCControl::CIPCControl()
{

}
CIPCControl::~CIPCControl()
{

}

CIPCControl* CIPCControl::instance = NULL;

CIPCControl* CIPCControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CIPCControl();
    }

    return instance;
}

//个人终端用户,发送请求设备状态的UDP消息给csmain进程
int CIPCControl::SendPersonalReportStatus(std::string strMac)
{
    AK::Server::P2PAdaptReportStatusMsg msg;
    msg.set_mac(strMac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_REPORT_STATUS);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//发送远程重启设备的消息给csmain进程
int CIPCControl::SendRebootDev(CSP2A_REBOOT_DEVICE* pstDeviceNetInfo)
{
    if (NULL == pstDeviceNetInfo)
    {
        AK_LOG_WARN << "param is null";
        return -1;
    }

    AK::Server::P2PAdaptRebootDevMsg msg;
    msg.set_mac(pstDeviceNetInfo->szMac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_REBOOT_DEVICE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//发送远程重置设备的消息给csmain进程
int CIPCControl::SendResetDev(CSP2A_REBOOT_DEVICE* pstDeviceNetInfo)
{
    if (NULL == pstDeviceNetInfo)
    {
        AK_LOG_WARN << "param is null";
        return -1;
    }

    AK::Server::P2PAdaptResetDevMsg msg;
    msg.set_mac(pstDeviceNetInfo->szMac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_RESET_DEVICE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::GetDevConfigure(CSP2A_FROM_DEVICE_CONFIGURE* pstDeviceConf, unsigned int nSeq)
{
#if 0

    if (NULL == pstDeviceConf)
    {
        AK_LOG_WARN << "param is null";
        return -1;
    }
    int nRet = -1;
    //请求序列号放在IPC_MSG ipcMsg.param1
    nRet = SendMsg(MSG_C2S_CONFIGURE_FROM_DEVICE, nSeq, 0, pstDeviceConf, sizeof(CSP2A_FROM_DEVICE_CONFIGURE));
    if (nRet < 0)
    {
        AK_LOG_WARN << "SendMsg to csmain failed.";
        return -1;
    }
#endif
    return 0;
}


//个人终端用户,客户端请求修改同一联动单元的设备或者app的配置信息
int CIPCControl::SendPerAlarmDeal(const CSP2A_PERSONNAL_DEAL_ALARM* pstAlarmDealInfo)
{
    if (NULL == pstAlarmDealInfo)
    {
        AK_LOG_WARN << "param is null";
        return -1;
    }
    AK::Server::GroupPerAlarmDealMsg msg;
    msg.set_node(pstAlarmDealInfo->szAreaNode);
    msg.set_alarm_id(pstAlarmDealInfo->szAlarmID);
    msg.set_deal_user(pstAlarmDealInfo->szUser);
    msg.set_deal_result(pstAlarmDealInfo->szResult);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_PERSONAL_ALARM_DEAL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return 0;
}

//社区用户,发送警告处理
int CIPCControl::SendCommunityAlarmDeal(const CSP2A_COMMUNITY_DEAL_ALARM* pstAlarmDealInfo)
{
    if (NULL == pstAlarmDealInfo)
    {
        AK_LOG_WARN << "param is null";
        return -1;
    }
    AK::Server::GroupPerAlarmDealMsg msg;
    msg.set_node(pstAlarmDealInfo->szAreaNode);
    msg.set_alarm_id(pstAlarmDealInfo->szAlarmID);
    msg.set_deal_user(pstAlarmDealInfo->szUser);
    msg.set_deal_result(pstAlarmDealInfo->szResult);
    msg.set_deal_time(pstAlarmDealInfo->szResult);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_COMMUNITY_ALARM_DEAL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//个人终端用户,发送请求设备注销sip的tcp消息给csmain进程
int CIPCControl::SendPerDevLogOutSip(const std::string& strMac)
{
    AK::Server::P2PAdaptDevLogOutMsg msg;
    msg.set_macs(strMac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_DEL_DEV);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//个人终端用户,发送请求设备注销sip的tcp消息给csmain进程
int CIPCControl::SendPerUidLogOutSip(const std::string& strUid)
{
    AK::Server::P2PAdaptUidLogOutMsg msg;
    msg.set_uids(strUid);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_DEL_UID);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//个人终端用户,发送有最新待发送文本消息的tcp消息给csmain进程
int CIPCControl::SendPerMessage()
{
    while (1)
    {
        PerMsgSendList text_messages;
        int finish = dbinterface::Message::GetTextMsgSendList(text_messages);//从数据库读取未发送出去的多条消息
        if (finish)
        {
            return 0;
        }
        int count = 0;
        for (auto& text_send : text_messages)//可能有多条消息
        {
            snprintf(text_send.text_message.from, sizeof(text_send.text_message.from), "%s", "Security Center");
            AK::Server::GroupAdaptTextMsg msg;
            msg.add_node_list(text_send.account);
            msg.set_client_type(text_send.client_type);
            msg.set_title(text_send.text_message.title);
            msg.set_content(text_send.text_message.content);
            msg.set_time(text_send.text_message.time);
            msg.set_from(text_send.text_message.from);
            msg.set_to(text_send.text_message.to);//
            msg.set_id(text_send.text_message.id);
            msg.set_type(text_send.text_message.type);
            
            CAkcsPdu pdu;
            pdu.SetMsgBody(&msg);
            pdu.SetHeadLen(sizeof(PduHeader_t));
            pdu.SetVersion(50);
            pdu.SetCommandId(MSG_C2S_PER_SEND_TEXT_MSG);
            pdu.SetSeqNum(0);
            g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

            if (text_send.client_type == PersoanlMessageSend::TextClientType::APP_SEND)
            {
                //家居text通知
                LINKER_NORMAL_MSG linker_msg;
                memset(&linker_msg, 0, sizeof(linker_msg));
                ProjectInfo project(text_send.account, linker_msg);
                PushLinKerText(text_send, linker_msg);
            }

            ++count;
            if (count == 50)
            {
                usleep(100 * 1000);
                count = 0;
            }
        }     
    }
    return 0;
}

//个人终端用户,发送新建用户的邮箱信息给csmain进程,csmain再发送给cspush
int CIPCControl::SendPerCreateUidMail(CSP2A_USER_CREATE_INFO* pstUserCreateInfo, bool is_to_master)
{
    Json::Value root;
    Json::Value item_data;

    root["OEM"] = gstCSADAPTConf.oem_name;

    // 获取账号信息
    EmailInfo email_info;
    dbinterface::ResidentPersonalAccount::GetEmailInfoByAccount(email_info, pstUserCreateInfo->szUser);

    if (strlen(email_info.oem_name) > 0)
    {
        root["OEM"] = email_info.oem_name;
    }

    item_data["uid"] = pstUserCreateInfo->szUser;
    item_data["email"] = pstUserCreateInfo->szEmail;
    item_data["pwd"] = pstUserCreateInfo->szPwd;
    item_data["qrcode_body"] = pstUserCreateInfo->szQRCodeBody;
    item_data["qrcode_url"] = pstUserCreateInfo->szQRCodeUrlPath;
    item_data["gw_code"] = std::to_string(gstCSADAPTConf.gateway_num);
    item_data["is_fake"] = pstUserCreateInfo->is_fake;
    item_data["role"] = email_info.role;
    item_data["community"] = email_info.community;

    if (email_info.role != ACCOUNT_ROLE_COMMUNITY_PM)
    {
        ResidentPerAccount personal_account;
        int ret = dbinterface::ResidentPersonalAccount::GetUidAccount(pstUserCreateInfo->szUser, personal_account);
        pstUserCreateInfo->enable_smarthome = CAKCSView::GetInstance()->CheckEnableSmarthome(personal_account);   
        item_data["enable_smarthome"] = std::to_string(pstUserCreateInfo->enable_smarthome);
    }

    if (email_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        // PM账号创建用户邮件
        item_data["email_type"] = "create_pm_uid";
        item_data["user"] = pstUserCreateInfo->szUser; // PM邮件模板的user是sip账号
        item_data["language"] = getEmailLanguage(EMAIL_PM_APP_CREATE_UID, pstUserCreateInfo->szEmail);
    }
    else if (is_to_master)
    {
        // 发送给主账号的家庭创建用户邮件
        item_data["email_type"] = "family_create_uid";
        item_data["user"] = email_info.username;
        item_data["uid"] = pstUserCreateInfo->user_uid;  // 原始从账号用户名
        item_data["language"] = getEmailLanguage(EMAIL_CREATE_UID, pstUserCreateInfo->szEmail);
    }
    else
    {
        // 普通个人用户创建用户邮件
        item_data["email_type"] = "create_uid";
        item_data["user"] = email_info.username;
        item_data["language"] = getEmailLanguage(EMAIL_CREATE_UID, pstUserCreateInfo->szEmail);      
    }

    AK_LOG_INFO << "[SendPerCreateUidMail] Send " << (is_to_master ? "family " : "") 
                << "create uid email. user:" << item_data["user"].asString() 
                << " uid:" << item_data["uid"].asString() 
                << " role:" << email_info.role 
                << " email:" << pstUserCreateInfo->szEmail 
                << " to_master:" << (is_to_master ? "true" : "false") 
                << " OEM:" << root["OEM"].asString();

    sendEmailNotification(root, item_data, pstUserCreateInfo->szEmail);
    return 0;
}

//界面上用户忘记密码,csadapt直接构建邮件数据发送
void CIPCControl::SendPerResetPwdMail(CSP2A_USER_EAMIL_INFO* pstUserEmailInfo, bool is_to_master)
{
    Json::Value root_value;
    Json::Value itemData;
    root_value["OEM"] = gstCSADAPTConf.oem_name;
    
    itemData["email"] = pstUserEmailInfo->szEmail;
    itemData["token"] = pstUserEmailInfo->szToken;
    itemData["role_type"] = pstUserEmailInfo->szRoleType;

    EmailInfo email_info;
    dbinterface::ResidentPersonalAccount::GetEmailInfoByAccount(email_info, pstUserEmailInfo->szUser);
    
    if (strlen(email_info.oem_name) > 0)
    {
        root_value["OEM"] = email_info.oem_name;
    }

    itemData["role"] = email_info.role;
    itemData["user"] = email_info.username;
    itemData["community"] = email_info.community;
    
    if (is_to_master)
    {
        itemData["email_type"] = "family_reset_pwd";
    }
    else
    {
        itemData["email_type"] = "reset_pwd";
    }

    // 设置语言
    if (email_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        itemData["language"] = getEmailLanguage(EMAIL_PM_APP_RESET_PWD, pstUserEmailInfo->szEmail);
    }
    else
    {
        itemData["language"] = getEmailLanguage(EMAIL_RESET_PWD, pstUserEmailInfo->szEmail);
    }

    AK_LOG_INFO << "[SendPerResetPwdMail] Send " << (is_to_master ? "family " : "") 
                << "reset pwd email. user:" << itemData["user"].asString() 
                << " email:" << pstUserEmailInfo->szEmail 
                << " role:" << email_info.role 
                << " role_type:" << pstUserEmailInfo->szRoleType 
                << " community:" << email_info.community
                << " OEM:" << root_value["OEM"].asString();
    
    sendEmailNotification(root_value, itemData, pstUserEmailInfo->szEmail);
}

//个人终端用户,发送修改密码的邮件给csmain进程,csmain再发送给cspush
int CIPCControl::SendPerChangePwdMail(CSP2A_USER_CREATE_INFO* pstUserCreateInfo, bool is_to_master)
{
    Json::Value root_value;
    root_value["OEM"] = gstCSADAPTConf.oem_name;

    Json::Value itemData;
    itemData["email"] = pstUserCreateInfo->szEmail;
    itemData["pwd"] = pstUserCreateInfo->szPwd;
    itemData["qrcode_body"] = pstUserCreateInfo->szQRCodeBody;
    itemData["qrcode_url"] = pstUserCreateInfo->szQRCodeUrlPath;
    itemData["gw_code"] = std::to_string(gstCSADAPTConf.gateway_num);
    itemData["email_type"] = "change_pwd";
    itemData["uid"] = pstUserCreateInfo->szUser;
    itemData["language"] = getEmailLanguage(EMAIL_CHANGE_PWD, pstUserCreateInfo->szUser);

    EmailInfo email_info;
    dbinterface::ResidentPersonalAccount::GetEmailInfoByAccount(email_info, pstUserCreateInfo->szUser);
    
    if (strlen(email_info.oem_name) > 0)
    {
        root_value["OEM"] = email_info.oem_name;
    }

    // 根据不同情况设置邮件类型、用户信息和语言
    if (email_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        itemData["email_type"] = "change_pm_pwd";
        itemData["user"] = pstUserCreateInfo->szUser; // pm 邮件模板的user是sip账号
        itemData["language"] = getEmailLanguage(EMAIL_PM_WEB_CHANGE_PWD, pstUserCreateInfo->szEmail);
    }
    else if (is_to_master)
    { 
        itemData["email_type"] = "family_change_pwd";
        itemData["user"] = email_info.username;
        itemData["uid"] = pstUserCreateInfo->user_uid;
    }
    else
    {
        itemData["user"] = email_info.username;
    }
    itemData["community"] = email_info.community;

    AK_LOG_INFO << "[SendPerChangePwdMail] Send " << (is_to_master ? "family " : "") 
                << "change pwd email. user:" << itemData["user"].asString() 
                << " uid:" << itemData["uid"].asString() 
                << " email:" << pstUserCreateInfo->szEmail 
                << " community:" << email_info.community
                << " OEM:" << root_value["OEM"].asString();
    
    sendEmailNotification(root_value, itemData, pstUserCreateInfo->szEmail);
    return 0;
}

//个人自己注册账号,csadapt通知csmain,发送相关信息到用户邮箱
/*  代码中未使用
int CIPCControl::SendPerCheckCodeMail(CSP2A_SEND_CHECK_CODE* pstSendCheckCode)
{
    AK::Server::P2PAdaptPerCheckCodeMailMsg msg;
    msg.set_check_code(pstSendCheckCode->szCheckCode);
    msg.set_email(pstSendCheckCode->szEmail);
    msg.set_language(pstSendCheckCode->szLanguage);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_CHECK_CODE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;

}
*/

//app过期了
int CIPCControl::SendAppExpire(const CSP2A_DEV_APP_EXPIRE* pstExpire)
{
    AK::Server::GroupAdaptDevAppExpireMsg msg;
    //目前只有一个,php脚本传过来是一个个传的.后续需要修改成流式接口
    AK::Server::GroupAdaptDevAppExpireMsg::GroupAdaptAppExpireInnerMsg* inner_msg = msg.add_expire_uid_list();
    inner_msg->set_user_name(pstExpire->szUserName);
    inner_msg->set_email(pstExpire->szEmail);
    inner_msg->set_community(pstExpire->community);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEV_APP_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//设备app即将过期了
int CIPCControl::SendDevAppWillBeExpire(const CSP2A_DEV_APP_WILLBE_EXPIRE* pstExpire)
{
    Json::Value root_value;
    Json::Value itemData;
    root_value["OEM"] = gstCSADAPTConf.oem_name;
    
    DistributorInfoSt dis_info;
    if (0 == dbinterface::DistributorInfo::GetDisInfoByUserAccount(pstExpire->szUid, project::RESIDENCE, dis_info))
    {
        if (strlen(dis_info.oem_name) > 0)
        {
            root_value["OEM"] = dis_info.oem_name;
        }
    }
    
    itemData["email_type"] = "will_expire";
    itemData["user"] = pstExpire->szUserName;
    itemData["email"] = pstExpire->szEmail;
    itemData["community"] = pstExpire->community;
    itemData["language"] = getEmailLanguage(EMAIL_DEV_APP_WILLBE_EXPIRE, pstExpire->szEmail);
    
    AK_LOG_INFO << "[SendDevAppWillBeExpire] Send app will expire email. user:" << pstExpire->szUserName 
                << " email:" << pstExpire->szEmail 
                << " community:" << pstExpire->community
                << " OEM:" << root_value["OEM"].asString();
    
    sendEmailNotification(root_value, itemData, pstExpire->szEmail);
    return 0;
}


//设备未过期
int CIPCControl::SendDevNotExpire(const CSP2A_DEV_NOT_EXPIRE* pstExpire)
{
    AK::Server::GroupAdaptDevNotExpireMsg msg;
    msg.set_uids(pstExpire->szUids);
    msg.set_macs(pstExpire->szMacs);
    msg.set_node(pstExpire->szNode);
    msg.set_type(pstExpire->nType);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEV_NOT_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//清空设备码
int CIPCControl::SendDevCleanDeviceCode(const CSP2A_DEV_CLEAN_DEVICE_CODE* pstExpire)
{
    AK::Server::P2PAdaptDevCleanDeviceCodeMsg msg;
    msg.set_macs(pstExpire->szMacs);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEV_CLEAN_DEV_CODE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}
//added by chenyc, 2018-08-27,for 视频存储
int CIPCControl::SendAddVsSched(CSP2A_ADD_VIDEO_STORAGE_SCHED* pstVsSchedulInfo)
{
    AK::Server::GroupAdaptAddVsSchedMsg msg;
    msg.set_id(pstVsSchedulInfo->id);
    msg.set_sched_type(pstVsSchedulInfo->sched_type);
    msg.set_date_flag(pstVsSchedulInfo->date_flag);
    msg.set_mac(pstVsSchedulInfo->mac);
    msg.set_begin_time(pstVsSchedulInfo->begin_time);
    msg.set_end_time(pstVsSchedulInfo->end_time);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_ADD_VIDEO_STORAGE_SCHED);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendDelVsSched(const CSP2A_DEL_VIDEO_STORAGE_SCHED* pstVsSchedInfo)
{
    AK::Server::GroupAdaptDelVsSchedMsg msg;
    msg.set_id(pstVsSchedInfo->id);
    msg.set_sched_type(pstVsSchedInfo->sched_type);
    msg.set_mac(pstVsSchedInfo->mac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEL_VIDEO_STORAGE_SCHED);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendDelVs(const CSP2A_DEL_VIDEO_STORAGE* pstVs)
{
    AK::Server::P2PAdaptDelVsMsg msg;
    msg.set_video_id(pstVs->video_id);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEL_VIDEO_STORAGE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//修改设备，要通知csmain更新内存数据
int CIPCControl::SendDevChange(CSP2A_DEVICE_CHANGE_INFO* pstDevChange)
{
    AK::Server::P2PAdaptDevChangeMsg msg;
    msg.set_mac_id(pstDevChange->nMacid);
    msg.set_is_per(pstDevChange->nIsPer);
    msg.set_mac(pstDevChange->szMac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEV_CHANGE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//账号是否激活
int CIPCControl::SendAccountActiveEmail(const CSP2A_ACCOUNT_ACTIVE_INFO* account_info)
{
    Json::Value root_value;
    Json::Value itemData;
    
    itemData["email_type"] = "account_active";
    itemData["user"] = account_info->szUserName;
    itemData["email"] = account_info->szEmail;
    itemData["enable"] = account_info->nActive;
    itemData["time"] = account_info->expire_time;
    itemData["subscription"] = account_info->subscription;
    itemData["language"] = getEmailLanguage(EMAIL_ACCOUNT_ACTIVE, account_info->szEmail);

    std::string oem_name;
    if (strlen(account_info->dis_uuid) > 0)
    {
        DistributorInfoSt dis_info;
        if (0 == dbinterface::DistributorInfo::GetDistributorInfoByDisUUID(account_info->dis_uuid, dis_info))
        {
            oem_name = dis_info.oem_name;
        }
    }
    root_value["OEM"] = oem_name.empty() ? gstCSADAPTConf.oem_name : oem_name;
    
    // 获取社区信息
    EmailInfo email_info;
    dbinterface::ResidentPersonalAccount::GetEmailInfoByAccount(email_info, account_info->szEmail);
    itemData["community"] = email_info.community;
    
    AK_LOG_INFO << "[SendAccountActiveEmail] Send account active email. enable:" << account_info->nActive 
                << " subscription:" << account_info->subscription 
                << " email:" << account_info->szEmail 
                << " username:" << account_info->szUserName 
                << " OEM:" << root_value["OEM"].asString();
    
    sendEmailNotification(root_value, itemData, account_info->szEmail);
    return 0;
}

int CIPCControl::SendPmAccountActiveEmail(const CSP2A_ACCOUNT_ACTIVE_INFO* account_info)
{
    Json::Value root_value;
    Json::Value itemData;
    root_value["OEM"] = gstCSADAPTConf.oem_name;
    
    itemData["email_type"] = "pm_app_active";
    itemData["user"] = account_info->szUserName;
    itemData["email"] = account_info->szEmail;
    itemData["enable"] = account_info->nActive;
    itemData["time"] = account_info->expire_time;
    itemData["subscription"] = account_info->subscription;
    itemData["language"] = getEmailLanguage(EMAIL_PM_APP_ACCOUNT_ACTIVE, account_info->szEmail);
    
    // 获取社区信息
    EmailInfo email_info;
    dbinterface::ResidentPersonalAccount::GetEmailInfoByAccount(email_info, account_info->account);
    
    if (strlen(email_info.oem_name) > 0)
    {
        root_value["OEM"] = email_info.oem_name;
    }
    itemData["community"] = email_info.community;
    
    AK_LOG_INFO << "[SendPmAccountActiveEmail] Send PM account active email. enable:" << account_info->nActive 
                << " subscription:" << account_info->subscription 
                << " email:" << account_info->szEmail 
                << " username:" << account_info->szUserName 
                << " community:" << email_info.community
                << " OEM:" << root_value["OEM"].asString();
    
    sendEmailNotification(root_value, itemData, account_info->szEmail);
    return 0;
}

//分享key
int CIPCControl::SendShareTmpkeyEmail(const CSP2A_SHARE_TEMKEY_INFO* pstShareTempkey)
{
    Json::Value root_value;
    Json::Value itemData;
    
    root_value["OEM"] = gstCSADAPTConf.oem_name;

    DistributorInfoSt dis_info;
    if (0 == dbinterface::DistributorInfo::GetDisInfoByProjectId(pstShareTempkey->mng_id, dis_info))
    {
        if (strlen(dis_info.oem_name) > 0)
        {
            root_value["OEM"] = dis_info.oem_name;
        }
    }
    itemData["email_type"] = "share_tmpkey";
    itemData["key"] = pstShareTempkey->szTmpKey;
    itemData["msg"] = pstShareTempkey->szMsg;
    itemData["email"] = pstShareTempkey->szEmail;
    itemData["language"] = pstShareTempkey->szLanguage;
    itemData["counts_every"] = pstShareTempkey->szCountOrEvery;
    itemData["from_time"] = pstShareTempkey->szStartTime;
    itemData["until_time"] = pstShareTempkey->szStopTime;
    itemData["qrcode_body"] = pstShareTempkey->szQRCodeBody;

    std::string community;
    dbinterface::AccountInfo account;
    if (0 == dbinterface::Account::GetAccountById(pstShareTempkey->mng_id, account))
    {
        if (account.grade == AccountGrade::COMMUNITY_MANEGER_GRADE)
        {
            community = account.location;
        }
    }
    itemData["community"] = community;

    AK_LOG_INFO << "[SendShareTmpkeyEmail] Send share tmpkey email. email:" << pstShareTempkey->szEmail 
                << " key:" << pstShareTempkey->szTmpKey 
                << " community:" << community 
                << " OEM:" << root_value["OEM"].asString();

    sendEmailNotification(root_value, itemData, pstShareTempkey->szEmail);
    return 0;
}

/*
//remote opendoor
int CIPCControl::SendRemoteOpenDoor(const CSP2A_REMOTE_OPENDDOR_INFO* pstRemoteOpenDoor)
{
    AK::Server::P2PAdaptRemoteOpenDoorMsg msg;
    msg.set_mac(pstRemoteOpenDoor->mac);
    msg.set_uid(pstRemoteOpenDoor->uid);
    msg.set_relay(pstRemoteOpenDoor->relay);
    msg.set_msg_traceid(pstRemoteOpenDoor->trace_id);
    msg.set_repost_mac(pstRemoteOpenDoor->repost_mac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_REMOTE_OPENDOOR);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}
*/

//创建物业
int CIPCControl::SendCreatePropertyWork(const CSP2A_CREATE_PROPERTY_WORK_INFO* pstCreateProperty)
{
    Json::Value root_value;
    Json::Value itemData;

    root_value["OEM"] = gstCSADAPTConf.oem_name;
    
    std::string oem_name;
    if (strlen(pstCreateProperty->dis_uuid) > 0)
    {
        DistributorInfoSt dis_info;
        if (0 == dbinterface::DistributorInfo::GetDistributorInfoByDisUUID(pstCreateProperty->dis_uuid, dis_info))
        {
            oem_name = dis_info.oem_name;
        }
    }
    root_value["OEM"] = oem_name.empty() ? gstCSADAPTConf.oem_name : oem_name;
    
    itemData["email_type"] = "create_property";
    itemData["user"] = pstCreateProperty->szUserName;
    itemData["email"] = pstCreateProperty->szEmail;
    itemData["password"] = pstCreateProperty->szPassword;
    itemData["language"] = getEmailLanguage(EMAIL_CREATE_PROPERTY_WORK, pstCreateProperty->szEmail);
    
    AK_LOG_INFO << "[SendCreatePropertyWork] Send create property work email. user:" << pstCreateProperty->szUserName 
                << " email:" << pstCreateProperty->szEmail 
                << " OEM:" << root_value["OEM"].asString();
    
    sendEmailNotification(root_value, itemData, pstCreateProperty->szEmail);
    return 0;
}
//enduser续费成功
int CIPCControl::SendRenewServerEmail(const std::vector<CSP2A_UID_RENEW_SRV_INFO>& uid_infos)
{
    for (const auto& uid_info : uid_infos)
    {
        Json::Value root_value;
        root_value["OEM"] = gstCSADAPTConf.oem_name;
        
        Json::Value item_data;
        item_data["email_type"] = "account_renew";
        item_data["user"] = uid_info.szUserName;
        item_data["email"] = uid_info.szEmail;
        item_data["time"] = uid_info.szTime;
        item_data["type"] = uid_info.type;
        item_data["language"] = getEmailLanguage(EMAIL_RENEW_SERVER, uid_info.szEmail);
        
        EmailInfo email_info;
        dbinterface::ResidentPersonalAccount::GetEmailInfoByAccount(email_info, uid_info.account);

        if (strlen(email_info.oem_name) > 0)
        {
            root_value["OEM"] = email_info.oem_name;
        }
        item_data["community"] = email_info.community;
        
        sendEmailNotification(root_value, item_data, uid_info.szEmail);
        
        AK_LOG_INFO << "[SendRenewServerEmail] renew server, username:" << uid_info.szUserName 
                    << " email:" << uid_info.szEmail 
                    << " community:" << item_data["community"].asString()
                    << " OEM:" << root_value["OEM"].asString();
    }
    return 0;
}

//pm app续费操作
int CIPCControl::SendPmRenewServerEmail(const CSP2A_UID_PM_RENEW_SRV_INFO& uid_info)
{
    Json::Value root_value;
    root_value["OEM"] = gstCSADAPTConf.oem_name;
    
    Json::Value item_data;
    item_data["email_type"] = "pm_account_renew";
    item_data["user"] = uid_info.username;
    item_data["email"] = uid_info.email;
    item_data["time"] = uid_info.time;
    item_data["type"] = uid_info.type;
    item_data["language"] = getEmailLanguage(EMAIL_PM_APP_RENEW_SERVER, uid_info.email);
    
    EmailInfo email_info;
    dbinterface::ResidentPersonalAccount::GetEmailInfoByAccount(email_info, uid_info.account);
   
    if (strlen(email_info.oem_name) > 0)
    {
        root_value["OEM"] = email_info.oem_name;
    }
    
    item_data["community"] = email_info.community;
    
    sendEmailNotification(root_value, item_data, uid_info.email);
    
    AK_LOG_INFO << "[SendPmRenewServerEmail] pm renew server, username:" << uid_info.username 
                << " email:" << uid_info.email 
                << " community:" << email_info.community 
                << " time:" << uid_info.time
                << " OEM:" << root_value["OEM"].asString();
    return 0;
}

//通知PM社区账号多少过期
int CIPCControl::SendPmEmail(const CSP2A_PM_INFO* pstPmInfo)
{
    AK::Server::P2PAdaptPMAccountWillExpireMsg msg;
    msg.set_community(pstPmInfo->szCommunity);
    msg.set_email(pstPmInfo->szEmail);
    msg.set_pm_name(pstPmInfo->szName);
    msg.set_account_num(pstPmInfo->nAccountNum);
    msg.set_before(pstPmInfo->nBefore);
    msg.set_list(pstPmInfo->list);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PM_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}


int CIPCControl::SendAlexaLogin(CSP2A_ALEXA_LOGIN_INFO* pst_alexa_login)
{
    AK::Server::GroupAdaptAlexaLoginMsg msg;
    msg.set_node(pst_alexa_login->node);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_ALEXA_LOGIN_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendAlexaSetArming(CSP2A_ALEXA_SET_ARMING_INFO* pst_alexa_set_arming)
{
    AK::Server::P2PAdaptAlexaSetArmingMsg msg;
    msg.set_mac(pst_alexa_set_arming->mac);
    msg.set_mode(pst_alexa_set_arming->mode);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_ALEXA_SET_ARMING_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//落地过期了
int CIPCControl::SendPhoneExpire(const CSP2A_DEV_APP_EXPIRE* pstExpire)
{
    AK::Server::P2PAdaptPhoneExpireMsg msg;
    msg.set_user_name(pstExpire->szUserName);
    msg.set_email(pstExpire->szEmail);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PHONE_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//落地即将过期了
int CIPCControl::SendPhoneWillExpire(const CSP2A_DEV_APP_EXPIRE* pstExpire)
{
    AK::Server::P2PAdaptPhoneWillExpireMsg msg;
    msg.set_user_name(pstExpire->szUserName);
    msg.set_email(pstExpire->szEmail);
    msg.set_before(pstExpire->nBefore);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PHONE_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知Installer落地即将过期了
int CIPCControl::SendInstallerPhoneWillExpire(const CSP2A_INSTALLER_EXPIRE* pstExpire)
{
    AK::Server::P2PAdaptInstallerPhoneWillExpireMsg msg;
    msg.set_user_name(pstExpire->szUserName);
    msg.set_email(pstExpire->szEmail);
    msg.set_count(pstExpire->nCount);
    msg.set_before(pstExpire->nBefore);
    msg.set_list(pstExpire->list);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_INSTALLER_PHONE_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知Installer账号即将过期了
int CIPCControl::SendInstallerAppWillExpire(const CSP2A_INSTALLER_EXPIRE* pstExpire, std::string community)
{
    AK::Server::P2PAdaptInstallerAppWillExpireMsg msg;
    msg.set_user_name(pstExpire->szUserName);
    msg.set_email(pstExpire->szEmail);
    msg.set_count(pstExpire->nCount);
    msg.set_community(community);
    msg.set_before(pstExpire->nBefore);
    msg.set_list(pstExpire->list);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_INSTALLER_APP_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知PM高级功能即将过期了
int CIPCControl::SendPMFeatureWillExpire(const CSP2A_PM_EXPIRE* pstExpire)
{
    AK::Server::P2PAdaptPmFeatureWillExpireMsg msg;
    msg.set_user_name(pstExpire->username);
    msg.set_email(pstExpire->email);
    msg.set_before(pstExpire->nbefore);
    msg.set_location(pstExpire->community);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PM_FEATURE_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知Installer高级功能即将过期了
int CIPCControl::SendInstallerFeatureWillExpire(const CSP2A_INSTALLER_EXPIRE* pstExpire)
{
    AK::Server::P2PAdaptInstallerFeatureWillExpireMsg msg;
    msg.set_user_name(pstExpire->szUserName);
    msg.set_email(pstExpire->szEmail);
    msg.set_before(pstExpire->nBefore);
    msg.set_location(pstExpire->community);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_INSTALLER_FEATURE_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}



//创建远程连接
int CIPCControl::SendCreateRemoteDevContorl(CSP2A_CREATE_REMOTE_DEV_CONTORL_INFO& info)
{
    AK::Server::P2PAdaptCreateRemoteDevContorlMsg msg;
    msg.set_user(info.user);
    msg.set_password(info.password);
    msg.set_port(info.port);
    msg.set_mac(info.mac);
    msg.set_ssh_proxy_domain(info.ssh_proxy_domain);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_CREATE_REMOTE_DEV_CONTORL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知csmain刷新conn缓存
int CIPCControl::NotifyRefreshConnCache(CSP2A_REFRESH_CACHE& info)
{
    AK::Server::GroupAdaptNotifyRefreshConnCache msg;
    msg.set_mac(info.mac);
    msg.set_node(info.node);
    msg.set_type(info.type);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_REFRESH_CONN_CACHE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//csadapt通知csmain,发送keysend
int CIPCControl::SendDevFileChange(CSP2A_DEV_FILE_CHANGE* dev_change)
{
    AK::Server::P2PAdaptNotifyFileChangeMsg msg;
    msg.set_mac(dev_change->mac);
    msg.set_type(dev_change->type);
    msg.set_msg_traceid(dev_change->traceid);
    msg.set_file_path(dev_change->file_path);
    msg.set_file_md5(dev_change->file_md5);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_FILE_CHANGE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知PM社区账号多少过期
int CIPCControl::SendPmAppAccountWillBeExpireEmail(const CSP2A_PM_INFO* pstPmInfo)
{
    AK::Server::P2PAdaptPMAppAccountWillExpireMsg msg;
    msg.set_community(pstPmInfo->szCommunity);
    msg.set_email(pstPmInfo->szEmail);
    msg.set_pm_name(pstPmInfo->szName);
    msg.set_account_num(pstPmInfo->nAccountNum);
    msg.set_before(pstPmInfo->nBefore);
    msg.set_list(pstPmInfo->list);
    AK_LOG_INFO << "Send SendPmAppAccountWillBeExpireEmail=" << msg.DebugString();
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PM_APP_ACCOUNT_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知PM社区账号多少过期
int CIPCControl::SendPmAppAccountExpireEmail(const CSP2A_PM_INFO* pstPmInfo)
{
    AK::Server::P2PAdaptPMAppAccountExpireMsg msg;
    msg.set_community(pstPmInfo->szCommunity);
    msg.set_email(pstPmInfo->szEmail);
    msg.set_pm_name(pstPmInfo->szName);
    msg.set_account_num(pstPmInfo->nAccountNum);
    msg.set_list(pstPmInfo->list);
    AK_LOG_INFO << "Send SendPmAppAccountExpireEmail=" << msg.DebugString();
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PM_APP_ACCOUNT_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::PushLinKerText(const PersoanlMessageSend& text_msg, const LINKER_NORMAL_MSG &linker_msg)
{
    Json::Value item;
    Json::FastWriter w;
    FormatLinkerJsonData(linker_msg, item);
    item["title"] = text_msg.text_message.title;
    item["content"] = text_msg.text_message.content;
    item["notice_name"] = text_msg.text_message.from;
    std::string data_json = w.write(item);
    SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_MESSAGE, data_json, linker_msg.account_uuid);
    return 0;
}

void CIPCControl::FormatLinkerJsonData(const LINKER_NORMAL_MSG &linker_msg, Json::Value &item)
{
    item["dev_uuid"] = linker_msg.dev_uuid;
    item["dev_name"] = linker_msg.dev_name;
    item["dev_type"] = linker_msg.dev_type;
    item["dev_grade"] = linker_msg.dev_grade;
    item["account_uuid"] = linker_msg.account_uuid;
    item["node_uuid"] = linker_msg.node_uuid;
    item["account_name"] = linker_msg.account_name;
    item["language"] = linker_msg.language;
    item["project_type"] = linker_msg.project_type;
    item["project_uuid"] = linker_msg.project_uuid;
    std::time_t t = std::time(0);
    item["timestamp"] = GetCurrentMilliTimeStamp();
    item["ins_uuid"] = linker_msg.ins_uuid;
    item["enable_smarthome"] = linker_msg.enable_smarthome;
}

int CIPCControl::SendLinKerCommonMsg(int msg_type, const std::string &data_json, const std::string &key)
{
    AK::Linker::P2PRouteLinker msg;
    msg.set_message_type(msg_type);
    msg.set_msg_json(data_json);
    msg.set_key(key);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_GROUP_PUSH_CSLINKER_COMMON_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendUserAddNewSite(const CSP2A_PER_ADD_NEWSITE& per_add_new_site)
{
    Json::Value root_value;
    Json::Value item_data;
    root_value["OEM"] = gstCSADAPTConf.oem_name;
    
    item_data["email_type"] = "user_add_new_site";
    item_data["send_type"] = per_add_new_site.send_type;
    item_data["name"] = per_add_new_site.name;
    item_data["project_name"] = per_add_new_site.project_name;
    item_data["email"] = per_add_new_site.email;
    item_data["apt_num"] = per_add_new_site.apt_num;
    
    // 根据角色确定语言获取方式
    EmailType email_type_for_language = EMAIL_ADD_NEW_SITE;
    if (per_add_new_site.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        email_type_for_language = EMAIL_PM_ADD_NEW_SITE;
    }
    item_data["language"] = getEmailLanguage(email_type_for_language, per_add_new_site.email);
    
    DistributorInfoSt dis_info;
    if (0 == dbinterface::DistributorInfo::GetDisInfoByUserAccount(per_add_new_site.main_site_account, project::RESIDENCE, dis_info))
    {
        if (strlen(dis_info.oem_name) > 0)
        {
            root_value["OEM"] = dis_info.oem_name;
        }
    }
    
    sendEmailNotification(root_value, item_data, per_add_new_site.email);
    
    AK_LOG_INFO << "[SendUserAddNewSite] notify user_add_new_site, project_name:" << per_add_new_site.project_name 
                << " email:" << per_add_new_site.email 
                << " OEM:" << root_value["OEM"].asString();
    return 0;
}

int CIPCControl::SendPmWebLinkNewSites(const CSP2A_PM_LINK_NEWSITES& pm_link_new_sites)
{
    Json::Value root_value;
    Json::Value item_data;
    root_value["OEM"] = gstCSADAPTConf.oem_name;
    
    item_data["email_type"] = "pm_link_new_sites";
    item_data["comm_name_list"] = pm_link_new_sites.comm_name_list;
    item_data["office_name_list"] = pm_link_new_sites.office_name_list;
    item_data["name"] = pm_link_new_sites.name;
    item_data["email"] = pm_link_new_sites.email;
    item_data["language"] = getEmailLanguage(EMAIL_PM_LINK_NEW_SITES, pm_link_new_sites.email);
    
    DistributorInfoSt dis_info;
    if (0 == dbinterface::DistributorInfo::GetDistributorInfoByPMAccountUUID(pm_link_new_sites.account_uuid, dis_info))
    {
        if (strlen(dis_info.oem_name) > 0)
        {
            root_value["OEM"] = dis_info.oem_name;
        }
    }
    
    sendEmailNotification(root_value, item_data, pm_link_new_sites.email);
    AK_LOG_INFO << "[SendPmWebLinkNewSites] notify pm_link_new_sites, comm_name_list:" << pm_link_new_sites.comm_name_list 
                << " office_name_list:" << pm_link_new_sites.office_name_list 
                << " email:" << pm_link_new_sites.email 
                << " OEM:" << root_value["OEM"].asString();
    return 0;
}

int CIPCControl::SendPmWebCreateUidMail(const CSP2A_USER_CREATE_INFO& user_create_info)
{
    Json::Value root;
    Json::Value item_data;
    root["OEM"] = gstCSADAPTConf.oem_name;

    item_data["email_type"] = "pm_web_create_uid";
    item_data["uid"] = user_create_info.szUser;
    item_data["email"] = user_create_info.szEmail;
    item_data["pwd"] = user_create_info.szPwd;
    item_data["qrcode_body"] = user_create_info.szQRCodeBody;
    item_data["qrcode_url"] = user_create_info.szQRCodeUrlPath;
    item_data["gw_code"] = std::to_string(gstCSADAPTConf.gateway_num);
    item_data["user"] = user_create_info.szEmail; // 邮件模板的user是邮箱
    item_data["language"] = getEmailLanguage(EMAIL_PM_WEB_CREATE_UID, user_create_info.szEmail);

    DistributorInfoSt dis_info;
    if (0 == dbinterface::DistributorInfo::GetDistributorInfoByDisUUID(user_create_info.szDisUuid, dis_info))
    {
        if (strlen(dis_info.oem_name) > 0)
        {
            root["OEM"] = dis_info.oem_name;
        }
    }
    sendEmailNotification(root, item_data, user_create_info.szEmail);
    
    AK_LOG_INFO << "[SendPmWebCreateUidMail] Notify pm_web_create_uid, email:" 
                << user_create_info.szEmail  
                << " OEM:" << root["OEM"].asString();
    return 0;
}

int CIPCControl::SendPmWebChangePwdMail(const CSP2A_USER_CREATE_INFO& user_create_info)
{
    Json::Value root;
    Json::Value item_data;

    root["OEM"] = gstCSADAPTConf.oem_name;
    
    // 设置邮件类型和数据
    item_data["email_type"] = "pm_web_change_pwd";
    item_data["email"] = user_create_info.szEmail;
    item_data["pwd"] = user_create_info.szPwd;
    item_data["qrcode_body"] = user_create_info.szQRCodeBody;
    item_data["qrcode_url"] = user_create_info.szQRCodeUrlPath;
    item_data["gw_code"] = std::to_string(gstCSADAPTConf.gateway_num);
    item_data["user"] = user_create_info.szEmail; // 邮件模板的user是邮箱
    item_data["language"] = getEmailLanguage(EMAIL_PM_WEB_CHANGE_PWD, user_create_info.szEmail);

    // 获取OEM信息
    DistributorInfoSt dis_info;
    if (0 == dbinterface::DistributorInfo::GetDistributorInfoByPMAccountUUID(user_create_info.szPmUUID, dis_info))
    {
        if (strlen(dis_info.oem_name) > 0)
        {
            root["OEM"] = dis_info.oem_name;
        }
    }
    
    sendEmailNotification(root, item_data, user_create_info.szEmail);
    
    AK_LOG_INFO << "[SendPmWebChangePwdMail] Notify pm_web_change_pwd, email:" << user_create_info.szEmail 
                << " OEM:" << root["OEM"].asString();
    return 0;
}

int CIPCControl::SendCommonEmailCode(const CSP2A_SEND_VERFICATION_CODE& verification_code)
{
    Json::Value root;
    Json::Value item_data;

    root["OEM"] = gstCSADAPTConf.oem_name;

    item_data["email_type"] = verification_code.type;
    item_data["code"] = verification_code.code;
    item_data["name"] = verification_code.name;
    item_data["email"] = verification_code.email;
    item_data["language"] = verification_code.language;

    // 获取OEM信息
    std::string oem_name;
    if (strlen(verification_code.dis_uuid) > 0)
    {
        DistributorInfoSt dis_info;
        if (0 == dbinterface::DistributorInfo::GetDistributorInfoByDisUUID(verification_code.dis_uuid, dis_info))
        {
            oem_name = dis_info.oem_name;
        }
    }
    root["OEM"] = oem_name.empty() ? gstCSADAPTConf.oem_name : oem_name;

    sendEmailNotification(root, item_data, verification_code.email);
    
    AK_LOG_INFO << "[SendCommonEmailCode] Notify send_email_code, code:" << verification_code.code 
                << " email:" << verification_code.email 
                << " OEM:" << root["OEM"].asString();
    return 0;
}

int CIPCControl::SendCommonSmsCode(const CSP2A_SEND_VERFICATION_CODE& verification_code)
{
    AK::Server::P2PSendCodeToMobile send_sms_code;
    send_sms_code.set_area_code(verification_code.phone_code);
    send_sms_code.set_phone(verification_code.mobile_number);
    send_sms_code.set_language(verification_code.language);
    send_sms_code.set_code(verification_code.code);
    send_sms_code.set_type(verification_code.type);
    AK_LOG_INFO << "SendCommonSmsCode=" << send_sms_code.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&send_sms_code);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_CODE_TO_MOBILE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);    
    return 0;
}

void CIPCControl::SendRequestDevDelLog(const std::string& mac)
{
    AK::Server::P2PSendRequestDevDelLog msg;
    msg.set_mac(mac);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_REQUEST_DEV_DEL_LOG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);    
}

std::string CIPCControl::getEmailLanguage(const EmailType email_type, const std::string& email)
{
    std::string language = "en";

    if (email_type == EMAIL_CREATE_UID
            || email_type == EMAIL_CHANGE_PWD
            || email_type == EMAIL_RESET_PWD
            || email_type == EMAIL_ACCOUNT_ACTIVE
            || email_type == EMAIL_RENEW_SERVER
            || email_type == EMAIL_ADD_NEW_SITE
            || email_type == EMAIL_DEV_APP_WILLBE_EXPIRE
       )
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUserAccountFromMaster(email, account))
        {
            language = account.language;
        }
    }
    else if (email_type == EMAIL_CREATE_PROPERTY_WORK
             || email_type == EMAIL_PM_APP_CREATE_UID
             || email_type == EMAIL_PM_APP_ACCOUNT_ACTIVE
             || email_type == EMAIL_PM_APP_RENEW_SERVER
             || email_type == EMAIL_PM_APP_RESET_PWD
             || email_type == EMAIL_PM_WEB_CREATE_UID
             || email_type == EMAIL_PM_WEB_CHANGE_PWD
             || email_type == EMAIL_PM_ADD_NEW_SITE
             || email_type == EMAIL_PM_LINK_NEW_SITES
            )
    {
        dbinterface::AccountInfo account;
        if (0 == dbinterface::Account::GetAccountFromMasterByEmail(email, account))
        {
            language = account.language;
        }
    }
    
    return language;
}

void CIPCControl::sendEmailNotification(const Json::Value& root_value, const Json::Value& item_data, const std::string& email)
{
    Json::Value root_copy = root_value;
    
    // 设置固定字段
    root_copy["app_type"] = "email";
    root_copy["ver"] = "1";
    
    Json::FastWriter writer;
    root_copy["data"] = writer.write(item_data);
    std::string data_json = writer.write(root_copy);

    // 发送邮件通知
    AK::Adapt::SendEmailNotifyMsg send_mail;
    send_mail.set_payload(data_json);
    send_mail.set_key(email);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&send_mail);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_EMAIL_NOTIFY);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
}

int CIPCControl::SendInsAppFeedbackEmail(const std::string& feedback_uuid)
{
    AK_LOG_INFO << "[SendInsAppFeedbackEmail] Processing feedback: " << feedback_uuid;
    
    // 1. 查询反馈信息
    InsAppFeedbackInfo feedback_info;
    if (0 != dbinterface::InsAppFeedback::GetInsAppFeedbackByUUID(feedback_uuid, feedback_info))
    {
        AK_LOG_WARN << "[SendInsAppFeedbackEmail] Failed to get feedback info: " << feedback_uuid;
        return -1;
    }
    
    // 2. 通过AccountUserInfoUUID查询安装商登录账号
    UserInfoAccount installer_userinfo;
    if (0 != dbinterface::AccountUserInfo::GetAccountUserInfoByUUID(feedback_info.account_user_info_uuid, installer_userinfo))
    {
        AK_LOG_WARN << "[SendInsAppFeedbackEmail] Failed to get installer userinfo: " << feedback_info.account_user_info_uuid;
        return -1;
    }
    
    // 3. 通过UserInfoUUID获取installer的AccountUUID
    std::string installer_account_uuid;
    if (0 != dbinterface::AccountMap::GetAccountUUIDByUserInfoUUID(feedback_info.account_user_info_uuid, installer_account_uuid))
    {
        AK_LOG_WARN << "[SendInsAppFeedbackEmail] Failed to get installer account uuid";
        return -1;
    }
    
    Json::Value root_value;
    root_value["OEM"] = gstCSADAPTConf.oem_name;
    
    Json::Value item_data;
    item_data["ins_name"] = installer_userinfo.account;
    item_data["contact_email"] = feedback_info.contact_email;
    item_data["content"] = feedback_info.content;
    item_data["file_list"] = feedback_info.file_list;
    item_data["email_type"] = "ins_feedback";
    item_data["project_type"] = 0;
    item_data["user"] = installer_userinfo.account;

    // 4. 查找distributor 
    dbinterface::AccountInfo installer_account;
    if (0 == dbinterface::Account::GetAccountByUUID(installer_account_uuid, installer_account))
    {
        dbinterface::AccountInfo distributor_account;
        if (0 == dbinterface::Account::GetAccountById(installer_account.parent_id, distributor_account))
        {
            CustomerServiceInfo dis_cs;
            if (0 == dbinterface::CustomerService::GetCustomerServiceByMngAccount(distributor_account.account, dis_cs) && 
                dis_cs.receive_feedback == 1)
            {
                Json::Value dis_item = item_data;
                dis_item["send_to_type"] = "dis";
                dis_item["dis_name"] = distributor_account.account;
                dis_item["language"] = distributor_account.language;

                DistributorInfoSt dis_info;
                if (0 == dbinterface::DistributorInfo::GetDistributorInfoByDisUUID(distributor_account.uuid, dis_info))
                {
                    if (strlen(dis_info.oem_name) > 0)
                    {
                        root_value["OEM"] = dis_info.oem_name;
                    }
                }
                
                AK_LOG_INFO << "[SendInsAppFeedbackEmail] Send to distributor: " << distributor_account.account;
                sendEmailNotification(root_value, dis_item, dis_cs.email);
                
                // 记录接收者
                dbinterface::InsAppFeedbackReceiverList::AddAppFeedbackReceiver(distributor_account.account, feedback_uuid);
            }
        }
        
    }
    
    // 5. 查找sub-distributor
    SubDisMngListInfo sub_dis_info;
    if (0 == dbinterface::SubDisMngList::GetSubDisMngListByInstallerUUID(installer_account_uuid, sub_dis_info))
    {
        // 获取sub-distributor账号信息
        dbinterface::AccountInfo sub_dis_account;
        if (0 == dbinterface::Account::GetAccountByUUID(sub_dis_info.distributor_uuid, sub_dis_account))
        {
            // 获取客服信息
            CustomerServiceInfo sub_dis_cs;
            if (0 == dbinterface::CustomerService::GetCustomerServiceByMngAccount(sub_dis_account.account, sub_dis_cs) && 
                sub_dis_cs.receive_feedback == 1)
            {
                Json::Value sub_dis_item = item_data;
                sub_dis_item["send_to_type"] = "dis";
                sub_dis_item["dis_name"] = sub_dis_account.account;
                sub_dis_item["language"] = sub_dis_account.language;
                 
                AK_LOG_INFO << "[SendInsAppFeedbackEmail] Send to sub-distributor: " << sub_dis_account.account;
                sendEmailNotification(root_value, sub_dis_item, sub_dis_cs.email);
                 
                // 记录接收者
                dbinterface::InsAppFeedbackReceiverList::AddAppFeedbackReceiver(sub_dis_account.account, feedback_uuid);
            }
        }
    }
    
    Json::Value company_item = item_data;
    company_item["cc_list"] = "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>";
    company_item["language"] = "en";
    company_item["send_to_type"] = "company";
    
    AK_LOG_INFO << "[SendInsAppFeedbackEmail] Send to company support";
    sendEmailNotification(root_value, company_item, "<EMAIL>");
    
    return 0;
}

