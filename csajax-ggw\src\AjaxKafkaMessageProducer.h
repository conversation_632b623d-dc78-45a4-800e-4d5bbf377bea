#ifndef __CSAJAX_GGW_KAFKA_MESSAGE_PRODUCER_H__
#define __CSAJAX_GGW_KAFKA_MESSAGE_PRODUCER_H__

#include "kafka/AkcsKafkaProducer.h"
#include <memory>

class AjaxNotifyMsg
{
public:
    AjaxNotifyMsg() {}
    ~AjaxNotifyMsg() {}

    void InitKafkaProducer(const std::string& ip, const std::string& topic);
    void ProduceMsg(const std::string& key, const std::string& msg);
private:
    std::shared_ptr<AkcsKafkaProducer> kafka_producer_;
};

void ProduceAjaxNotifyMsg(const std::string &key, const std::string& msg);

#endif