#include "util.h"
#include "AdaptDef.h"
#include "AkLogging.h"
#include "KafkaConsumerPushTopicHandle.h"
#include "NewOfficeSmsNotify.h"
#include "NewOfficeTransferEmail.h"

extern CSADAPT_CONF gstCSADAPTConf;

void HandleKafkaPushTopicMsg::Init()
{
    RegNewOfficeHandle("sms", NewOfficeSmsNotify::Handle);
    RegNewOfficeHandle("email", NewOfficeTransferEmail::Handle);
}

void HandleKafkaPushTopicMsg::RegNewOfficeHandle(const std::string& msg_type, HandleKafkaPushNotifyFunc func)
{
    functions_.insert(std::map<std::string, HandleKafkaPushNotifyFunc>::value_type(msg_type, func));
}

void HandleKafkaPushTopicMsg::StartKafkaConsumer()
{
    kafak_.SetParma(
        gstCSADAPTConf.kafka_broker_ip, gstCSADAPTConf.appbackend_push_topic,
        gstCSADAPTConf.appbackend_push_group, gstCSADAPTConf.appbackend_push_thread_num
    );

    kafak_.SetConsumerCb(
        std::bind(
            &HandleKafkaPushTopicMsg::HandleKafkaMessage, this, std::placeholders::_1,
            std::placeholders::_2, std::placeholders::_3, std::placeholders::_4
        )
    );

    kafak_.Start();
}

bool HandleKafkaPushTopicMsg::HandleTcpMessage(const std::string& org_msg)
{
    KafkaWebMsgParse msg(org_msg);
    if (!msg.ParseOk())
    {
        return true;
    }
    
    auto it = functions_.find(msg.msg_type_);
    if (it == functions_.end())
    {
        AK_LOG_WARN << "Not found msg_type = " << msg.msg_type_ << ", trace_id = " << msg.trace_id_;
        return true;
    }

    it->second(org_msg, msg.msg_type_, msg.kv_, msg.oem_name_);
    return true;
}

bool HandleKafkaPushTopicMsg::HandleKafkaMessage(uint64_t partition, uint64_t offset, const std::string& key, const std::string& org_msg)
{
    KafkaWebMsgParse msg(org_msg);
    if (!msg.ParseOk())
    {
        return true;
    }

    auto it = functions_.find(msg.msg_type_);
    if (it == functions_.end())
    {
        AK_LOG_WARN << "Not found msg_type = " << msg.msg_type_ << ", trace_id = " << msg.trace_id_;
        return true;
    }

    it->second(org_msg, msg.msg_type_, msg.kv_, msg.oem_name_);
    return true;
}
