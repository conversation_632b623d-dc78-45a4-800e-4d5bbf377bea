<?php

// 生成时替换建表语句
$tableDefine= "
CREATE TABLE `CustomerService` (
  `MngAccount` char(64) NOT NULL COMMENT '区域管理员账号',
  `Phone` varchar(32) DEFAULT '',
  `Email` varchar(128) DEFAULT '',
  `ReceiveFeedback` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否接受用户的反馈邮件,0:不接收, 1:接收',
  `EmailForRentManager` char(255) DEFAULT '' COMMENT '接收rentManager通知的邮件地址,最多5个，用逗号分割',
  PRIMARY KEY (`MngAccount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8         
";

$headerDefine = "
#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include \"BasicDefine.h\"
#include \"ConnectionManager.h\"
#include \"dbinterface/InterfaceComm.h\"
";

// 表名
$tableName = getTableName($tableDefine);
// 结构体名
$structName = $tableName . "Info";
// 头文件定义
$hppDefine = getTableHeaderDefine($tableName);
// 头文件函数
$hppfunction = getHppFunctions($tableName, $tableDefine, $structName);
// 结构体
$tableStruct = getTableStruct($tableName, $tableDefine, $structName);
// 结构体参数名
$structParam = getStructParam($structName, $tableDefine);
// 表名转成下划线连接
$tableToUnderline = getStructParam($structName, $tableDefine);
// getTableFromSql定义
$getTableFromSqlDefine = getTableFromSqlDefine($tableName, $structName, $structParam);

// 构造hpp文件
$hppContent = "";
$hppContent .= "#ifndef $hppDefine\n";
$hppContent .= "#define $hppDefine\n";
$hppContent .= $headerDefine . "\n";
$hppContent .= $tableStruct . "\n\n";
$hppContent .= "namespace dbinterface {\n\n";
$hppContent .= "class $tableName\n{\n";
$hppContent .= "public:\n";
$hppContent .= "$hppfunction\n";
$hppContent .= "private:\n";
$hppContent .= "    $tableName() = delete;\n";
$hppContent .= "    ~$tableName() = delete;\n";
$hppContent .= "    $getTableFromSqlDefine;\n";
$hppContent .= "};\n\n}\n#endif";
file_put_contents("$tableName.h", $hppContent);

// 构造cpp文件
$cppHeaderDefine = "#include \"util.h\"
#include \"Rldb.h\"
#include \"RldbQuery.h\"
#include \"AkLogging.h\"
#include \"$tableName.h\"
";

// 字段名以,连接
$tableField = getTableFields($tableName, $tableDefine);
// sql查询字段
$sqlFieldSec = getSqlFieldSec($tableField, $tableToUnderline);
// getTableFromSql实现
$getTableFromSqlFunctionImplementation = getTableFromSqlFunctionImplementation($tableDefine, $tableName, $structName, $structParam);
// 
$cppfunction = getCppFunctions($tableName, $tableDefine, $structName, $tableToUnderline);


$cppContnet = "$cppHeaderDefine\n";
$cppContnet .= "namespace dbinterface {\n\n";
$cppContnet .= "$sqlFieldSec\n\n";
$cppContnet .= "$getTableFromSqlFunctionImplementation\n\n";
$cppContnet .= "$cppfunction\n}";

file_put_contents("$tableName.cpp", $cppContnet);


function getTableName($tableDefine)
{
    preg_match("/CREATE TABLE `([^`]+)`/", $tableDefine, $matches);
    $tableName = $matches[1];
    return $tableName;
}

function getTableHeaderDefine($input)
{
    // 使用正则表达式将单词分隔
    $output = preg_replace_callback('/([A-Z][a-z]*)/', function($matches) {
        return '_' . strtoupper($matches[1]);
    }, $input);

    // 如果开头有下划线，则去除
    if (substr($output, 0, 1) === '_') {
        $output = substr($output, 1);
    }

    return "__DB_" . $output . "_H__";
}

function convertColumnName($columnName) 
{
    $convertedName = strtolower(preg_replace('/(?<!^)([A-Z])/', '_$1', $columnName));
    $fixedName = str_replace('_u_u_i_d', '_uuid', $convertedName);
    $fixedName = str_replace('u_u_i_d', 'uuid', $fixedName);
    $fixedName = str_replace('i_d', 'id', $fixedName);
    if ($fixedName == "i_d") {
      $fixedName = "id";
    }
   // echo "columnName = $columnName, fixedName = $fixedName \n";
    return ltrim($fixedName, '_');
}

function camelCaseToSnakeCase($input) 
{
    return strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $input));
}

function getTableStruct($tableName, $tableDefine)
{
    // 解析表定义，提取字段名和字段类型
    preg_match_all("/`(\w+)` ([^ ]+)/", $tableDefine, $matches, PREG_SET_ORDER);

    // 定义C语言结构体
    $structDefinition = "typedef struct {$tableName}Info_T\n{";

    // 遍历匹配结果，生成C语言结构体成员
    foreach ($matches as $match) {
        $fieldName = $match[1];
        $fieldType = $match[2];
        
        // 排除CreateTime和UpdateTime字段
        if ($fieldName != 'CreateTime' && $fieldName != 'UpdateTime' &&  $fieldName != 'ID' &&  $fieldName != 'Version' && !strstr($fieldName, 'RBAC')) {
            $fieldName  = convertColumnName($fieldName);

            // 根据字段类型和长度生成对应的C语言结构体成员
            if (strpos($fieldType, 'int') !== false || strpos($fieldType, 'INT') !== false) {
                $structDefinition .= "\n    int $fieldName;";
            } elseif (preg_match("/char\((\d+)\)/", $fieldType, $charMatch) || preg_match("/CHAR\((\d+)\)/", $fieldType, $charMatch)) {
                $structDefinition .= "\n    char $fieldName" . "[{$charMatch[1]}];";
            } elseif (strpos($fieldType, 'time') !== false || strpos($fieldType, 'TIME') !== false) {
                $structDefinition .= "\n    char $fieldName" . "[32];";
            }
        }
    }

    // 默认构造函数
    $structDefinition .= "\n    {$tableName}Info_T() {\n";
    $structDefinition .= "        memset(this, 0, sizeof(*this));\n    }\n";
    $structDefinition .= "} {$tableName}Info;";
    
    return $structDefinition;
}

function getHppFunctions($tableName, $tableDefine, $structName)
{
    // 解析表定义，提取索引信息
    preg_match_all("/KEY `([^`]+)` \(([^)]+)\)/", $tableDefine, $matches, PREG_SET_ORDER);
    $structParam = convertColumnName(camelCaseToSnakeCase($structName));
    
    $functions = '';
    // 遍历匹配结果，生成函数
    foreach ($matches as $match) {
        $indexName = $match[1];
        $indexName = explode('_', $indexName)[0];
        $param = convertColumnName($indexName);

        $functionName = "    static int Get{$tableName}By{$indexName}(const std::string& $param, $structName& $structParam);";

        $functions .= $functionName . "\n";
    }

    return $functions;
}

function getCppFunctions($tableName, $tableDefine, $structName, $tableToUnderline)
{
     // 解析表定义，提取索引信息
     preg_match_all("/KEY `([^`]+)` \(([^)]+)\)/", $tableDefine, $matches, PREG_SET_ORDER);
     $structParam = convertColumnName(camelCaseToSnakeCase($structName));

     $functions = '';
     // 遍历匹配结果，生成函数
    foreach ($matches as $match) {
        $indexName = $match[1];
        $indexName = explode('_', $indexName)[0];
        $param = convertColumnName($indexName);

        $functionName = "int {$tableName}::Get{$tableName}By{$indexName}(const std::string& $param, $structName& $structParam)\n{\n";
        $functionName .= "    std::stringstream stream_sql;\n";
        $functionName .= "    stream_sql << \"select \" << {$tableToUnderline}_sec << \" from {$tableName} where {$indexName} = '\" << {$param} << \"'\";\n";
        $functionName .= "    GET_DB_CONN_ERR_RETURN(db_conn, -1);\n\n";
        $functionName .= "    CRldbQuery query(db_conn.get());\n";
        $functionName .= "    query.Query(stream_sql.str());\n";
        $functionName .= "    if (query.MoveToNextRow())\n    {\n";
        $functionName .= "        Get{$tableName}FromSql({$structParam}, query);\n    }\n";
        $functionName .= "    else\n    {\n        AK_LOG_WARN << \"get {$structName} by {$indexName} failed, {$indexName} = \" << {$param};\n";
        $functionName .= "        return -1;\n    }\n    return 0;\n}";
        $functions .= $functionName . "\n\n";
    }

    return $functions;
}

function getTableFields($tableName, $tableDefine)
{
    // 解析表定义，提取字段名和字段类型
    preg_match_all("/`(\w+)` ([^ ]+)/", $tableDefine, $matches, PREG_SET_ORDER);

    $fields = "\" ";
    
    foreach ($matches as $match) {
        $fieldName =$match[1];
        $fieldType = $match[2];

        // 排除CreateTime和UpdateTime字段
        if ($fieldName != 'CreateTime' && $fieldName != 'UpdateTime' &&  $fieldName != 'ID' &&  $fieldName != 'Version' && !strstr($fieldName, 'RBAC')) {
            // 根据字段类型和长度生成对应的C语言结构体成员
            if (strpos($fieldType, 'int') !== false || strpos($fieldType, 'INT') !== false) {
                $fields .= $fieldName . ",";
            } elseif (preg_match("/char\((\d+)\)/", $fieldType, $charMatch) || preg_match("/CHAR\((\d+)\)/", $fieldType, $charMatch)) {
                $fields .= $fieldName . ",";
            } elseif (strpos($fieldType, 'time') !== false || strpos($fieldType, 'TIME') !== false) {
                $fields .= $fieldName . ",";
            }
        }
    }
    return rtrim($fields, ",") . " \";";
}

function getStructParam($structName, $tableDefine)
{
    preg_match_all("/KEY `([^`]+)` \(([^)]+)\)/", $tableDefine, $matches, PREG_SET_ORDER);
    return convertColumnName(camelCaseToSnakeCase($structName));
}

function getSqlFieldSec($tableField, $tableToUnderline)
{
    return "static const std::string {$tableToUnderline}_sec = $tableField";
}

function getTableFromSqlDefine($tableName, $structName, $structParam)
{
    return "static void Get{$tableName}FromSql({$structName}& {$structParam}, CRldbQuery& query)";
}

function getTableFromSqlFunctionImplementation($tableDefine, $tableName, $structName, $structParam)
{
    $functionImplementation = "void {$tableName}::Get{$tableName}FromSql({$structName}& $structParam, CRldbQuery& query)\n{\n";
    
    // 解析表定义，提取字段名和字段类型
    preg_match_all("/`(\w+)` ([^ ]+)/", $tableDefine, $matches, PREG_SET_ORDER);
     
    $index = 0;
    foreach ($matches as $match) {
        $fieldName =$match[1];
        $fieldType = $match[2];

        // 排除CreateTime和UpdateTime字段
        if ($fieldName != 'CreateTime' && $fieldName != 'UpdateTime' &&  $fieldName != 'ID' &&  $fieldName != 'Version' && !strstr($fieldName, 'RBAC')) {
            $fieldName = $structParam . "." .convertColumnName($fieldName);
            
            // 根据字段类型和长度生成对应的C语言结构体成员
            if (strpos($fieldType, 'int') !== false || strpos($fieldType, 'INT') !== false) {
                $functionImplementation .= "    {$fieldName} = ATOI(query.GetRowData(" . $index++ . "));\n";
            } elseif (preg_match("/char\((\d+)\)/", $fieldType, $charMatch) || preg_match("/CHAR\((\d+)\)/", $fieldType, $charMatch)) {
                $functionImplementation .= "    Snprintf({$fieldName}, sizeof({$fieldName}), query.GetRowData(" . $index++ . "));\n";
            } elseif (strpos($fieldType, 'time') !== false || strpos($fieldType, 'TIME') !== false) {
                $functionImplementation .= "    Snprintf({$fieldName}, sizeof({$fieldName}), query.GetRowData(" . $index++ . "));\n";
            }
        }
    }

    $functionImplementation .= "    return;\n}";
    return $functionImplementation;
}

