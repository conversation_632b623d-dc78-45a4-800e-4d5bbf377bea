#ifndef __CSAJAX_KAFKA_MESSAGE_CONSUMER_H__
#define __CSAJAX_KAFKA_MESSAGE_CONSUMER_H__

#include "kafka/AkcsKafkaConsumer.h"
#include "Singleton.h"

class HandleAjaxKafkaNotifyMsg
{
public:
    // 单例
    friend class AKCS::Singleton<HandleAjaxKafkaNotifyMsg>;

    void Init();
    void StartKafkaConsumer();
    bool Status() { return kafka_.Status(); }
    bool HandleKafkaMessage(uint64_t partition, uint64_t offset, const std::string& key, const std::string& msg);

private:
    AkcsKafkaConsumer kafka_;
};

#endif