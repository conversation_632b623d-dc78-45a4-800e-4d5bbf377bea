#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "InsAppFeedbackReceiverList.h"
#include "util_string.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/UUID.h"

namespace dbinterface {

int InsAppFeedbackReceiverList::AddAppFeedbackReceiver(const std::string& receiver_account, const std::string& feedback_uuid)
{
    std::string server_tag = SystemSetting::GetServerTag();
    // 生成UUID
    std::string uuid;
    dbinterface::UUID::GenerateUUID(server_tag, uuid);
    
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    // 构造插入数据
    std::map<std::string, std::string> strMap;
    strMap.emplace("ReceiverAccount", receiver_account);
    // 如果feedback_uuid为空，就走数据库默认值
    if (!feedback_uuid.empty()) {
        strMap.emplace("FeedbackUUID", feedback_uuid);
    }
    strMap.emplace("UUID", uuid);
    
    std::map<std::string, int> intMap; // 如果有整型字段可以添加到这里
    
    // 使用InsertData方法插入数据
    int ret = db_conn->InsertData("InsAppFeedbackReceiverList", strMap, intMap);
    if (ret != 0)
    {
        AK_LOG_WARN << "Insert InsAppFeedbackReceiverList failed, ReceiverAccount = " << receiver_account 
                    << ", FeedbackUUID = " << feedback_uuid;
        return -1;
    }
    
    return 0;
}


} 