#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "EtcdCliMng.h"
#include "AkLogging.h"
#include <sstream>
#include "util.h"
extern CAkEtcdCliManager* g_etcd_cli_mng;

//各个应用组件共享的
const char *g_ak_srv_conf = "/ak"; //其中服务注册以/akcs开头,配置项以/akconf开头
const char *g_ak_srv = "/akcs/";
const char *g_web_domain_server = "akcs/web/domainServer/"; //其中服务注册以 akcs/web/domainServer/ 开头,配置项以 akcs/web/domainServer/ 开头
const char *g_conf_db_addr = "/akconf/db_addr"; //ip:port的格式
const char *g_conf_db_master_addr = "/akconf/db_master_addr"; //db主库地址，如web、csconfig是直连主库的
const char *g_conf_beanstalk_addr = "/akconf/beanstalk_addr";
const char *g_ak_srv_session = "/akcs/cssession/";
const char *g_ak_srv_nsqlookupd = "/akcs/nsqlookupd/innerip/http";
const char *g_ak_srv_cspush = "/akcs/cspush/innerip/apppush";
const char *g_ak_srv_adapt = "/akcs/csadapt/";
const char *g_ak_srv_route = "/akcs/csroute/";
const char *g_ak_srv_main = "/akcs/csmain/";
const char *g_ak_srv_cspbxrpc = "/akcs/cspbxrpc/";
const char *g_ak_srv_opensips = "/akcs/opensips/";
const char *g_ak_srv_freeswitch = "/akcs/freeswitch/innerip/";
const char *g_ak_srv_smg = "/akconf/smg/inner_addr";
const char *g_ak_srv_smg_alexa = "/akconf/smg/alexa_inner_server/";
const char *g_ak_srv_zipkin_kafka = "/akconf/zipkin/kafka/inner_addr";
const char *g_ak_srv_web_adapt_entry = "akcs/web/adaptEntry/appBackend";
const char *g_ak_srv_web_rpc_rbac = "akcs/web/domainServer/rbac";
const char *g_ak_srv_mqtt_inner_tcp = "/akconf/mqtt/inner_addr/tcp";
const char *g_ak_srv_mqtt_outer_tls = "/akconf/mqtt/outer_addr/tls";
const char *g_ak_srv_video_record = "akcs/csvideorecord/innerip";
const char *g_ak_srv_video_record_rpc = "akcs/csvideorecord/rpc";
const char *g_ak_srv_mqtt_inner_api_http = "/akconf/mqtt/inner_api_addr/http";
const char *g_ak_srv_yunji_robot_rpc = "akcs/csyunjirobot/rpc";

//各个应用组件独占的
const char *g_gate_config_service_key = "/akconf/csgate";
const char conf_cagte_limit_switch[] = "/akconf/csgate/limit_switch";

CAkEtcdCliManager *CAkEtcdCliManager::instance_ = nullptr;

CAkEtcdCliManager::CAkEtcdCliManager(const std::string& addr)
{
	std::string substr(",");
	SplitString(addr, substr, etcd_addrs_);
	if(etcd_addrs_.size() < 1)
	{
		AK_LOG_FATAL << "etcd addr format is invalid, addr is:" << addr;
        return;
	}
    etcd_cli_= new etcd::Client(etcd_addrs_);
    is_inner_need_register_again_ = false;
    is_outer_need_register_again_ = false;
    inner_lease_id_ = 0;
    outer_lease_id_ = 0;
    etcd_stat_ = false;
}

CAkEtcdCliManager* CAkEtcdCliManager::GetInstance(const std::string& addr)
{
    if(instance_ == nullptr)
    {
        instance_ = new CAkEtcdCliManager(addr);
    }
    return instance_;

}
void CAkEtcdCliManager::destroyInstance()
{
    if (instance_)
        delete instance_;
    instance_ = nullptr;
}


void CAkEtcdCliManager::UpdateEtcdAddrs(const std::vector<std::string>& addresss)
{
    etcd_cli_->EtcdSrvConnUpdate(addresss);
}

const std::string CAkEtcdCliManager::GetRandomPushSrv()
{
	std::stringstream stream;
    stream << "cspush server list is: ";
    etcd::Response resp_cspush = etcd_cli_->ls("/akcs/cspush/").get();
    if(resp_cspush.keys().size() < 1)//etcd没有注册的时候,默认本机
    {
		return "";
    }
	for (uint i = 0; i < resp_cspush.keys().size(); ++i)
	{
	    stream << resp_cspush.key(i) << "=";
	    stream << resp_cspush.value(i).as_string() << ";";
	}
    AK_LOG_INFO << stream.str();
    //随机选择一个cspush
	int push_srv_num = resp_cspush.keys().size();
    int index = GetRandomNum(push_srv_num);
    return resp_cspush.value(index).as_string();
}

bool CAkEtcdCliManager::CheckEtcdCliStatus()
{
    return etcd_stat_;
}

int CAkEtcdCliManager::GetAllPushSrvs(std::set<std::string>& push_srvs)
{
	std::stringstream stream;
    stream << "cspush server list is: ";
    etcd::Response resp = etcd_cli_->ls("/akcs/cspush/").get();
    if (resp.is_ok())
    {
	    if(resp.keys().size() == 0)
	    {
			AK_LOG_WARN << "the num of cspush server is 0, etcd-ls again";
            resp = etcd_cli_->ls("/akcs/cspush/").get();
	    }
		for (uint i = 0; i < resp.keys().size(); ++i)
		{
		    stream << resp.key(i) << "=";
		    stream << resp.value(i).as_string() << ";";
	        push_srvs.insert(resp.value(i).as_string());
		}
	    AK_LOG_INFO << stream.str();
		return 0;
    }
	else
	{
		AK_LOG_WARN << "etcd ,ls cspush srvs failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
		if (resp.error_code() == 14) //etcd 崩溃
		{
			return -1;
		}
	}
    return 0;
}

int CAkEtcdCliManager::GetAllRouteSrvs(std::set<std::string>& route_srvs)
{
	std::stringstream stream;
    stream << "csroute server list is: ";
    etcd::Response resp = etcd_cli_->ls("/akcs/csroute/").get();
    if (resp.is_ok())
    {
	    if(resp.keys().size() == 0)
	    {
			AK_LOG_WARN << "the num of csroute server is 0, etcd-ls again";
            resp = etcd_cli_->ls("/akcs/csroute/").get();
	    }
		for (uint i = 0; i < resp.keys().size(); ++i)
		{
		    stream << resp.key(i) << "=";
		    stream << resp.value(i).as_string() << ";";
	        route_srvs.insert(resp.value(i).as_string());
		}
	    AK_LOG_INFO << stream.str();
		return 0;
    }
	else
	{
		AK_LOG_WARN << "etcd ,ls csroute srvs failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
		if (resp.error_code() == 14) //etcd 崩溃
		{
			return -1;
		}
	}
    return 0;
}

int CAkEtcdCliManager::GetAllAccSrvs(std::vector<std::string>& acc_srvs)
{
	std::stringstream stream;
    stream << "csmain server list is: ";
    etcd::Response resp = etcd_cli_->ls("/akcs/csmain/outerip/").get();
    if (resp.is_ok())
    {
	    if(resp.keys().size() == 0)//为空时,可能业务异常，再尝试一次
	    {
			AK_LOG_WARN << "the num of csmain server is 0, etcd-ls again";
	        resp = etcd_cli_->ls("/akcs/csmain/outerip/").get();
	    }
		for (uint i = 0; i < resp.keys().size(); ++i)
		{
		    stream << resp.key(i) << "=";
		    stream << resp.value(i).as_string() << ";";
	        acc_srvs.push_back(resp.value(i).as_string());
		}
	    AK_LOG_INFO << stream.str();
		return 0;
    }
	else
	{
		AK_LOG_WARN << "etcd ,ls csmain srvs failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
		if (resp.error_code() == 14) //etcd 崩溃
		{
			return -1;
		}
	}
    return 0;
}

//只有内网ip
int CAkEtcdCliManager::GetAllAccRpcInnerSrvs(std::set<std::string>& acc_srvs)
{
	std::stringstream stream;
    stream << "csmain inner rpc server list is: ";
    etcd::Response resp = etcd_cli_->ls("/akcs/csmain/innerip/rpc/").get();
    if (resp.is_ok())
    {
	    if(resp.keys().size() == 0)//为空时,可能业务异常，再尝试一次
	    {
			AK_LOG_WARN << "the num of csmain server is 0, etcd-ls again";
	        resp = etcd_cli_->ls("/akcs/csmain/innerip/rpc/").get();
	    }
		for (uint i = 0; i < resp.keys().size(); ++i)
		{
		    stream << resp.key(i) << "=";
		    stream << resp.value(i).as_string() << ";";
	        acc_srvs.insert(resp.value(i).as_string());
		}
	    AK_LOG_INFO << stream.str();
		return 0;
    }
	else
	{
		AK_LOG_WARN << "etcd ,ls csmain inner rpc srvs failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
		if (resp.error_code() == 14) //etcd 崩溃
		{
			return -1;
		}
	}
    return 0;
}


int CAkEtcdCliManager::GetAllRtspSrvs(std::vector<std::string>& vrtspd_srvs)
{
	std::stringstream stream;
    stream << "csvrtspd server list is: ";
    etcd::Response resp = etcd_cli_->ls("/akcs/csvrtspd/outerip/").get();
    if (resp.is_ok())
    {
	    if(resp.keys().size() == 0)
	    {
			AK_LOG_WARN << "the num of csvrtspd server is 0, etcd-ls again";
            resp = etcd_cli_->ls("/akcs/csvrtspd/outerip/").get();
	    }
		for (uint i = 0; i < resp.keys().size(); ++i)
		{
		    stream << resp.key(i) << "=";
		    stream << resp.value(i).as_string() << ";";
	        vrtspd_srvs.push_back(resp.value(i).as_string());
		}
	    AK_LOG_INFO << stream.str();
		return 0;
    }
	else
	{
		AK_LOG_WARN << "etcd ,ls csvrtspd srvs failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
		if (resp.error_code() == 14) //etcd 崩溃
		{
			return -1;
		}
	}
    return 0;
}

int CAkEtcdCliManager::GetAllOpsSrvs(std::vector<std::string>& ops_srvs)
{
	std::stringstream stream;
    stream << "opensips server list is: ";
    etcd::Response resp = etcd_cli_->ls("/akcs/opensips/outerip/").get();
    if (resp.is_ok())
    {
	    if(resp.keys().size() == 0)
	    {
			AK_LOG_WARN << "the num of opensips server is 0, etcd-ls again";
            resp = etcd_cli_->ls("/akcs/opensips/outerip/").get();
	    }
		for (uint i = 0; i < resp.keys().size(); ++i)
		{
		    stream << resp.key(i) << "=";
		    stream << resp.value(i).as_string() << ";";
	        ops_srvs.push_back(resp.value(i).as_string());
		}
	    AK_LOG_INFO << stream.str();
		return 0;
    }
	else
	{
		AK_LOG_WARN << "etcd ,ls opensips srvs failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
		if (resp.error_code() == 14) //etcd 崩溃
		{
			return -1;
		}
	}
    return 0;
}

int CAkEtcdCliManager::GetAllFtpSrvs(std::vector<std::string>& ftp_srvs)
{
    std::stringstream stream;
    stream << "csftp server list is: ";
    etcd::Response resp = etcd_cli_->ls("/akcs/csftp/outerip/").get();
    if (resp.is_ok())
    {
        if(resp.keys().size() == 0)
        {
            AK_LOG_WARN << "the num of csftp server is 0, etcd-ls again";
            resp = etcd_cli_->ls("/akcs/csftp/outerip/").get();
        }
        for (uint i = 0; i < resp.keys().size(); ++i)
        {
            stream << resp.key(i) << "=";
            stream << resp.value(i).as_string() << ";";
            ftp_srvs.push_back(resp.value(i).as_string());
        }
        AK_LOG_INFO << stream.str();
        return 0;
    }
    else
    {
        AK_LOG_WARN << "etcd ,ls csftp srvs failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
        if (resp.error_code() == 14) //etcd 崩溃
        {
            return -1;
        }
    }
    return 0;
}

const std::string CAkEtcdCliManager::GetRandomAdaptSrv()
{
	std::stringstream stream;
    stream << "csadapt server list is: ";
    etcd::Response resp_csadapt = etcd_cli_->ls("/akcs/csadapt/").get();
    if(resp_csadapt.keys().size() < 1)//etcd没有注册的时候,默认本机
    {
		return "";
    }
	for (uint i = 0; i < resp_csadapt.keys().size(); ++i)
	{
	    stream << resp_csadapt.key(i) << "=";
	    stream << resp_csadapt.value(i).as_string() << ";";
	}
    AK_LOG_INFO << stream.str();
    //随机选择一个cspush
	int push_srv_num = resp_csadapt.keys().size();
    int index = GetRandomNum(push_srv_num);
    return resp_csadapt.value(index).as_string();
}

int CAkEtcdCliManager::GetAllSessionSrvs(std::set<std::string>& session_srvs)
{
	std::stringstream stream;
    stream << "session server list is: ";
    etcd::Response resp = etcd_cli_->ls("/akcs/cssession/").get();
    if (resp.is_ok())
    {
	    if(resp.keys().size() == 0)
	    {
			AK_LOG_WARN << "the num of session server is 0, etcd-ls again";
            resp = etcd_cli_->ls("/akcs/cssession/").get();
	    }
		for (uint i = 0; i < resp.keys().size(); ++i)
		{
		    stream << resp.key(i) << "=";
		    stream << resp.value(i).as_string() << ";";
	        session_srvs.insert(resp.value(i).as_string());
		}
	    AK_LOG_INFO << stream.str();
		return 0;
    }
	else
	{
		AK_LOG_WARN << "etcd ,ls session srvs failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
		if (resp.error_code() == 14) //etcd 崩溃
		{
			return -1;
		}
	}
    return 0;
}

//*************:8512
int CAkEtcdCliManager::GetAllNsqlookupdHttpSrvs(std::set<std::string>& nsqlookupd_srvs)
{
	std::stringstream stream;
    stream << "nsqlookupd server list is: ";
    etcd::Response resp = etcd_cli_->ls("/akcs/nsqlookupd/innerip/http").get();
    if (resp.is_ok())
    {
	    if(resp.keys().size() == 0)
	    {
			AK_LOG_WARN << "the num of nsqlookupd server is 0, etcd-ls again";
            resp = etcd_cli_->ls("/akcs/nsqlookupd/innerip/http").get();
	    }
		for (uint i = 0; i < resp.keys().size(); ++i)
		{
		    stream << resp.key(i) << "=";
		    stream << resp.value(i).as_string() << ";";
	        nsqlookupd_srvs.insert(resp.value(i).as_string());
		}
	    AK_LOG_INFO << stream.str();
		return 0;
    }
	else
	{
		AK_LOG_WARN << "etcd ,ls nsqlookupd srvs failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
		if (resp.error_code() == 14) //etcd 崩溃
		{
			return -1;
		}
	}
    return 0;
}

int CAkEtcdCliManager::GetAllPbxRpcInnerSrvs(std::set<std::string>& srv_addrs)
{
	std::stringstream stream;
    stream << "cspbxrpc server inner rpc server list is: ";
    etcd::Response resp = etcd_cli_->ls("/akcs/cspbxrpc/innerip/rpc/").get();
    if (resp.is_ok())
    {
	    if(resp.keys().size() == 0)//为空时,可能业务异常，再尝试一次
	    {
			AK_LOG_WARN << "the num of cspbxrpc server is 0, etcd-ls again";
	        resp = etcd_cli_->ls("/akcs/cspbxrpc/innerip/rpc/").get();
	    }
		for (uint i = 0; i < resp.keys().size(); ++i)
		{
		    stream << resp.key(i) << "=";
		    stream << resp.value(i).as_string() << ";";
	        srv_addrs.insert(resp.value(i).as_string());
		}
	    AK_LOG_INFO << stream.str();
		return 0;
    }
	else
	{
		AK_LOG_WARN << "etcd ,ls cspbxrpc inner rpc srvs failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
		if (resp.error_code() == 14) //etcd 崩溃
		{
			return -1;
		}
	}
    return 0;
}

int CAkEtcdCliManager::GetAllRbacSrvs(std::set<std::string>& rbac_srvs)
{
    std::stringstream stream;
    stream << "rbac server list is: ";
    etcd::Response resp = etcd_cli_->ls(g_ak_srv_web_rpc_rbac).get();
    if (resp.is_ok())
    {
        if (resp.keys().size() == 0)
        {
            AK_LOG_WARN << "the num of rbac server is 0, etcd-ls again";
            resp = etcd_cli_->ls(g_ak_srv_web_rpc_rbac).get();
        }
        for (uint i = 0; i < resp.keys().size(); ++i)
        {
            stream << resp.key(i) << "=";
            stream << resp.value(i).as_string() << ";";
            rbac_srvs.insert(resp.value(i).as_string());
        }
        AK_LOG_INFO << stream.str();
        return 0;
    }
    else
    {
        AK_LOG_WARN << "get rbac srvs from etcd failed: code="<< resp.error_code()
            << ", message=" << resp.error_message();
        if (resp.error_code() == 14) //etcd 崩溃
        {
            return -1;
        }
    }
    return 0;
}

int CAkEtcdCliManager::GetAllVideoRecordSrvs(std::vector<std::string>& video_record_srvs)
{
    std::stringstream stream;
    stream << "video record server list is: ";
    etcd::Response resp = etcd_cli_->ls(g_ak_srv_video_record).get();
    if (resp.is_ok())
    {
        if (resp.keys().size() == 0)
        {
            AK_LOG_WARN << "the num of video_record is 0, etcd-ls again";
            resp = etcd_cli_->ls(g_ak_srv_video_record).get();
        }
        for (uint i = 0; i < resp.keys().size(); ++i)
        {
            stream << resp.key(i) << "=";
            stream << resp.value(i).as_string() << ";";
            video_record_srvs.push_back(resp.value(i).as_string());
        }
        AK_LOG_INFO << stream.str();
        return 0;
    }
    else
    {
        AK_LOG_WARN << "get video_record_srvs from etcd failed: code="<< resp.error_code() << ", message=" << resp.error_message();
        if (resp.error_code() == 14) //etcd 崩溃
        {
            return -1;
        }
    }
    return 0;
}

int CAkEtcdCliManager::GetAllVideoRecordRpcSrvs(std::set<std::string>& video_record_rpc_srvs)
{
	std::stringstream stream;
    stream << "csvideorecord server rpc server list is: ";
    etcd::Response resp = etcd_cli_->ls(g_ak_srv_video_record_rpc).get();
    if (resp.is_ok())
    {
	    if(resp.keys().size() == 0)//为空时,可能业务异常，再尝试一次
	    {
			AK_LOG_WARN << "the num of csvideorecord rpc server is 0, etcd-ls again";
	        resp = etcd_cli_->ls(g_ak_srv_video_record_rpc).get();
	    }
		for (uint i = 0; i < resp.keys().size(); ++i)
		{
		    stream << resp.key(i) << "=";
		    stream << resp.value(i).as_string() << ";";
	        video_record_rpc_srvs.insert(resp.value(i).as_string());
		}
	    AK_LOG_INFO << stream.str();
		return 0;
    }
	else
	{
		AK_LOG_WARN << "etcd ,ls csvideorecord inner rpc srvs failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
		if (resp.error_code() == 14) //etcd 崩溃
		{
			return -1;
		}
	}
    return 0;
}

int CAkEtcdCliManager::GetAllYunJiRobotRpcSrvs(std::set<std::string>& yunji_robot_rpc_srvs)
{
	std::stringstream stream;
    stream << "csyunjirobot server rpc server list is: ";
    etcd::Response resp = etcd_cli_->ls(g_ak_srv_yunji_robot_rpc).get();
    if (resp.is_ok())
    {
	    if(resp.keys().size() == 0)//为空时,可能业务异常，再尝试一次
	    {
			AK_LOG_WARN << "the num of csyunjirobot rpc server is 0, etcd-ls again";
	        resp = etcd_cli_->ls(g_ak_srv_yunji_robot_rpc).get();
	    }
		for (uint i = 0; i < resp.keys().size(); ++i)
		{
		    stream << resp.key(i) << "=";
		    stream << resp.value(i).as_string() << ";";
	        yunji_robot_rpc_srvs.insert(resp.value(i).as_string());
		}
	    AK_LOG_INFO << stream.str();
		return 0;
    }
	else
	{
		AK_LOG_WARN << "etcd ,ls csyunjirobot inner rpc srvs failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
		if (resp.error_code() == 14) //etcd 崩溃
		{
			return -1;
		}
	}
    return 0;
}

//外部配置项写在这里
int CAkEtcdCliManager::LoadGateSrvOuterConf(GateSrvConf &csgate_conf)
{
    std::stringstream stream;
    stream << "LoadGateSrvConf: ";
    std::string value;
    try
    {
        etcd::Response resp = etcd_cli_->ls(g_conf_db_addr).get();
        if (resp.is_ok())
        {
            for (uint i = 0; i < resp.keys().size(); ++i)//2022.03.22 chenyc,即使明确没有subtree,也不能通过value = resp.value().as_string() 获取
    		{
    		    stream << resp.key(i) << "=";
    		    stream << resp.value(i).as_string() << ";";
                value = resp.value(0).as_string();
    		}
            AK_LOG_INFO << stream.str();
            std::vector<std::string> ip_port;
            SplitString(value, ":", ip_port);
            if(ip_port.size() != 2)
            {
                AK_LOG_WARN << "The conf_db_addr format in server conf is error:" << value;
                return -1;
            }
            ::strncpy(csgate_conf.db_ip, ip_port[0].c_str(), sizeof(csgate_conf.db_ip) - 1);
            csgate_conf.db_port = ATOI(ip_port[1].c_str());
        }
    	else
    	{
    		AK_LOG_WARN << "etcd ,ls conf_db_addr failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
    		if (resp.error_code() == 14) //etcd 崩溃
    		{
    			return -1;
    		}
    	}
      
    }
    catch (const exception& ex)
    {
        AK_LOG_WARN << "etcd watch caught exception, details: " << ex.what();
    }
    return 0;

}
int CAkEtcdCliManager::LoadSrvDbOuterConf(SrvDbConf &conf)
{
    std::stringstream stream;
    stream << "LoadSrvConf: ";
    std::string value;
    try
    {
        etcd::Response resp = etcd_cli_->ls(g_conf_db_addr).get();
        if (resp.is_ok())
        {
            for (uint i = 0; i < resp.keys().size(); ++i)//2022.03.22 chenyc,即使明确没有subtree,也不能通过value = resp.value().as_string() 获取
    		{
    		    stream << resp.key(i) << "=";
    		    stream << resp.value(i).as_string() << ";";
                value = resp.value(0).as_string();
    		}
            AK_LOG_INFO << stream.str();
            std::vector<std::string> ip_port;
            SplitString(value, ":", ip_port);
            if(ip_port.size() != 2)
            {
                AK_LOG_WARN << "The conf_db_addr format in server conf is error:" << value;
                return -1;
            }
            ::strncpy(conf.db_ip, ip_port[0].c_str(), sizeof(conf.db_ip) - 1);
            conf.db_port = ATOI(ip_port[1].c_str());
        }
    	else
    	{
    		AK_LOG_WARN << "etcd ,ls conf_db_addr failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
    		if (resp.error_code() == 14) //etcd 崩溃
    		{
    			return -1;
    		}
    	}
        
        resp = etcd_cli_->ls(g_conf_db_master_addr).get();
        if (resp.is_ok())
        {
            for (uint i = 0; i < resp.keys().size(); ++i)
            {
    		    stream << resp.key(i) << "=";
    		    stream << resp.value(i).as_string() << ";";
                value = resp.value(0).as_string();
    		}
            AK_LOG_INFO << stream.str();
            std::vector<std::string> ip_port;
            SplitString(value, ":", ip_port);
            if(ip_port.size() != 2)
            {
                AK_LOG_WARN << "The conf_db_master_addr format in server conf is error:" << value;
                return -1;
            }
            ::strncpy(conf.db_master_ip, ip_port[0].c_str(), sizeof(conf.db_ip) - 1);
            conf.db_master_port = ATOI(ip_port[1].c_str());
        }
    	else
    	{
    		AK_LOG_WARN << "etcd ,ls conf_db_addr failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
    		if (resp.error_code() == 14) //etcd 崩溃
    		{
    			return -1;
    		}
    	}
        
      
    }
    catch (const exception& ex)
    {
        AK_LOG_WARN << "etcd watch caught exception, details: " << ex.what();
    }
    return 0;

}

int CAkEtcdCliManager::LoadSrvSmgConf(std::string &smg_addr)
{
    std::stringstream stream;
    stream << "LoadSrvConf: ";

    try
    {
        etcd::Response resp = etcd_cli_->ls(g_ak_srv_smg).get();
        if (resp.is_ok())
        {
            for (uint i = 0; i < resp.keys().size(); ++i)
    		{
    		    stream << resp.key(i) << "=";
    		    stream << resp.value(i).as_string() << ";";
                smg_addr = resp.value(0).as_string();
    		}
            AK_LOG_INFO << stream.str();
        }
    	else
    	{
    		AK_LOG_WARN << "etcd ,ls conf_db_addr failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
    		if (resp.error_code() == 14) //etcd 崩溃
    		{
    			return -1;
    		}
    	}        
      
    }
    catch (const exception& ex)
    {
        AK_LOG_WARN << "etcd watch caught exception, details: " << ex.what();
    }
    
    return 0;
}

int CAkEtcdCliManager::LoadSrvMqttConf(std::string &mqtt_addr)
{
    std::stringstream stream;
    stream << "LoadSrvConf: ";

    try
    {
        etcd::Response resp = etcd_cli_->ls(g_ak_srv_mqtt_inner_tcp).get();
        if (resp.is_ok())
        {
            for (uint i = 0; i < resp.keys().size(); ++i)
    		{
    		    stream << resp.key(i) << "=";
    		    stream << resp.value(i).as_string() << ";";
                mqtt_addr = resp.value(0).as_string();
    		}
            AK_LOG_INFO << stream.str();
        }
    	else
    	{
    		AK_LOG_WARN << "etcd ,ls conf_db_addr failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
    		if (resp.error_code() == 14) //etcd 崩溃
    		{
    			return -1;
    		}
    	}        
      
    }
    catch (const exception& ex)
    {
        AK_LOG_WARN << "etcd watch caught exception, details: " << ex.what();
    }
    
    return 0;
}

int CAkEtcdCliManager::LoadSrvMqttOuterTlsConf(std::string &mqtt_addr)
{
    std::stringstream stream;
    stream << "LoadSrvConf: ";

    try
    {
        etcd::Response resp = etcd_cli_->ls(g_ak_srv_mqtt_outer_tls).get();
        if (resp.is_ok())
        {
            for (uint i = 0; i < resp.keys().size(); ++i)
    		{
    		    stream << resp.key(i) << "=";
    		    stream << resp.value(i).as_string() << ";";
                mqtt_addr = resp.value(0).as_string();
    		}
            AK_LOG_INFO << stream.str();
        }
    	else
    	{
    		AK_LOG_WARN << "etcd ,ls conf_db_addr failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
    		if (resp.error_code() == 14) //etcd 崩溃
    		{
    			return -1;
    		}
    	}        
      
    }
    catch (const exception& ex)
    {
        AK_LOG_WARN << "etcd watch caught exception, details: " << ex.what();
    }
    
    return 0;
}


int CAkEtcdCliManager::LoadSrvSmgAlexaConf(std::string &smg_alexa_addr)
{
    std::stringstream stream;
    stream << "LoadSrvConf: ";

    try
    {
        etcd::Response resp = etcd_cli_->ls(g_ak_srv_smg_alexa).get();
        if (resp.is_ok())
        {
            // 目前只有单台直接取下标0的返回,后面集群改为获取所有节点
            stream << resp.key(0) << "=";
            stream << resp.value(0).as_string() << ";";
            smg_alexa_addr = resp.value(0).as_string();
            AK_LOG_INFO << stream.str();
       /*
            for (uint i = 0; i < resp.keys().size(); ++i)
    		{
    		    stream << resp.key(i) << "=";
    		    stream << resp.value(i).as_string() << ";";
                smg_alexa_addr = resp.value(0).as_string();
                return 0;
    		}
    	*/
        }
    	else
    	{
    		AK_LOG_WARN << "etcd ,ls smg alexa failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
    		if (resp.error_code() == 14) //etcd 崩溃
    		{
    			return -1;
    		}
    	}        
    }
    catch (const exception& ex)
    {
        AK_LOG_WARN << "etcd watch caught exception, details: " << ex.what();
    }
    
    return 0;
}

int CAkEtcdCliManager::LoadSrvZipkinConf(std::string &kafka_addr)
{
    std::stringstream stream;
    stream << "LoadSrvConf: ";

    try
    {
        etcd::Response resp = etcd_cli_->ls(g_ak_srv_zipkin_kafka).get();
        if (resp.is_ok())
        {
            for (uint i = 0; i < resp.keys().size(); ++i)
    		{
    		    stream << resp.key(i) << "=";
    		    stream << resp.value(i).as_string() << ";";
                kafka_addr = resp.value(0).as_string();
    		}
            AK_LOG_INFO << stream.str();
        }
    	else
    	{
    		AK_LOG_WARN << "etcd ,ls conf_db_addr failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
    		if (resp.error_code() == 14) //etcd 崩溃
    		{
    			return -1;
    		}
    	}        
      
    }
    catch (const exception& ex)
    {
        AK_LOG_WARN << "etcd watch caught exception, details: " << ex.what();
    }
    
    return 0;
}

int CAkEtcdCliManager::LoadSrvWebAdaptEntryConf(std::string& key, std::string& web_adapt_entry)
{
    std::stringstream stream;
    stream << "LoadSrvConf: ";

    try
    {
        etcd::Response resp = etcd_cli_->ls(g_ak_srv_web_adapt_entry).get();
        if (resp.is_ok())
        {
            stream << resp.key(0) << "=";
            stream << resp.value(0).as_string() << ";";
            key = resp.key(0);
            web_adapt_entry = resp.value(0).as_string();
            AK_LOG_INFO << stream.str();
        }
        else
        {
            AK_LOG_WARN << "etcd ,ls web_adapt_entry failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
            if (resp.error_code() == 14) //etcd 崩溃
            {
                return -1;
            }
        }        
    }
    catch (const exception& ex)
    {
        AK_LOG_WARN << "etcd watch caught exception, details: " << ex.what();
    }

    return 0;
}

int CAkEtcdCliManager::LoadSrvMqttInnerApiHttpConf(std::string &mqtt_inner_api_http)
{
    std::stringstream stream;
    stream << "LoadSrvConf: ";

    try
    {
        etcd::Response resp = etcd_cli_->ls(g_ak_srv_mqtt_inner_api_http).get();
        if (resp.is_ok())
        {
            for (uint i = 0; i < resp.keys().size(); ++i)
    		{
    		    stream << resp.key(i) << "=";
    		    stream << resp.value(i).as_string() << ";";
                mqtt_inner_api_http = resp.value(0).as_string();
    		}
            AK_LOG_INFO << stream.str();
        }
    	else
    	{
    		AK_LOG_WARN << "etcd ,ls mqtt inner api http failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
    		if (resp.error_code() == 14) //etcd 崩溃
    		{
    			return -1;
    		}
    	}        
      
    }
    catch (const exception& ex)
    {
        AK_LOG_WARN << "etcd watch caught exception, details: " << ex.what();
    }
    
    return 0;
}

void CAkEtcdCliManager::EnterWatchChanges()
{
    do
    {
        WatchForAKChanges(g_ak_srv_conf);
    } while (true);
}

void CAkEtcdCliManager::EnterWatchWebDomainServerChanges()
{
    do
    {
        WatchForAKChanges(g_web_domain_server);
    } while (true);
}

void CAkEtcdCliManager::WatchForAKChanges(const char* watch_key)
{
    pplx::task<etcd::Response> tmp;
    static int index_watch = 0;
    if (index_watch == 0)//added by chenyc,当与watch通信的节点失效的时候,index也会返回0
    {
        tmp = etcd_cli_->watch(watch_key, true);
    }
    else
    {
        tmp = etcd_cli_->watch(watch_key, index_watch + 1, true);
    }

    try
    {
        etcd::Response resp = tmp.get();  //watch实际上等待在这里.直到watch的key或者subkey有变化
        if (resp.is_ok())
        {   
            //watch的返回值,keys是没有值的
            const std::string change_key = resp.key();//这个key是实际变化的key,不一定是watch的key
            AK_LOG_INFO << "WatchForAKChanges: etcd watch something happend, action is:"<< resp.action()
                        << ", key is " << change_key << ", value is:" << resp.value().as_string() << ", watch index is:" << resp.index();
            //watch timeout,第一次打印 etcd watch something happend, action is:,value is:, watch index is:1789,之后就会打印index=0,
            //所以这种情况下,index不要赋值,直接执行故障转移
            if(resp.action().empty())
            {   
                etcd_cli_->node_failover();
                AK_LOG_WARN << "Some error happened, etcd node failover, details: " << resp.error_message();
            }
            else
            {
                index_watch = resp.index();
                WatchKeyCbMap watch_key_cb_tmp;
                {
                    std::lock_guard<std::mutex> lock(watch_key_lock_);
                    watch_key_cb_tmp = watch_key_cb_;
                }
                WatchKeyCbMap::iterator it = watch_key_cb_tmp.find(change_key);
                if(it != watch_key_cb_tmp.end())
                {
                    it->second();
                }
                //根据变化的key,进行整个watch tree的遍历
                //例如实际变化的是/akconf/csmain/db_addr,监听列表有/akconf/csmain，那么就要执行/akconf/csmain对应的cb
                for(const auto watch_key_cb_pair : watch_key_cb_tmp)
                {
                    std::size_t found = change_key.find(watch_key_cb_pair.first);
                    if((found != std::string::npos) && (change_key != watch_key_cb_pair.first))
                    {
                        watch_key_cb_pair.second();
                    }
                }
            }
        }
        else
        {
            AK_LOG_WARN << "Watch error, etcd node failover, details: " << resp.error_message();
            etcd_cli_->node_failover();
        }
    }
    catch (const exception& ex)
    {
        AK_LOG_WARN << "etcd watch caught exception, details: " << ex.what();
    }
}

int CAkEtcdCliManager::WatchSrvs(const std::string& key, WatchSrvCallback& cb)
{
	do
    {
    	WatchForChanges(key, cb); 
    } while(true);
    return 0;
}

void CAkEtcdCliManager::WatchForChanges(const std::string& key, const WatchSrvCallback& cb)
{  
	pplx::task<etcd::Response> tmp;
    static int index = 0;
    if(index == 0)//added by chenyc,当与watch通信的节点失效的时候,index也会返回0
    {
        tmp = etcd_cli_->watch(key, true);
    }
    else
    {
        tmp = etcd_cli_->watch(key, index + 1, true);
    }
    try
    {
        etcd::Response resp = tmp.get();//watch实际上等待在这里.直到watch的key或者子key有变化     
        if (resp.is_ok())
        {
            //etcd watch something happend,delete,create
	        //watch的返回值,key是没有值的
	        //AK_LOG_INFO << "num of key is:" << resp.keys().size();
	        
	        AK_LOG_INFO << "WatchForChanges：etcd watch something happend, action is:"<< resp.action()
	                    << " key is " << key << ", value is:" << resp.value().as_string() << ", watch index is:" << resp.index();
			//watch timeout,第一次打印 etcd watch something happend, action is:,value is:, watch index is:1789,之后就会打印index=0,
			//所以这种情况下,index不要赋值,直接执行故障转移
			if(resp.action().empty())
			{
				etcd_cli_->node_failover();
                AK_LOG_WARN << "Some error happened, etcd node failover, details: " << resp.error_message();
			}
			else
			{
				cb();
				index = resp.index();                
			}
        }
        else
        {
			AK_LOG_WARN << "Watch error, etcd node failover, details: " << resp.error_message();
			etcd_cli_->node_failover();
        }
    }
    catch (const exception& ex)
    {
        AK_LOG_WARN << "etcd watch caught exception, details: " << ex.what();
    }
}

void CAkEtcdCliManager::AddAsyncWatchKey(const std::string& key, WatchSrvCallback cb)
{
    AK_LOG_INFO << "AddAsyncWatchKey key = " << key;
    std::lock_guard<std::mutex> lock(watch_key_lock_);
    watch_key_cb_.insert(std::pair<std::string, WatchKeyCallback>(key, cb));
}

const std::string CAkEtcdCliManager::GetHealthyNodeUri() const
{
   return etcd_cli_->get_healthy_node_uri();
}
//
int64_t CAkEtcdCliManager::RegSrv(const std::string& key, const std::string& value,const int ttl, 
                                                 int type)
{
	etcd::Response response;
    int64_t lease_id = 0;
	//modified by chenyc,2019-04-25,修改成永久有效
    //pplx::task<etcd::Response> response_task = etcd_cli_->set(key, value, ttl);
	pplx::task<etcd::Response> response_task = etcd_cli_->set(key, value, 0);
    try
    {    
	    response = response_task.get();
	    if (response.is_ok())
        {
            lease_id = response.value().lease();
	        AK_LOG_INFO << "value is " <<response.value().as_string() << " lease_id is " << lease_id;
        }
	    else
        {
	        AK_LOG_FATAL << "operation failed, details: " << response.error_message();
        }
    }
    catch (std::exception const & ex)
    {
    	AK_LOG_FATAL << "communication problem, details: " << ex.what();
        return -1;
    }
    return 0;

}

//注册并保活
int64_t CAkEtcdCliManager::RegKeepAliveSrv(const std::string& key, const std::string& value,const int ttl, 
                                                 int type, evpp::EventLoop* loop)
{
	etcd::Response response;
    int64_t lease_id = 0;
	//modified by chenyc,2019-04-25,修改成永久有效
    //pplx::task<etcd::Response> response_task = etcd_cli_->set(key, value, ttl);
	pplx::task<etcd::Response> response_task = etcd_cli_->set(key, value, 0);
    try
    {    
	    response = response_task.get();
	    if (response.is_ok())
        {
            lease_id = response.value().lease();
	        AK_LOG_INFO << "value is " <<response.value().as_string() << " lease_id is " << lease_id;
        }
	    else
        {
	        AK_LOG_FATAL << "operation failed, details: " << response.error_message();
        }
    }
    catch (std::exception const & ex)
    {
    	AK_LOG_FATAL << "communication problem, details: " << ex.what();
        return -1;
    }
	//modified by chenyc,2019-04-25,改成注册后，永久有效
    #if 0
    //定时发送
    if (lease_id > 0)
    {
    	//etcd_cli_->leasekeepalive(lease_id);
		if(type == csbase::REG_INNER)
		{
			inner_key_ = key;
			inner_value_ = value;
			inner_ttl_ = ttl;
			inner_lease_id_ = lease_id;
			//(ttl / 2) - 0.1):保证一个key的租约周期能够有两次的keepalive,避免因单次续约失败,导致注册中心中该节点被删除
			//modified by chenyc,2019-04-25,永久有效
    		//loop->RunEvery(evpp::Duration(static_cast<double>((ttl / 2) - 0.1)), std::bind(&CAkEtcdCliManager::onInnerLeaseKeepAliveTimer, this));
		}
		else
	    {
			outer_key_ = key;
			outer_value_ = value;
			outer_ttl_ = ttl;
			outer_lease_id_ = lease_id;
			
			//loop->RunEvery(evpp::Duration(static_cast<double>((ttl / 2) - 0.1)), std::bind(&CAkEtcdCliManager::onOuterLeaseKeepAliveTimer, this));
		}
    }
	else
	{
		AK_LOG_FATAL << "reg etcd server failed, key is:" << key;
	}
	#endif
	return 0;
}

void CAkEtcdCliManager::onInnerLeaseKeepAliveTimer()
{
	//TODO,失败重试,重试失败,证明与etcd srv失去通信连接,需要触发告警系统 	
	etcd::Response response;
	int64_t lease_id = 0;
	if(is_inner_need_register_again_)//当与etcd断开后,需要重新注册服务
	{
		pplx::task<etcd::Response> response_task = etcd_cli_->set(inner_key_, inner_value_, inner_ttl_);
	    try
	    {    
		    response = response_task.get();
		    if (response.is_ok())
	        {
	            lease_id = response.value().lease();
		        AK_LOG_INFO << "value is" <<response.value().as_string() << "lease_id is " << lease_id;
				is_inner_need_register_again_ = false;
				if(lease_id > 0)//证明重新与etcd连接上了
			    {
					inner_lease_id_ = lease_id;
				}
	        }
		    else
	        {
		        AK_LOG_WARN << "operation failed, details: " << response.error_message();
				//return;
	        }
	    }
	    catch (std::exception const & ex)
	    {
	    	AK_LOG_WARN << "communication problem, details: " << ex.what();
	        //return;
	    }
	}
    pplx::task<etcd::Response> response_task = etcd_cli_->leasekeepalive(inner_lease_id_);
    try
    {    
	    response = response_task.get();
	    if (response.is_ok())
        {
            //int64_t lease_id = response.value().lease();
            //int64_t lease_ttl = response.value().ttl();
	        //AK_LOG_INFO << "lease keepalive succeed, lease_id is " << lease_id << ", ttl is " << lease_ttl;
        }
	    else
        {
			//lease keepalive failed, details: Connect Failed, error code:14
			AK_LOG_WARN << "lease keepalive failed, details: " << response.error_message() << ", error code:" << response.error_code();
            //TODO,chenyc, 2019-03-15,重试一次 lease keepalive failed, details: Connect Failed
            if(response.error_code() == 14)//Connect Failed == 14
            {
                is_inner_need_register_again_ = true;
			}
        }
    }
    catch (std::exception const & ex)
    {
    	AK_LOG_WARN << "keepalive communication problem, details: " << ex.what();
        return;
    }
}
void CAkEtcdCliManager::onOuterLeaseKeepAliveTimer()
{
	//TODO,失败重试,重试失败,证明与etcd srv失去通信连接,需要触发告警系统 
	etcd::Response response;
	int64_t lease_id = 0;
	if(is_outer_need_register_again_)//当与etcd断开后,需要重新注册服务
	{
		pplx::task<etcd::Response> response_task = etcd_cli_->set(outer_key_, outer_value_, outer_ttl_);
		try
		{	 
			response = response_task.get();
			if (response.is_ok())
			{
				lease_id = response.value().lease();
				AK_LOG_INFO << "value is" <<response.value().as_string() << "lease_id is " << lease_id;
				is_outer_need_register_again_ = false;
				if(lease_id > 0)
			    {
					outer_lease_id_ = lease_id;
				}
			}
			else
			{
				AK_LOG_WARN << "operation failed, details: " << response.error_message();
				return;
			}
		}
		catch (std::exception const & ex)
		{
			AK_LOG_WARN << "communication problem, details: " << ex.what();
			return;
		}
	}
	pplx::task<etcd::Response> response_task = etcd_cli_->leasekeepalive(outer_lease_id_);
	try
	{	 
		response = response_task.get();
		if (response.is_ok())
		{
			//int64_t lease_id = response.value().lease();
			//int64_t lease_ttl = response.value().ttl();
			//AK_LOG_INFO << "lease keepalive succeed, lease_id is " << lease_id << ", ttl is " << lease_ttl;
		}
		else
		{
			//lease keepalive failed, details: Connect Failed, error code:14
			AK_LOG_WARN << "lease keepalive failed, details: " << response.error_message() << ", error code:" << response.error_code();
			//TODO,chenyc, 2019-03-15,重试一次 lease keepalive failed, details: Connect Failed
			if(response.error_code() == 14)//Connect Failed == 14
			{
				is_outer_need_register_again_ = true;
			}
		}
	}
	catch (std::exception const & ex)
	{
		AK_LOG_WARN << "keepalive communication problem, details: " << ex.what();
		return;
	}
}

void CAkEtcdCliManager::LeaseRevoke()
{
	etcd_cli_->leaserevoke(outer_lease_id_);
	etcd_cli_->leaserevoke(inner_lease_id_);
}

void CAkEtcdCliManager::UpdateEtcdServerStatus()
{
    try
    {
        etcd::Response resp = etcd_cli_->ls(g_conf_db_addr).get();
        if (resp.is_ok())
        {
            etcd_stat_ = true;
        }
        else
        {
            etcd_stat_ = false;
            AK_LOG_WARN << "etcd ,ls conf_db_addr failed, details: " << resp.error_message() << ", error code is: " << resp.error_code();
            if (resp.error_code() == 14) //etcd 崩溃
            {
                return ;
            }
        }        
      
    }
    catch (std::exception const & ex)
    {
        AK_LOG_WARN << "etcd watch caught exception, details: " << ex.what();
    }
    
    return ;
}

  

void CAkEtcdCliManager::CheckEtcdHealth(evpp::EventLoop* loop)
{
    double interval = 15.0;
    loop->RunEvery(evpp::Duration(static_cast<double>(interval)), std::bind(&CAkEtcdCliManager::UpdateEtcdServerStatus, this)); 
    return ;
}

