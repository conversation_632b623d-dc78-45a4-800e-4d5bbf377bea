#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <iostream>
#include <sstream>
#include <thread>
#include <fcntl.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "AdaptDef.h"
#include "AKCSMsg.h"
#include "Rldb.h"
#include "UnixSocketControl.h"
#include "AKCSView.h"
#include "ConfigFileReader.h"
#include "redis/PubSubManager.h"
#include <unistd.h>
#include <signal.h>
#include "ConnectionPool.h"
#include "AwsConnectionPool.h"
#include "AkLogging.h"
#include <evpp/evnsq/producer.h>
#include <evpp/event_loop.h>
#include "AdaptMQProduce.h"
#include "AdaptEtcd.h"
#include "AdaptMQProduce.h"
#include "AES256.h"
#include "HttpServer.h"
#include "CachePool.h"
#include "AkcsDnsResolver.h"
#include "EtcdCliMng.h"
#include "KafkaConsumerAppBackendTopicHandle.h"
#include "kafka/AkcsKafkaProducerNotifyConfig.h"
#include "zipkin/ZipkinConf.h"
#include "zipkin/ZipkinAsyn.h"
#include "AkcsAppInit.h"
#include "Rldb/LogConnectionPoolManage.h"
#include "beanstalk.hpp"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/SystemSettingTable.h"
#include "KafkaConsumerPushTopicHandle.h"
#include "KafkaConsumerNotifyTopicHandle.h"
#include "NotifyMessageKafkaConsumer.h"
#include "NewOfficeNotifyHandler.h"
#include <KdcDecrypt.h>
#include "RouteClientMng.h"
#include "Metric.h"

CSADAPT_CONF gstCSADAPTConf;
PubSubManager* g_pub_sub_mng_ptr = nullptr;
evnsq::Producer* g_nsq_pub_mng_ptr = nullptr;
#define MAX_RLDB_CONN 10
#define BEANSTALK_SERVER_PORT  (8519)
extern CAkEtcdCliManager* g_etcd_cli_mng;
extern const char* g_conf_db_master_addr;
extern RouteMQProduce* g_nsq_producer;
int g_etcd_dns_res = 0;
extern const char* g_ak_srv_zipkin_kafka;
extern CAkEtcdCliManager* g_etcd_zipkinconf_cli;
LOG_DELIVERY gstAKCSLogDelivery;
std::map<string, AKCS_DST> g_time_zone_DST;

#define PIDFILE "/var/run/csadapt.pid"
#define CSADAPD_CONF_FILE "/usr/local/akcs/csadapt/conf/csadapt.conf"


/* 初始化数据库连接 */
int DaoInit()
{
    ConnPool* gConnPool = GetDBConnPollInstance();
    if (NULL == gConnPool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    gConnPool->Init(gstCSADAPTConf.szDbIP, gstCSADAPTConf.szDbUserName, gstCSADAPTConf.szDbPassword, gstCSADAPTConf.szDbDatabase, gstCSADAPTConf.nDbPort, MAX_RLDB_CONN, "csadapt");

    LogConnPoolManage* log_conn_pool_manage = GetLogDBConnPoolManageInstance();
    if (NULL == log_conn_pool_manage)
    {
        AK_LOG_WARN << "LOG DaoInit failed.";
        return -1;
    }
    log_conn_pool_manage->Init(gstCSADAPTConf.log_db_ip, gstCSADAPTConf.szDbUserName, gstCSADAPTConf.szDbPassword, gstCSADAPTConf.log_db_database, gstCSADAPTConf.log_db_port, MAX_RLDB_CONN, "csadapt");

    if (gstCSADAPTConf.is_aws)
    {
        AwsConnPool* aws_conn_pool = GetAwsDBConnPollInstance();
        if (NULL == aws_conn_pool)
        {
            AK_LOG_WARN << "Aws DaoInit failed.";
            return -1;
        }
        aws_conn_pool->Init(gstCSADAPTConf.aws_db_ip, gstCSADAPTConf.szDbUserName, gstCSADAPTConf.szDbPassword, gstCSADAPTConf.szDbDatabase, gstCSADAPTConf.nDbPort, 5, "csadapt");
    }
    return 0;
}

int DaoReInit()
{
    ConnPool* conn_pool = GetDBConnPollInstance();
    if (NULL == conn_pool)
    {
        AK_LOG_WARN << "DaoReInit failed.";
        return -1;
    }
    conn_pool->ReInit(gstCSADAPTConf.szDbIP, gstCSADAPTConf.nDbPort);
    return 0;
}

int LogDeliveryInit()
{
    gstAKCSLogDelivery.personal_capture_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalCapture");
    gstAKCSLogDelivery.personal_motion_delivery = dbinterface::LogSlice::GetDeliveryByTableName("PersonalMotion");
    gstAKCSLogDelivery.call_history_delivery = dbinterface::LogSlice::GetDeliveryByTableName("CallHistory");
    if (gstAKCSLogDelivery.personal_capture_delivery == 0 || gstAKCSLogDelivery.personal_motion_delivery == 0 || gstAKCSLogDelivery.call_history_delivery == 0)
    {
        return -1;
    }
    return 0;
}

void UpdateOuterConfFromConfSrv()
{
    if (gstCSADAPTConf.is_aws)
    {
        return;
    }

    SrvDbConf conf_tmp;
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    //进行比较,确定配置型变更后需要的动作
    if ((::strcmp(conf_tmp.db_master_ip, gstCSADAPTConf.szDbIP) != 0) || (conf_tmp.db_master_port != gstCSADAPTConf.nDbPort))
    {
        Snprintf(gstCSADAPTConf.szDbIP, sizeof(gstCSADAPTConf.szDbIP), conf_tmp.db_master_ip);
        gstCSADAPTConf.nDbPort = conf_tmp.db_master_port;
        DaoReInit();
    }
}

int LoadConfFromConfSrv()
{
    if (gstCSADAPTConf.is_aws)
    {
        return -1;
    }

    SrvDbConf conf_tmp;
    g_etcd_cli_mng->LoadSrvDbOuterConf(conf_tmp);
    if ((strlen(conf_tmp.db_master_ip) == 0) || (conf_tmp.db_master_port == 0))
    {
        return -1;
    }
    Snprintf(gstCSADAPTConf.szDbIP, sizeof(gstCSADAPTConf.szDbIP), conf_tmp.db_master_ip);
    gstCSADAPTConf.nDbPort = conf_tmp.db_master_port;
    return 0;
}
void ConfSrvInit()
{
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstCSADAPTConf.szEtcdServerAddr);

    g_etcd_cli_mng->AddAsyncWatchKey(g_conf_db_master_addr, UpdateOuterConfFromConfSrv);

    g_etcd_zipkinconf_cli = g_etcd_cli_mng;
    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_zipkin_kafka, UpdateZipkinConfFromConfSrv);
    //从配置中心获取 初始化zipkin配置
    UpdateZipkinConfFromConfSrv();
}
void ConfWatch()
{
    g_etcd_cli_mng->EnterWatchChanges();
    CAkEtcdCliManager::destroyInstance();
}


void ConfInit(const std::string& file)
{
    CConfigFileReader config_file(file.c_str());

    const char* is_aws = config_file.GetConfigName("is_aws");
    gstCSADAPTConf.is_aws = ATOI(is_aws);

    Snprintf(gstCSADAPTConf.szCsadaptOuterIP, sizeof(gstCSADAPTConf.szCsadaptOuterIP), config_file.GetConfigName("csadapt_outerip"));
    const char* log_level = config_file.GetConfigName("csadapt_loglevel");
    gstCSADAPTConf.nLogLevel = ATOI(log_level);

    Snprintf(gstCSADAPTConf.szCspbxOuterIP, sizeof(gstCSADAPTConf.szCspbxOuterIP), config_file.GetConfigName("cspbx_ip"));
    Snprintf(gstCSADAPTConf.szCspbxOuterPort, sizeof(gstCSADAPTConf.szCspbxOuterPort), config_file.GetConfigName("cspbx_port"));

    Snprintf(gstCSADAPTConf.szDbUserName, sizeof(gstCSADAPTConf.szDbUserName), config_file.GetConfigName("db_username"));
    //获取数据库密码
    CConfigFileReader kdc_config_file("/etc/kdc.conf");
    const char* encrypt_db_passwd = kdc_config_file.GetConfigName("AKCS_DBUSER01");
    AK_LOG_INFO << "Encrypted Database Password: " << (encrypt_db_passwd != nullptr ? encrypt_db_passwd : "null");
    std::string decrypt_db_passwd = akuvox_encrypt::KdcDecrypt(std::string(encrypt_db_passwd));

    Snprintf(gstCSADAPTConf.szDbPassword, sizeof(gstCSADAPTConf.szDbPassword), decrypt_db_passwd.c_str());

    Snprintf(gstCSADAPTConf.szDbDatabase, sizeof(gstCSADAPTConf.szDbDatabase), config_file.GetConfigName("db_database"));
    //Snprintf(gstCSADAPTConf.szDbSocketFile, sizeof(gstCSADAPTConf.szDbSocketFile), config_file.GetConfigName("db_socketfile"));

    if (LoadConfFromConfSrv() != 0)
    {
        Snprintf(gstCSADAPTConf.szDbIP, sizeof(gstCSADAPTConf.szDbIP), config_file.GetConfigName("db_ip"));
        const char* db_port = config_file.GetConfigName("db_port");
        gstCSADAPTConf.nDbPort = ATOI(db_port);
    }
    const char* encrypt = config_file.GetConfigName("noencrypt");
    gstCSADAPTConf.nNoEncrypt = ATOI(encrypt);

    Snprintf(gstCSADAPTConf.web_domain, sizeof(gstCSADAPTConf.web_domain), config_file.GetConfigName("web_domain"));

    /*读取OEM特殊配置文件，直接加入配置文件选项*/
    FILE* pFstream = nullptr;
    if ((pFstream = fopen("/usr/local/akcs/csadapt/conf/oem_config.conf", "r")) != nullptr)
    {
        fread(gstCSADAPTConf.szOEMConfig, sizeof(char), sizeof(gstCSADAPTConf.szOEMConfig), pFstream);
        fclose(pFstream);
    }
    Snprintf(gstCSADAPTConf.szNSQTopicForDelPic, sizeof(gstCSADAPTConf.szNSQTopicForDelPic), config_file.GetConfigName("nsq_delpic_topic"));
    Snprintf(gstCSADAPTConf.szNSQRouteTopic, sizeof(gstCSADAPTConf.szNSQRouteTopic), config_file.GetConfigName("nsq_route_topic"));

    Snprintf(gstCSADAPTConf.szBeanStalkAddr, sizeof(gstCSADAPTConf.szBeanStalkAddr), config_file.GetConfigName("beanstalkd_ip"));
    Snprintf(gstCSADAPTConf.beanstalk_backup_ip, sizeof(gstCSADAPTConf.beanstalk_backup_ip), config_file.GetConfigName("beanstalkd_backup_ip"));

    Snprintf(gstCSADAPTConf.szSshProxyDomain, sizeof(gstCSADAPTConf.szSshProxyDomain), config_file.GetConfigName("remote_config_domain"));
    Snprintf(gstCSADAPTConf.web_ip, sizeof(gstCSADAPTConf.web_ip), config_file.GetConfigName("web_ip"));

    //获取服务器ip信息
    CConfigFileReader server_config_file("/etc/ip");
    Snprintf(gstCSADAPTConf.szServerHostname, sizeof(gstCSADAPTConf.szServerHostname), server_config_file.GetConfigName("AKCS_HOSTNAME"));
    Snprintf(gstCSADAPTConf.szServerInnerIP, sizeof(gstCSADAPTConf.szServerInnerIP), server_config_file.GetConfigName("SERVER_INNER_IP"));
    Snprintf(gstCSADAPTConf.szServerOutIP, sizeof(gstCSADAPTConf.szServerOutIP), server_config_file.GetConfigName("SERVERIP"));

    const char* system_area_type = config_file.GetConfigName("system_area_type");
    gstCSADAPTConf.server_type = ATOI(system_area_type);
    Snprintf(gstCSADAPTConf.community_ids, sizeof(gstCSADAPTConf.community_ids), config_file.GetConfigName("community_ids"));

    Snprintf(gstCSADAPTConf.aws_db_ip, sizeof(gstCSADAPTConf.aws_db_ip), config_file.GetConfigName("aws_mysql_ip"));
    const char* aws_redirect = config_file.GetConfigName("aws_redirect");
    gstCSADAPTConf.aws_redirect = ATOI(aws_redirect);

    //kafka配置
    Snprintf(gstCSADAPTConf.notify_app_backend_topic, sizeof(gstCSADAPTConf.notify_app_backend_topic), config_file.GetConfigName("notify_app_backend_topic"));
    Snprintf(gstCSADAPTConf.notify_app_backend_group, sizeof(gstCSADAPTConf.notify_app_backend_group), config_file.GetConfigName("notify_app_backend_group"));
    gstCSADAPTConf.notify_app_backend_thread_num = ATOI(config_file.GetConfigName("notify_app_backend_thread_num"));


    Snprintf(gstCSADAPTConf.kafka_broker_ip, sizeof(gstCSADAPTConf.kafka_broker_ip), config_file.GetConfigName("kafka_broker_ip"));
    Snprintf(gstCSADAPTConf.special_mng_id, sizeof(gstCSADAPTConf.special_mng_id), config_file.GetConfigName("special_mng_id"));

    gstCSADAPTConf.write_thread_number = ATOI(config_file.GetConfigName("write_thread_number"));
    if (gstCSADAPTConf.write_thread_number < 2)
    {
        gstCSADAPTConf.write_thread_number = 2;
    }

    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));

    Snprintf(gstCSADAPTConf.log_db_ip, sizeof(gstCSADAPTConf.log_db_ip), config_file.GetConfigName("db_log_ip"));
    const char* log_db_port = config_file.GetConfigName("db_log_port");
    gstCSADAPTConf.log_db_port = ATOI(log_db_port);
    Snprintf(gstCSADAPTConf.log_db_database, sizeof(gstCSADAPTConf.log_db_database), config_file.GetConfigName("db_log_database"));

    Snprintf(gstCSADAPTConf.appbackend_analysis_topic, sizeof(gstCSADAPTConf.appbackend_analysis_topic), config_file.GetConfigName("kafka-notify-appbackend-analysis-topic"));
    Snprintf(gstCSADAPTConf.appbackend_analysis_group, sizeof(gstCSADAPTConf.appbackend_analysis_group), config_file.GetConfigName("kafka-notify-appbackend-analysis-group"));
    gstCSADAPTConf.appbackend_analysis_thread_num = ATOI(config_file.GetConfigName("notify-appbackend-analysis-thread-num"));

    Snprintf(gstCSADAPTConf.appbackend_push_topic, sizeof(gstCSADAPTConf.appbackend_push_topic), config_file.GetConfigName("kafka-notify-appbackend-push-topic"));
    Snprintf(gstCSADAPTConf.appbackend_push_group, sizeof(gstCSADAPTConf.appbackend_push_group), config_file.GetConfigName("kafka-notify-appbackend-push-group"));
    gstCSADAPTConf.appbackend_push_thread_num = ATOI(config_file.GetConfigName("notify-appbackend-push-thread-num"));

    Snprintf(gstCSADAPTConf.appbackend_notify_topic, sizeof(gstCSADAPTConf.appbackend_notify_topic), config_file.GetConfigName("kafka-notify-appbackend-notify-topic"));
    Snprintf(gstCSADAPTConf.appbackend_notify_group, sizeof(gstCSADAPTConf.appbackend_notify_group), config_file.GetConfigName("kafka-notify-appbackend-notify-group"));
    gstCSADAPTConf.appbackend_notify_thread_num = ATOI(config_file.GetConfigName("notify-appbackend-notify-thread-num"));

    Snprintf(gstCSADAPTConf.notify_web_message_topic, sizeof(gstCSADAPTConf.notify_web_message_topic), config_file.GetConfigName("kafka-notify-web-message-topic"));
    Snprintf(gstCSADAPTConf.notify_web_message_group, sizeof(gstCSADAPTConf.notify_web_message_group), config_file.GetConfigName("kafka-notify-web-message-group"));
    gstCSADAPTConf.notify_web_message_thread_num = ATOI(config_file.GetConfigName("notify-web-message-thread-num"));

    Snprintf(gstCSADAPTConf.notify_csconfig_topic, sizeof(gstCSADAPTConf.notify_csconfig_topic), config_file.GetConfigName("kafka-notify-csconfig-topic"));

    const char* gateway_num = config_file.GetConfigName("gateway_num");
    gstCSADAPTConf.gateway_num = ATOI(gateway_num);

    Snprintf(gstCSADAPTConf.oem_name, sizeof(gstCSADAPTConf.oem_name), config_file.GetConfigName("oem_name"));
    if (strlen(gstCSADAPTConf.oem_name) == 0) 
    {
        Snprintf(gstCSADAPTConf.oem_name, sizeof(gstCSADAPTConf.oem_name), "Akuvox");
    }

    return;
}

int ControlInit()
{
    //初始化所有单例,避免后面多线程竞争

    GetUnixSocketControlInstance()->Init(gstCSADAPTConf.szBeanStalkAddr);
    GetUnixSocketControlInstance()->Init(gstCSADAPTConf.beanstalk_backup_ip);
    GetUnixSocketControlInstance()->InitPduBeanstalk();
    GetAKCSViewInstance();
    return 0;
}


void sig_handler(int sig_no, siginfo_t* info, void* ctext)
{
    AK_LOG_WARN << "receive sig_no=" << sig_no;
    if (sig_no == SIGUSR1)
    {
        CConfigFileReader config_file(CSADAPD_CONF_FILE);
        const char* encrypt = config_file.GetConfigName("noencrypt");
        gstCSADAPTConf.nNoEncrypt = ATOI(encrypt);

        const char* log_level = config_file.GetConfigName("csadapt_loglevel");
        gstCSADAPTConf.nLogLevel = ATOI(log_level);
    }
    else
    {

    }
    return;
}

int sig_init()
{
    struct sigaction sa;
    sa.sa_flags = 0;
    sa.sa_sigaction = sig_handler;
    sa.sa_flags |= SA_SIGINFO;
    //安装信号
    if (sigaction(SIGUSR1, &sa, NULL) == -1) // 10
    {
        AK_LOG_WARN << "install SIGUSR1 error";
        return -1;
    }
    if (sigaction(SIGUSR2, &sa, NULL) == -1) // 12
    {
        AK_LOG_WARN << "install SIGUSR2 error";
        return -1;
    }
    return 0;
}

void StartIPCServer()
{
    //UnixSocket通信
    GetUnixSocketControlInstance()->IPCServerThread();
}

int InstanceInit()
{
    return CacheManager::getInstance()->Init("/usr/local/akcs/csadapt/conf/csadapt_redis.conf", "csadaptCacheInstances");
}

int OnDnsChange(const std::vector <std::string>& addrs)
{
    std::vector <std::string> new_addrs;
    std::stringstream etcd_ips_str;
    for (auto& ip : addrs)
    {
        std::string etcd_addr = ip;
        etcd_addr += ":8507";
        new_addrs.push_back(etcd_addr);
        etcd_ips_str << etcd_addr << ",";
        AK_LOG_INFO << "etcd new ip: " << etcd_addr;
        g_etcd_dns_res = 1;//解析过了
    }
    //更新为ip串 
    snprintf(gstCSADAPTConf.szEtcdServerAddr, sizeof(gstCSADAPTConf.szEtcdServerAddr), "%s", etcd_ips_str.str().c_str());
    AK_LOG_INFO << "etcd addrs: " << gstCSADAPTConf.szEtcdServerAddr;

    if (g_etcd_cli_mng && new_addrs.size() > 0)
    {
        g_etcd_cli_mng->UpdateEtcdAddrs(new_addrs);
    }
    return 0;
}

void DnsResolver()
{
    CConfigFileReader config_file(CSADAPD_CONF_FILE);
    //etcd集群信息形如:etcd_srv_net=127.0.0.1:8525,************:8523,...
    Snprintf(gstCSADAPTConf.szEtcdServerAddr, sizeof(gstCSADAPTConf.szEtcdServerAddr), config_file.GetConfigName("etcd_srv_net"));

    int need_res = 0;
    std::string etcd_net = gstCSADAPTConf.szEtcdServerAddr;
    for (unsigned int i = 0; i < etcd_net.size(); i++)
    {
        if (etcd_net[i] >= 'a' && etcd_net[i] <= 'z')
        {
            need_res = 1;
            break;
        }
    }

    if (need_res)
    {
        g_etcd_dns_res = 0;
    }
    else
    {
        //不需要解析
        g_etcd_dns_res = 1;
    }

    if (g_etcd_dns_res == 0)
    {
        evpp::EventLoop dns_loop;
        AkcsDNSResolver dns(gstCSADAPTConf.szEtcdServerAddr, &dns_loop);
        dns.SetOnChange(OnDnsChange);
        dns_loop.Run();
    }
    return;
}

//serverTag初始化
void ServerTagInit()
{
    Snprintf(gstCSADAPTConf.server_tag, sizeof(gstCSADAPTConf.server_tag), dbinterface::SystemSetting::GetServerTag().c_str());
}

int main(int argc, char* argv[])
{
    std::string config_file = CSADAPD_CONF_FILE;
    /*带参数控制命令处理*/
    if (argc >= 2)
    {
        if (!::strcasecmp(argv[1], "-c"))
        {
            char szPid[8];
            char szCmd[128];
            FILE* fp = fopen(PIDFILE, "r");
            if (fp)
            {
                fgets(szPid, sizeof(szPid), fp);
                fclose(fp);

                snprintf(szCmd, sizeof(szCmd), "kill -10 %s", szPid);
                system(szCmd);
            }
            return 0;
        }
        else if (!::strcasecmp(argv[1], "-d"))
        {
            if (argc < 4)
            {
                return 0;
            }
            char key[] = AES_ENCRYPT_KEY_V1;
            FileAESDecrypt(argv[2], key, argv[3]);
            return 0;
        }
        else if (!::strcasecmp(argv[1], "-C"))
        {
            if (argc < 3)
            {
                return 0;
            }
            config_file = argv[2];
        }
    }

    //先判断是否已经有同一个实例在后台运行了
    if (!IsSingleton2("/var/run/csadapt.pid"))
    {
        printf("There is another csadapt running in this system.");
        return -1;
    }

    GlogInit2(argv[0], "csadaptlog");

    /*安装信号*/
    sig_init();

    //配置中心初始化
    //一定要另起线程，不能用别的loop，因为这个会卡住，会影响别的执行
    memset(&gstCSADAPTConf, 0, sizeof(CSADAPT_CONF));
    std::thread dnsThread = std::thread(DnsResolver);
    while (!g_etcd_dns_res)
    {
        usleep(10);
    }

    ConfSrvInit();

    /* 读取配置文件 */
    ConfInit(config_file);

    ParseTimeZone("/usr/local/akcs/csadapt/conf/TimeZone.xml", g_time_zone_DST);

    int ret = -1;
    ret = DaoInit();
    if (0 != ret)
    {
        AK_LOG_WARN << "DaoInit failed";
        GlogClean2();
        return -1;
    }
    if (InstanceInit() != 0)
    {
        AK_LOG_FATAL << "init instance failed";
        return -1;
    }

    NewOfficeNotifyHandler::InitDataAnalysisKafkaProducer();
    //nsq消息发布 这个要先于ControlInit，因为里面有延时队列，如果程序重启时候队列里面有数据，
    //在消息处理时候，因为nsq没有初始化导致发布数据时候段错误
    std::thread mqProduceThread = std::thread(MQProduceInit);
    sleep(1);//等线程起来

    //启动控制器线程
    ControlInit();
    std::thread etcdCliThread = std::thread(EtcdSrvInit);
    std::string inner_addr = GetEth0IPAddr();
    std::string nsqd_tcp_addr = inner_addr + std::string(":8514");
    evpp::EventLoop loop;
    //删除图片的发布管理器
    g_nsq_pub_mng_ptr = new evnsq::Producer(&loop, evnsq::Option());
    g_nsq_pub_mng_ptr->ConnectToNSQDs(nsqd_tcp_addr);

    //kafka消费线程
    AKCS::Singleton<HandleKafkaNotifyTopicMsg>::instance().Init();
    AKCS::Singleton<HandleKafkaNotifyTopicMsg>::instance().StartKafkaConsumer();

    AKCS::Singleton<HandleKafkaPushTopicMsg>::instance().Init();
    AKCS::Singleton<HandleKafkaPushTopicMsg>::instance().StartKafkaConsumer();

    AKCS::Singleton<NotifyMessageKafkaConsumer>::instance().Init();
    AKCS::Singleton<NotifyMessageKafkaConsumer>::instance().StartKafkaConsumer();

    AKCS::Singleton<HandleKafkaNotifyBackendTopicMsg>::instance().Init();
    AKCS::Singleton<HandleKafkaNotifyBackendTopicMsg>::instance().StartKafkaConsumer();

    AKCS::Singleton<AkcsKafkaProducerNotifyConfig>::instance().Init(gstCSADAPTConf.notify_csconfig_topic, gstCSADAPTConf.kafka_broker_ip);


    //起ipcserver(unix socket)
    std::thread IPCServerThread(StartIPCServer);

    //csadapt exporter
    std::thread httpThread(startHttpServer); //port = 9241

    std::thread conf_watch_thread = std::thread(ConfWatch);

    LogDeliveryInit();

    GetZipkinAsynInstance()->Init();

    ServerTagInit();


    // 初始化metric单例
    InitMetricInstance();
    AK_LOG_INFO << "csadapt is starting";

    loop.Run();
    conf_watch_thread.join();
    IPCServerThread.join();
    etcdCliThread.join();
    mqProduceThread.join();
    dnsThread.join();
    GlogClean2();
    return 0;
}

