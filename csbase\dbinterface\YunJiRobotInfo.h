#ifndef __YUNJIROBOT_INFO_H__
#define __YUNJIROBOT_INFO_H__
#include <string>
#include <memory>
#include <vector>
#include "Rldb.h"
#include "RldbQuery.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/office/OfficePersonalAccount.h"

typedef struct CommunityYunJiRobotInfo_T
{
    int is_enable;
    char account_uuid[36];         
    char access_key_id[128];    
    char access_key_secret[128];

    CommunityYunJiRobotInfo_T() {
        memset(this, 0, sizeof(*this));
    }
}CommunityYunJiRobotInfo;

typedef std::vector<CommunityYunJiRobotInfo> YunJiRobotInfoList;

namespace dbinterface
{

class YunJiRobotInfo
{
public:
    static int GetYunJiRobotInfoByAccountUUID(const std::string& account_uuid, CommunityYunJiRobotInfo& community_yunji_info);
    static int GetYunJiRobotInfoList(YunJiRobotInfoList& yunji_robot_info_list);
private:
    YunJiRobotInfo() = delete;
    ~YunJiRobotInfo() = delete;   
    static void GetYunJiRobotInfoFromSql(CommunityYunJiRobotInfo& community_yunji_info, CRldbQuery& query);
};

}
#endif

