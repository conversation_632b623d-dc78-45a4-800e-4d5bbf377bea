#ifndef __BACKEND_P2P_MSG_H__
#define __BACKEND_P2P_MSG_H__

#include "AkcsCommonDef.h"
#include "AK.BackendCommon.pb.h"

class BackendP2PMsgControl
{
public:
    static void PushMsg2Route(const google::protobuf::MessageLite* msg, int project_type);
    static AK::BackendCommon::BackendP2PBaseMessage CreateP2PBaseMsg(int msgid, int type, const std::string &uid, csmain::DeviceType conntype, int projecttype);
    static csmain::DeviceType DevProjectTypeToDevType(int projecttype);
private:
};

#endif // __BACKEND_P2P_MSG_H__