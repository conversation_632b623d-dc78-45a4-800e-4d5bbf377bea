#include <map>
#include <set>
#include <mutex>
#include "util.h"
#include "YunJiRobotClientMng.h"

YunJiRobotRpcClientMng* YunJiRobotRpcClientMng::instance_ = nullptr;

YunJiRobotRpcClientMng* YunJiRobotRpcClientMng::Instance()
{
	if (!instance_)
    {
		instance_ = new YunJiRobotRpcClientMng();
	}
	return instance_;
}

void YunJiRobotRpcClientMng::AddYunJiRobotRpcSrv(const std::string &csyunjirobot_grpc_addr, const YunJiRobotRpcClientPtr& csyunjirobot_rpc_cli)
{
    std::string logic_srv_id = "csyunjirobot_";
    std::string::size_type pos = csyunjirobot_grpc_addr.find(":");
    logic_srv_id += csyunjirobot_grpc_addr.substr(0, pos);

    std::lock_guard<std::mutex> lock(rpc_clis_mutex_);
    csyunjirobot_rpc_clis_.insert(std::pair<std::string, YunJiRobotRpcClientPtr>(logic_srv_id, csyunjirobot_rpc_cli));
    AK_LOG_INFO << "add csyunjirobot_rpc_client " << logic_srv_id;
} 

void YunJiRobotRpcClientMng::UpdateYunJiRobotRpcSrv(const std::set<std::string> &csyunjirobot_grpc_addr) 
{
	//TODO后面逻辑服务器数量多的时候,用两个set取差集加速处理
    std::lock_guard<std::mutex> lock(rpc_clis_mutex_);
    for(const auto &rpc_addr : csyunjirobot_grpc_addr)
    {
        auto it = csyunjirobot_rpc_clis_.find(rpc_addr);
        if(it == csyunjirobot_rpc_clis_.end())
        {
        	std::string logic_srv_id="csyunjirobot_";
            std::string::size_type pos = rpc_addr.find(":");
        	logic_srv_id += rpc_addr.substr(0, pos);
            YunJiRobotRpcClientPtr call_cli_ptr(new YunJiRobotRpcClient(rpc_addr));
            csyunjirobot_rpc_clis_.insert(std::pair<std::string, YunJiRobotRpcClientPtr>(logic_srv_id, call_cli_ptr));
            AK_LOG_INFO << "add csyunjirobot_rpc_client " << logic_srv_id;
        }
        else
        {
            //如果没有改变，那么rpc客户端会自己重连
        }
    }
    
	//再检查下线的csyunjirobot rpc srv
	if(csyunjirobot_rpc_clis_.size() == csyunjirobot_grpc_addr.size())
    {
		return;
    }
	for (auto it = csyunjirobot_rpc_clis_.begin(); it != csyunjirobot_rpc_clis_.end();)
    {
        auto it2 = csyunjirobot_grpc_addr.find(it->first);
        if(it2 == csyunjirobot_grpc_addr.end())
        {
			AK_LOG_INFO << "del csyunjirobot_rpc_client";
            csyunjirobot_rpc_clis_.erase(it++);
        }
        else
        {
        	it++;
        }
    }
} 

YunJiRobotRpcClientPtr YunJiRobotRpcClientMng::GetRpcClientInstance(const std::string &logic_srv_id)
{
    std::lock_guard<std::mutex> lock(rpc_clis_mutex_);
    auto it = csyunjirobot_rpc_clis_.find(logic_srv_id);
    if(it == csyunjirobot_rpc_clis_.end())
    {
    	AK_LOG_INFO << "cannot find [" << logic_srv_id << "] csyunjirobot server";
    	return nullptr;
    }
    else
    {
        return csyunjirobot_rpc_clis_[logic_srv_id];
    }
    return nullptr;
} 

YunJiRobotRpcClientPtr YunJiRobotRpcClientMng::GetRpcRandomClientInstance()
{    
    auto it = std::next(csyunjirobot_rpc_clis_.begin(), current_index_.fetch_add(1) % csyunjirobot_rpc_clis_.size());
    return it->second;
}