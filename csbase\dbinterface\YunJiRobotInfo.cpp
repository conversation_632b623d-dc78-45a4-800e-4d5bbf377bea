#include <sstream>
#include <string.h>
#include "util.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "YunJiRobotInfo.h"
#include "Rldb/Rldb.h"
#include "Rldb/RldbQuery.h"
#include "Rldb/ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

namespace dbinterface
{

static const std::string yunji_robot_info_sec = " IsEnable, AccountUUID, AccessKeyId, AccessKeySecret ";

void YunJiRobotInfo::GetYunJiRobotInfoFromSql(CommunityYunJiRobotInfo& community_yunji_info, CRldbQuery& query)
{    
    community_yunji_info.is_enable = ATOI(query.GetRowData(0));
    Snprintf(community_yunji_info.account_uuid, sizeof(community_yunji_info.account_uuid), query.GetRowData(1));
    Snprintf(community_yunji_info.access_key_id, sizeof(community_yunji_info.access_key_id), query.GetRowData(2));
    Snprintf(community_yunji_info.access_key_secret, sizeof(community_yunji_info.access_key_secret), query.GetRowData(3));
}

int YunJiRobotInfo::GetYunJiRobotInfoByAccountUUID(const std::string& account_uuid, CommunityYunJiRobotInfo& community_yunji_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << yunji_robot_info_sec << " from YJRobotInfo where AccountUUID = '" << account_uuid << "'";
    
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetYunJiRobotInfoFromSql(community_yunji_info, query);
        return 0;
    }
    return -1;
}


int YunJiRobotInfo::GetYunJiRobotInfoList(YunJiRobotInfoList& yunji_robot_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << yunji_robot_info_sec << " from YJRobotInfo";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        CommunityYunJiRobotInfo community_yunji_info;
        GetYunJiRobotInfoFromSql(community_yunji_info, query);
        yunji_robot_info_list.push_back(community_yunji_info);
    }
    return 0;
}

}