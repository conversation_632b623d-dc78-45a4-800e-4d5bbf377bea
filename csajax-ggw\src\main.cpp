#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <thread>
#include <fcntl.h>
#include <sys/stat.h>
#include <unistd.h>
#include <pthread.h>
#include <thread>

#include "HttpServer.h"
#include "ServiceConf.h"
#include "AkLogging.h"
#include "ConfigFileReader.h"
#include "sqs/AkcsSqsConsumer.h"
#include "SqsMessageProcessControl.h"
#include "Metric.h"
#include "AjaxKafkaMessageProducer.h"
#include "AkcsAppInit.h"

SERVICE_CONF g_service_conf;
AkcsSqsConsumer* g_sqs_consumer = nullptr;
extern AjaxNotifyMsg g_ajax_kafka_notify_msg;

#define PIDFILE "/var/run/csajax-ggw.pid"
#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
#define FILE_MODE (S_IRWXU | S_IRWXG | S_IRWXO)
#define SERVICE_CONF_FILE "/usr/local/akcs/csajax-ggw/conf/csajax-ggw.conf"
#define SERVICE_SQS_CONF_FILE "/usr/local/akcs/csajax-ggw/conf/csajax-ggw_sqs.conf"

void glogInit(const char* argv)
{
    google::InitGoogleLogging(argv);
    google::SetLogDestination(google::GLOG_INFO, "/var/log/csajax-ggwlog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/csajax-ggwlog/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/csajax-ggwlog/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/csajax-ggwlog/FATAL");
    FLAGS_logbufsecs = 0;
    google::SetStderrLogging(google::GLOG_WARNING); // 输出到标准输出的时候大于INFO级别的都输出;
    FLAGS_max_log_size = 50;    //单日志文件最大50M
}

void glogClean()
{
    google::ShutdownGoogleLogging();
}

void ConfInit()
{
    memset(&g_service_conf, 0, sizeof(SERVICE_CONF));
    CConfigFileReader config_file(SERVICE_CONF_FILE);

    Snprintf(g_service_conf.kafka_broker_ip, sizeof(g_service_conf.kafka_broker_ip), config_file.GetConfigName("kafka_ajax_global_broker_ip"));
    Snprintf(g_service_conf.kafka_ajax_notify_msg_topic, sizeof(g_service_conf.kafka_ajax_notify_msg_topic), config_file.GetConfigName("kafka_ajax_notify_msg_topic"));

    const char* log_encrypt = config_file.GetConfigName("log_encrypt");
    AkLogControlSingleton::GetInstance().SetEncryptSwitch(ATOI(log_encrypt));
    const char* log_trace = config_file.GetConfigName("log_trace");    
    AkLogControlSingleton::GetInstance().SetTraceidSwitch(ATOI(log_trace));

    g_service_conf.dispatch_thread_nums = ATOI(config_file.GetConfigName("dispatch_thread_nums"));
}

int main(int argc, char* argv[])
{
    //先判断是否已经有同一个实例在后台运行了
    if (!IsSingleton2(PIDFILE))
    {
        printf("another csajax-ggw has been running in this sytem.");
        return -1;
    }

    //glog初始化    
    glogInit(argv[0]);

    //配置中心初始化
	memset(&g_service_conf, 0, sizeof(SERVICE_CONF));
    
    //读取配置文件
    ConfInit();

    // kafka生产者
    g_ajax_kafka_notify_msg.InitKafkaProducer(g_service_conf.kafka_broker_ip, g_service_conf.kafka_ajax_notify_msg_topic);

    // 处理线程
    GetSqsMessageProcessControlInstance()->Init(g_service_conf.dispatch_thread_nums);

    // 初始化并启动SQS消费者管理器
    g_sqs_consumer = new AkcsSqsConsumer();
    g_sqs_consumer->Init(SERVICE_SQS_CONF_FILE);
    g_sqs_consumer->RegisterMessageCallback(
        [](const std::string& message) -> bool {
            return GetSqsMessageProcessControlInstance()->AddMessageProcessTask(message);
        }
    );
    g_sqs_consumer->Start();    

    // start http server.
    LOG_INFO << "start sever!";
    std::thread httpThread(startHttpServer);//port = 9283
    // 初始化metric单例
    InitMetricInstance();
    
    AK_LOG_INFO << "csajax-ggw is starting";

    glogClean();
    return 0;
}