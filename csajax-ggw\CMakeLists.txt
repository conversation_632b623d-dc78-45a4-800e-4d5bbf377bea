CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (csajax  CXX)
SET(CSBASE_DIR ../csbase)
SET(DEPENDENT_LIBRARIES libcsbase.a pthread libevent.so libglog.so libevpp.so -lssl -lcrypto -lcpprest -levpp -levent -lboost_system libaws-cpp-sdk-core.so libaws-cpp-sdk-sqs.so -lcurl libcppkafka.so
librdkafka.so librdkafka++.so)

SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR}")
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(DBINTERFACE_FILES_OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR})
include(${CMAKE_CURRENT_SOURCE_DIR}/../csbase/common_scripts/dbinterface_files_list.cmake)
LINK_DIRECTORIES(${CSBASE_SOURCE_DIR} ${CSBASE_SOURCE_DIR}/thirdlib ${CSBASE_SOURCE_DIR}/thirdlib/oss)


AUX_SOURCE_DIRECTORY(./src SRC_LIST)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/encrypt SRC_LIST_ENCRYPT)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/metrics SRC_LIST_BASE_METRICS)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/sqs SRC_LIST_BASE_SQS)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/kafka SRC_LIST_BASE_KAFKA)
AUX_SOURCE_DIRECTORY(${CSBASE_DIR}/jsoncpp0.5/src SRC_LIST_JSON)

link_directories(${CMAKE_CURRENT_SOURCE_DIR}/${CSBASE_DIR} 
${CMAKE_CURRENT_SOURCE_DIR}/${CSBASE_DIR}/thirdlib 
${CMAKE_CURRENT_SOURCE_DIR}/${CSBASE_DIR}/evpp/lib 
/usr/local/lib)


SET(BASE_LIST_INC 
     ${CSBASE_DIR} 
     ${CSBASE_DIR}/oss/include
     ${CSBASE_DIR}/evpp 
     ${CSBASE_DIR}/encrypt
     ${CSBASE_DIR}/metrics 
     ${CSBASE_DIR}/sqs
     ${CSBASE_DIR}/jsoncpp0.5/include
     ./src)

ADD_DEFINITIONS(-std=c++11 -g -Werror -Wno-unused-parameter -Wno-deprecated -Wno-deprecated-copy -Wno-shift-negative-value)
                           
include_directories(${BASE_LIST_INC} ./src /usr/local/boost/include)

SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)

add_executable(csajax-ggw ${SRC_LIST} ${SRC_LIST_ENCRYPT} ${SRC_LIST_BASE_METRICS} ${SRC_LIST_BASE_SQS} 
${SRC_LIST_BASE_KAFKA} ${prefixed_file_list} ${SRC_LIST_JSON})

set_target_properties(csajax-ggw PROPERTIES LINK_FLAGS "-Wl,-rpath,/usr/local/akcs/csajax-ggw/lib")
target_link_libraries(csajax-ggw  ${DEPENDENT_LIBRARIES})
