#include "SqsMessageProcessControl.h"
#include "AkLogging.h"
#include "gid/SnowFlakeGid.h"
#include "ThreadLocalSingleton.h"
#include "SqsMessageDispatcher.h"

SqsMessageProcessControl* SqsMessageProcessControl::instance_ = nullptr;

SqsMessageProcessControl::~SqsMessageProcessControl()
{
    stop_ = true;
    for (auto& thread : threads_)
    {
        if (thread.joinable())
        {
            thread.join();
        }
    }
}

void SqsMessageProcessControl::Init(int thread_num)
{
    threads_.resize(thread_num);
    for (auto& thread : threads_)
    {
        thread = std::thread(&SqsMessageProcessControl::ProcessMessage, this);
    }
    AK_LOG_INFO << "Sqs Message Process Thread Start Success,thread_num=" << thread_num;
}

bool SqsMessageProcessControl::AddMessageProcessTask(const std::string& message)
{
    message_queue_.Push(message);
    return true;
}

void SqsMessageProcessControl::ProcessMessage()
{
    // 设置glog线程traceid
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);

    while (!stop_)
    {
        std::string message = message_queue_.Pop(); //阻塞等待
        if (message.empty())
        {
            AK_LOG_WARN << "message is empty, drop it";
            continue;
        }

        AK_LOG_INFO << "dispatch message";
        SqsMessageDispatcher::Dispatch(message);
    }
}

SqsMessageProcessControl* SqsMessageProcessControl::GetInstance()
{
    if (instance_ == NULL)
    {
        instance_ = new SqsMessageProcessControl();
    }

    return instance_;
}

SqsMessageProcessControl* GetSqsMessageProcessControlInstance()
{
    return SqsMessageProcessControl::GetInstance();
}