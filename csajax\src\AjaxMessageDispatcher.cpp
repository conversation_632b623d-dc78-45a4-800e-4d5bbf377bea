#include "AjaxMessageDispatcher.h"
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "BackendP2PMsgControl.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/AjaxDeviceInfo.h"
#include "AkcsMsgDef.h"
#include "AkLogging.h"

void AjaxMessageDispatcher::Dispatch(const SqsMsgKvList& msg_kv_list)
{
    AjaxDeviceInfoList device_info_list;
    dbinterface::AjaxDeviceInfo::GetLoginAjaxDeviceInfoByHubID(msg_kv_list.at("hub_id"), device_info_list);
    for (auto& device_info : device_info_list)
    {
        int project_type = project::RESIDENCE;

        ResidentDev dev;

        if (0 == dbinterface::ResidentPerDevices::GetUUIDDev(device_info.dev_uuid, dev))
        {
            project_type = project::PERSONAL;
        } 
        else if (0 == dbinterface::ResidentDevices::GetUUIDDev(device_info.dev_uuid, dev))
        {
            project_type = project::RESIDENCE;
        }
        else
        {
            AK_LOG_WARN << "device uuid not found. uuid=" << device_info.dev_uuid;
            continue;
        }

        SendAjaxNotifyMsg2Route(dev.mac, msg_kv_list.at("body"), project_type);
    }
}

void AjaxMessageDispatcher::SendAjaxNotifyMsg2Route(const std::string& mac, const std::string& msg_body, int project_type)
{
    AK::BackendCommon::BackendP2PBaseMessage base;
    base = BackendP2PMsgControl::CreateP2PBaseMsg(AKCS_M2R_P2P_AJAX_MESSAGE_NOTIFY, TransP2PMsgType::TO_DEV_MAC, mac,
                BackendP2PMsgControl::DevProjectTypeToDevType(project_type), project_type);

    AK::Server::P2PAjaxMessageNotify p2p_msg;
    p2p_msg.set_body(msg_body);
    p2p_msg.set_mac(mac);

    base.mutable_p2pajaxmessagenotify2()->CopyFrom(p2p_msg);
    BackendP2PMsgControl::PushMsg2Route(&base, project_type);
}

