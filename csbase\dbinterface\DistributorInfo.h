#ifndef __DISTRIBUTOR_INFO_H__
#define __DISTRIBUTOR_INFO_H__
#include <string>
#include <memory>
#include <stdint.h>
#include "Rldb.h"
#include "RldbQuery.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/office/OfficePersonalAccount.h"

typedef struct DistributorInfoSt_T
{
    int  show_villa_monitor;        // Ins单住户是否显示绑定Monitor模块 0:off, 1:on
    int  enable_pin_encrypt;        // pin是否加密, 0:不加密, 1:加密
    int  enable_yale_lock;          // dis是否开通yale锁 0:off, 1:on
    int  enable_offline_solution;   // 是否开启室内机离线转流方案              0:off, 1:on
    int  enable_create_subdis;      // super是否给dis是否开启Create Sub Dis开关 0:off, 1:on
    int  enable_project_landline;   // dis是否开启了项目落地开关 0:off, 1:on
    int  enable_rfcard_control;     // 是否允许用户操作RfCard，0:不允许, 1:允许
    int  enable_apt_charge_plan;    // 是否开启按apt收费开关
    int  enable_yunji_robot;        // 是否开启云迹机器人
    char account[256];              // account表的account字段
    char oem_name[64];              // dis所属的oem

    DistributorInfoSt_T() {
        memset(this, 0, sizeof(*this));
    }
}DistributorInfoSt;

enum DisOemType
{
    OEM_TYPE_AKUVOX = 0,
    OEM_TYPE_HAGER = 1,
};

namespace dbinterface
{
class DistributorInfo
{

public:
    DistributorInfo();
    ~DistributorInfo();

    static int GetDistributorInfo(const std::string& account, DistributorInfoSt& distributor_info);
    
    static int GetDisInfoByProjectId(int project_id, DistributorInfoSt& dis_info);
    static int GetDistributorInfoByPersonalAccount(const ResidentPerAccount& personal_account, DistributorInfoSt& dis_info);
    static int GetDistributorInfoByDisUUID(const std::string& uuid, DistributorInfoSt& dis_info);
    static int GetDistributorInfoByPMAccountUUID(const std::string& pm_account_uuid, DistributorInfoSt& dis_info);
    static int GetDistributorInfoByOfficePersonalAccount(const OfficeAccount& office_account, DistributorInfoSt& dis_info);
    
    static int GetDisInfoByUserAccount(const std::string& user, int project_type, DistributorInfoSt& dis_info);
    static int GetDistributorInfoByNode(const std::string& personal_account, DistributorInfoSt& distributor_info);   
    static int GetDistributorInfoByNodeUUID(const std::string& node_uuid, DistributorInfoSt& distributor_info);
private:
    static void GetDistributorInfoFromSql(DistributorInfoSt& distributor_info, CRldbQuery& query);
};

}
#endif

