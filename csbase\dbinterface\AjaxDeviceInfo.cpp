#include "dbinterface/AjaxDeviceInfo.h"
#include "ConnectionManager.h"
#include "util.h"

namespace dbinterface
{

static const std::string ajax_device_info_sec = "DeviceUUID,HubID,LoginStatus";

void AjaxDeviceInfo::GetAjaxDeviceInfoFromSql(CRldbQuery& query, AjaxDeviceInfoSt& device_info)
{
    Snprintf(device_info.dev_uuid, sizeof(device_info.dev_uuid), query.GetRowData(0));
    Snprintf(device_info.hub_id, sizeof(device_info.hub_id), query.GetRowData(1));
    device_info.login_status = ATOI(query.GetRowData(2));
}

int AjaxDeviceInfo::GetLoginAjaxDeviceInfoByHubID(const std::string& hub_id, AjaxDeviceInfoList& device_info_list)
{
    std::stringstream streamsql;
    streamsql << "select " << ajax_device_info_sec << " from AjaxDeviceInfo where HubID = '" << hub_id << "' and LoginStatus = 1";

    GET_DB_CONN_ERR_RETURN(conn, -1);

    CRldbQuery query(conn.get());
    query.Query(streamsql.str());
    while (query.MoveToNextRow())
    {
        AjaxDeviceInfoSt device_info;
        GetAjaxDeviceInfoFromSql(query, device_info);
        device_info_list.push_back(device_info);
    }
    return 0;
}

int AjaxDeviceInfo::InsertOrUpdateAjaxDeviceInfo(const std::string& dev_uuid, const std::string& hub_id, AjaxLoginStatus login_status, const std::string& uuid)
{
    std::map<std::string, std::string> insert_str_datas;
    insert_str_datas.emplace("UUID", uuid);
    insert_str_datas.emplace("DeviceUUID", dev_uuid);
    insert_str_datas.emplace("HubID", hub_id);
    
    std::map<std::string, int> insert_int_datas;
    insert_int_datas.emplace("LoginStatus", (int)login_status);
    
    std::map<std::string, std::string> update_str_datas;
    update_str_datas.emplace("HubID", hub_id);
    
    std::map<std::string, int> update_int_datas;
    update_int_datas.emplace("LoginStatus", (int)login_status);
    
    std::string table_name= "AjaxDeviceInfo";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldb* conn = db_conn.get();
    int ret = conn->InsertOrUpdateData(table_name, insert_str_datas, insert_int_datas, update_str_datas, update_int_datas);
    
    if (ret < 0) {
        AK_LOG_WARN << "Failed to insert or update data";
    }
    return ret;
}


}

