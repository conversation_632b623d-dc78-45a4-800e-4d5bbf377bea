#ifndef __DB_SUB_DIS_MNG_LIST_H__
#define __DB_SUB_DIS_MNG_LIST_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct SubDisMngListInfo_T
{
    char installer_uuid[36];
    char distributor_uuid[36];
    char uuid[36];
    SubDisMngListInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} SubDisMngListInfo;

namespace dbinterface {

class SubDisMngList
{
public:
    static int GetSubDisMngListByInstallerUUID(const std::string& installer_uuid, SubDisMngListInfo& sub_dis_mng_list_info);
    
private:
    SubDisMngList() = delete;
    ~SubDisMngList() = delete;
    static void GetSubDisMngListFromSql(SubDisMngListInfo& sub_dis_mng_list_info, CRldbQuery& query);
};

}
#endif