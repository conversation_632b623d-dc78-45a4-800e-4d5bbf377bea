#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "SmartLockPrivateKey.h"

#define PIN_COLUMN_NAME_PERSONAL "PersonalPrivateKeyUUID"
#define PIN_COLUMN_NAME_COMMUNITY "CommPerPrivateKeyUUID"
namespace dbinterface {

static const std::string smart_lock_private_key_info_sec = " UUID,SmartLockUUID,Pin,Status,PersonalAccountUUID,CommPerPrivateKeyUUID,PersonalPrivateKeyUUID,CredentialId,LockReportStatus ";

void SmartLockPrivateKey::GetSmartLockPrivateKeyFromSql(SmartLockPrivateKeyInfo& smart_lock_private_key_info, CRldbQuery& query)
{
    Snprintf(smart_lock_private_key_info.uuid, sizeof(smart_lock_private_key_info.uuid), query.GetRowData(0));
    Snprintf(smart_lock_private_key_info.smart_lock_uuid, sizeof(smart_lock_private_key_info.smart_lock_uuid), query.GetRowData(1));
    Snprintf(smart_lock_private_key_info.pin, sizeof(smart_lock_private_key_info.pin), query.GetRowData(2));
    smart_lock_private_key_info.status = ATOI(query.GetRowData(3));
    Snprintf(smart_lock_private_key_info.personal_account_uuid, sizeof(smart_lock_private_key_info.personal_account_uuid), query.GetRowData(4));
    Snprintf(smart_lock_private_key_info.comm_per_private_key_uuid, sizeof(smart_lock_private_key_info.comm_per_private_key_uuid), query.GetRowData(5));
    Snprintf(smart_lock_private_key_info.personal_private_key_uuid, sizeof(smart_lock_private_key_info.personal_private_key_uuid), query.GetRowData(6));
    smart_lock_private_key_info.credential_id = ATOI(query.GetRowData(7));
    smart_lock_private_key_info.lock_report_status = ATOI(query.GetRowData(8));
    return;
}

int SmartLockPrivateKey::GetSmartLockPrivateKeyByCommPerPrivateKeyUUID(const std::string& comm_per_private_key_uuid, SmartLockPrivateKeyInfo& smart_lock_private_key_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << smart_lock_private_key_info_sec << " from SmartLockPrivateKey where CommPerPrivateKeyUUID = '" << comm_per_private_key_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSmartLockPrivateKeyFromSql(smart_lock_private_key_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SmartLockPrivateKeyInfo by CommPerPrivateKeyUUID failed, CommPerPrivateKeyUUID = " << comm_per_private_key_uuid;
        return -1;
    }
    return 0;
}

int SmartLockPrivateKey::GetSmartLockPrivateKeyByPersonalPrivateKeyUUID(const std::string& personal_private_key_uuid, SmartLockPrivateKeyInfo& smart_lock_private_key_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << smart_lock_private_key_info_sec << " from SmartLockPrivateKey where PersonalPrivateKeyUUID = '" << personal_private_key_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSmartLockPrivateKeyFromSql(smart_lock_private_key_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SmartLockPrivateKeyInfo by PersonalPrivateKeyUUID failed, PersonalPrivateKeyUUID = " << personal_private_key_uuid;
        return -1;
    }
    return 0;
}

int SmartLockPrivateKey::GetSmartLockPrivateKeyBySmartLockAndCredentialId(const std::string& smart_lock_uuid, int credential_id, SmartLockPrivateKeyInfo& smart_lock_private_key_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << smart_lock_private_key_info_sec << " from SmartLockPrivateKey where SmartLockUUID = '" << smart_lock_uuid << "' and CredentialId = " << credential_id;
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSmartLockPrivateKeyFromSql(smart_lock_private_key_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SmartLockPrivateKeyInfo by SmartLockUUID and CredentialId failed, SmartLockUUID = " << smart_lock_uuid << ", CredentialId = " << credential_id;
        return -1;
    }
    return 0;
}

int SmartLockPrivateKey::DeleteSmartLockRelatedPrivateKey(const std::string& smart_lock_uuid)
{
    std::stringstream stream_sql;
    stream_sql << "delete from SmartLockPrivateKey where SmartLockUUID = '" << smart_lock_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    db_conn->Execute(stream_sql.str());
    return 0;
}

int SmartLockPrivateKey::DeleteSmartLockCommPerPrivateKey(const std::string& comm_per_private_key_uuid)
{
    std::stringstream stream_sql;
    stream_sql << "delete from SmartLockPrivateKey where CommPerPrivateKeyUUID = '" << comm_per_private_key_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    db_conn->Execute(stream_sql.str());
    return 0;
}

int SmartLockPrivateKey::DeleteSmartLockPersonalPrivateKey(const std::string& personal_private_key_uuid)
{
    std::stringstream stream_sql;
    stream_sql << "delete from SmartLockPrivateKey where PersonalPrivateKeyUUID = '" << personal_private_key_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    db_conn->Execute(stream_sql.str());

    return 0;
}

int SmartLockPrivateKey::InsertOrUpdateSmartLockPrivateKey(const SmartLockPrivateKeyInfo& smartlock_private_key_info)
{
    std::string pin_column_name;
    std::string pin_uuid;

    if (strlen(smartlock_private_key_info.personal_private_key_uuid) > 0)
    {
        pin_column_name = PIN_COLUMN_NAME_PERSONAL;
        pin_uuid = smartlock_private_key_info.personal_private_key_uuid;
    }
    else if (strlen(smartlock_private_key_info.comm_per_private_key_uuid) > 0)
    {
        pin_column_name = PIN_COLUMN_NAME_COMMUNITY;
        pin_uuid = smartlock_private_key_info.comm_per_private_key_uuid;
    }

    std::map<std::string, std::string> insert_str_datas;
    insert_str_datas.emplace("UUID", smartlock_private_key_info.uuid);
    insert_str_datas.emplace("SmartLockUUID", smartlock_private_key_info.smart_lock_uuid);
    insert_str_datas.emplace("PersonalAccountUUID", smartlock_private_key_info.personal_account_uuid);
    insert_str_datas.emplace(pin_column_name, pin_uuid);
    
    std::map<std::string, int> insert_int_datas;
    insert_int_datas.emplace("Status", smartlock_private_key_info.status);
    insert_int_datas.emplace("CredentialId", smartlock_private_key_info.credential_id);
    insert_int_datas.emplace("LockReportStatus", smartlock_private_key_info.lock_report_status);

    std::map<std::string, std::string> update_str_datas;
    
    std::map<std::string, int> update_int_datas;
    update_int_datas.emplace("CredentialId", smartlock_private_key_info.credential_id);
    update_int_datas.emplace("Status", smartlock_private_key_info.status);
    update_int_datas.emplace("LockReportStatus", smartlock_private_key_info.lock_report_status);
    
    if (smartlock_private_key_info.lock_report_status < 0)
    {
        insert_int_datas.erase("LockReportStatus");
        update_int_datas.erase("LockReportStatus");
    }

    if (smartlock_private_key_info.credential_id < 0)
    {
        insert_int_datas.erase("CredentialId");
        update_int_datas.erase("CredentialId");
    }

    std::string table_name= "SmartLockPrivateKey";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldb* conn = db_conn.get();
    int ret = conn->InsertOrUpdateData(table_name, insert_str_datas, insert_int_datas, update_str_datas, update_int_datas);
    
    if (ret < 0) {
        AK_LOG_WARN << "Failed to insert or update data";
    }
    return ret;
}

int SmartLockPrivateKey::UpdateSmartLockPrivateKeyStatus(const SmartLockPrivateKeyInfo& smartlock_private_key_info)
{
    std::string pin_column_name;
    std::string pin_uuid;

    if (strlen(smartlock_private_key_info.personal_private_key_uuid) > 0)
    {
        pin_column_name = PIN_COLUMN_NAME_PERSONAL;
        pin_uuid = smartlock_private_key_info.personal_private_key_uuid;
    }
    else if (strlen(smartlock_private_key_info.comm_per_private_key_uuid) > 0)
    {
        pin_column_name = PIN_COLUMN_NAME_COMMUNITY;
        pin_uuid = smartlock_private_key_info.comm_per_private_key_uuid;
    }
    
    std::stringstream stream_sql;
    stream_sql << "update SmartLockPrivateKey set Status = " << smartlock_private_key_info.status 
            << " where " << pin_column_name << " = '" << pin_uuid << "' and SmartLockUUID = '" << smartlock_private_key_info.smart_lock_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    db_conn->Execute(stream_sql.str());
    return 0;
}

int SmartLockPrivateKey::DeleteCommunitySmartLockPrivateKeyByPinUUIDListStr(const std::string& pin_uuid_list_str)
{
    if (pin_uuid_list_str.empty())
    {
        return 0;
    }

    std::stringstream stream_sql;
    stream_sql << "delete from SmartLockPrivateKey where CommPerPrivateKeyUUID in (" << pin_uuid_list_str << ")";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    db_conn->Execute(stream_sql.str());
    return 0;
}

int SmartLockPrivateKey::DeletePersonalSmartLockPrivateKeyByPinUUIDAndSmartLockUUIDNotInList(const std::string& pin_uuid, const std::string& smart_lock_uuid_not_in_list_str)
{
    if (smart_lock_uuid_not_in_list_str.empty())
    {
        return 0;
    }

    std::stringstream stream_sql;
    stream_sql << "delete from SmartLockPrivateKey where PersonalPrivateKeyUUID = '" << pin_uuid << "' and SmartLockUUID not in (" << smart_lock_uuid_not_in_list_str << ")";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    db_conn->Execute(stream_sql.str());
    return 0;
}

int SmartLockPrivateKey::DeletePersonalSmartLockPrivateKeyBySmartLockUUIDAndPinUUID(const std::string& smart_lock_uuid, const std::string& pin_uuid)
{
    std::stringstream stream_sql;
    stream_sql << "delete from SmartLockPrivateKey where SmartLockUUID = '" << smart_lock_uuid << "' and PersonalPrivateKeyUUID = '" << pin_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    db_conn->Execute(stream_sql.str());
    return 0;
}
}
