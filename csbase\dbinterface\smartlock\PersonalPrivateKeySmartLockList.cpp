#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "PersonalPrivateKeySmartLockList.h"

namespace dbinterface {

static const std::string personal_private_key_smart_lock_list_info_sec = " UUI<PERSON>,SmartLockUUID,PersonalPrivate<PERSON><PERSON>UUID,PersonalAccountUUID,MainUserUUID ";

void PersonalPrivateKeySmartLockList::GetPersonalPrivateKeySmartLockListFromSql(PersonalPrivateKeySmartLockListInfo& personal_private_key_smart_lock_list_info, CRldbQuery& query)
{
    Snprintf(personal_private_key_smart_lock_list_info.uuid, sizeof(personal_private_key_smart_lock_list_info.uuid), query.GetRowData(0));
    Snprintf(personal_private_key_smart_lock_list_info.smart_lock_uuid, sizeof(personal_private_key_smart_lock_list_info.smart_lock_uuid), query.GetRowData(1));
    Snprintf(personal_private_key_smart_lock_list_info.personal_private_key_uuid, sizeof(personal_private_key_smart_lock_list_info.personal_private_key_uuid), query.GetRowData(2));
    Snprintf(personal_private_key_smart_lock_list_info.personal_account_uuid, sizeof(personal_private_key_smart_lock_list_info.personal_account_uuid), query.GetRowData(3));
    Snprintf(personal_private_key_smart_lock_list_info.main_user_uuid, sizeof(personal_private_key_smart_lock_list_info.main_user_uuid), query.GetRowData(4));
    return;
}

int PersonalPrivateKeySmartLockList::GetPersonalPrivateKeySmartLockListBySmartLockUUID(const std::string& smart_lock_uuid, PersonalPrivateKeySmartLockListInfo& personal_private_key_smart_lock_list_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << personal_private_key_smart_lock_list_info_sec << " from PersonalPrivateKeySmartLockList where SmartLockUUID = '" << smart_lock_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetPersonalPrivateKeySmartLockListFromSql(personal_private_key_smart_lock_list_info, query);
    }
    else
    {
        AK_LOG_WARN << "get PersonalPrivateKeySmartLockListInfo by SmartLockUUID failed, SmartLockUUID = " << smart_lock_uuid;
        return -1;
    }
    return 0;
}


int PersonalPrivateKeySmartLockList::GetSmartLockSupportPinListByNode(const std::string& node, SmartLockSupportPinListMap& smart_lock_support_pin_list_map)
{
    std::stringstream stream_sql;
    stream_sql << "select KL.UUID,KL.SmartLockUUID,KL.PersonalPrivateKeyUUID,KL.PersonalAccountUUID,KL.MainUserUUID from PersonalPrivateKeySmartLockList KL "
               << " left join PersonalAccount P on KL.MainUserUUID = P.UUID "
               << " where P.Account = '" << node << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        PersonalPrivateKeySmartLockListInfo personal_private_key_smart_lock_list_info;
        GetPersonalPrivateKeySmartLockListFromSql(personal_private_key_smart_lock_list_info, query);

        // 判断personal_private_key_smart_lock_list_map中是否存在key_uuid，如果存在则向unordered_set添加元素，否则创建新的unordered_set并添加元素
        auto iter = smart_lock_support_pin_list_map.find(personal_private_key_smart_lock_list_info.smart_lock_uuid);
        if (iter != smart_lock_support_pin_list_map.end()) 
        {
            iter->second.insert(personal_private_key_smart_lock_list_info.personal_private_key_uuid);
        } 
        else 
        {
            std::unordered_set<std::string> set;
            set.insert(personal_private_key_smart_lock_list_info.personal_private_key_uuid);
            smart_lock_support_pin_list_map.insert(std::make_pair(personal_private_key_smart_lock_list_info.smart_lock_uuid, set));
        }
    }

    return 0;
    
}

int PersonalPrivateKeySmartLockList::GetSmartLockUUIDListByPinUUID(const std::string& pin_uuid, std::vector<std::string>& smart_lock_uuid_list)
{
    std::stringstream stream_sql;
    stream_sql << "select SmartLockUUID from PersonalPrivateKeySmartLockList where PersonalPrivateKeyUUID = '" << pin_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        smart_lock_uuid_list.push_back(query.GetRowData(0));
    }

    return 0;
}

}