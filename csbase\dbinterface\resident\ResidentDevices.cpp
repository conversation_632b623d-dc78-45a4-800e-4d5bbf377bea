#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/resident/ResidentDevices.h"
#include <string.h>
#include "AkLogging.h"
#include "AkcsMonitor.h"
#include "AkcsPasswdConfuse.h"
#include "util.h"
#include "util_time.h"
#include "AkcsMysqlSegFlag.h"
#include "dbinterface/VersionModel.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "ConnectionManager.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"

namespace dbinterface
{


static const std::string devices_sec = " ID,Type,MngAccountID,UnitID,Node,MAC,Location,Grade,ConfigMD5,\
    SipAccount,SipPwd,NetGroupNumber,RtspPwd,ContactMD5,StairShow,Relay,Config,DclientVer,SipType,Flags,\
    IPAddress,SecurityRelay,FaceMD5,UserMetaMD5,ScheduleMD5,AccSrvID,outerIP,UUID,Gateway,SubnetMask,PrimaryDNS,SecondaryDNS,\
    Firmware,Hardware,Status,LastConnection,PrivatekeyMD5,RfidMD5,AuthCode,ProjectType,Brand,Port,Arming,IsIPV6,IsDynamicsIV,IsRepost,\
    Function,AccountUUID,CommunityUnitUUID,PersonalAccountUUID,AllowEndUserHoldDoor,CreateTime,AllowEndUserMonitor,WiredIPAddress,ConnUpdateVer,WiredSubnetMask ";


ResidentDevices::ResidentDevices()
{

}

void ResidentDevices::GetDevicesFromSql(ResidentDev& dev, CRldbQuery& query)
{
    dev.id = ATOI(query.GetRowData(0));
    dev.dev_type = ATOI(query.GetRowData(1));
    dev.project_mng_id = ATOI(query.GetRowData(2));
    dev.unit_id = ATOI(query.GetRowData(3));
    Snprintf(dev.node, sizeof(dev.node), query.GetRowData(4));
    Snprintf(dev.mac, sizeof(dev.mac), query.GetRowData(5));
    Snprintf(dev.location, sizeof(dev.location), query.GetRowData(6));
    dev.grade = ATOI(query.GetRowData(7));
    Snprintf(dev.config_md5, sizeof(dev.config_md5), query.GetRowData(8));
    Snprintf(dev.sip, sizeof(dev.sip), query.GetRowData(9));
    Snprintf(dev.sippwd, sizeof(dev.sippwd), query.GetRowData(10));
    dev.netgroup_num = ATOI(query.GetRowData(11));
    Snprintf(dev.rtsppwd, sizeof(dev.rtsppwd), query.GetRowData(12)); 
    Snprintf(dev.contact_md5, sizeof(dev.contact_md5), query.GetRowData(13));
    dev.stair_show = ATOI(query.GetRowData(14));
    Snprintf(dev.relay, sizeof(dev.relay), query.GetRowData(15));
    Snprintf(dev.autop_config, sizeof(dev.autop_config), query.GetRowData(16));
    dev.dclient_ver = ATOI(query.GetRowData(17));
    dev.sip_type = ATOI(query.GetRowData(18));
    dev.flags = ATOI(query.GetRowData(19));
    Snprintf(dev.ipaddr, sizeof(dev.ipaddr), query.GetRowData(20));
    Snprintf(dev.security_relay, sizeof(dev.security_relay), query.GetRowData(21));
    Snprintf(dev.face_md5, sizeof(dev.face_md5), query.GetRowData(22));
    Snprintf(dev.user_mate_md5, sizeof(dev.user_mate_md5), query.GetRowData(23));
    Snprintf(dev.schedule_md5, sizeof(dev.schedule_md5), query.GetRowData(24));
    Snprintf(dev.acc_srv_id, sizeof(dev.acc_srv_id), query.GetRowData(25));
    Snprintf(dev.outer_ip, sizeof(dev.outer_ip), query.GetRowData(26));
    Snprintf(dev.uuid, sizeof(dev.uuid), query.GetRowData(27));
    Snprintf(dev.gateway, sizeof(dev.gateway), query.GetRowData(28));
    Snprintf(dev.subnet_mask, sizeof(dev.subnet_mask), query.GetRowData(29));
    Snprintf(dev.primary_dns, sizeof(dev.primary_dns), query.GetRowData(30));
    Snprintf(dev.secondary_dns, sizeof(dev.secondary_dns), query.GetRowData(31));
    Snprintf(dev.sw_ver, sizeof(dev.sw_ver), query.GetRowData(32));    
    Snprintf(dev.hw_ver, sizeof(dev.hw_ver), query.GetRowData(33));
    dev.status = ATOI(query.GetRowData(34));
    Snprintf(dev.last_connection, sizeof(dev.last_connection), query.GetRowData(35));
    Snprintf(dev.private_key_md5, sizeof(dev.private_key_md5), query.GetRowData(36));
    Snprintf(dev.rf_id_md5, sizeof(dev.rf_id_md5), query.GetRowData(37));
    Snprintf(dev.auth_code, sizeof(dev.auth_code), query.GetRowData(38));
    dev.project_type = ATOI(query.GetRowData(39));
    dev.brand = ATOI(query.GetRowData(40));
    dev.port = ATOI(query.GetRowData(41));
    dev.arming = ATOI(query.GetRowData(42));
    dev.is_ipv6 = ATOI(query.GetRowData(43));
    dev.is_dy_iv = ATOI(query.GetRowData(44));    
    dev.repost  = ATOI(query.GetRowData(45));
    dev.fun_bit = strtoul(query.GetRowData(46), nullptr, 10);
    Snprintf(dev.project_uuid, sizeof(dev.project_uuid), query.GetRowData(47));
    Snprintf(dev.unit_uuid, sizeof(dev.unit_uuid), query.GetRowData(48));
    dev.allow_end_user_hold_door = ATOI(query.GetRowData(49));

    Snprintf(dev.node_uuid, sizeof(dev.node_uuid), query.GetRowData(49));
    dev.allow_end_user_hold_door = ATOI(query.GetRowData(50));
    Snprintf(dev.create_time, sizeof(dev.create_time), query.GetRowData(51));
    dev.allow_end_user_monitor = ATOI(query.GetRowData(52));
    Snprintf(dev.wired_ipaddr, sizeof(dev.wired_ipaddr), query.GetRowData(53));
    dev.conn_version = ATOULL(query.GetRowData(54));    

    std::string sw_ver = dev.sw_ver;
    auto pos = sw_ver.find(".");
    if (pos != std::string::npos)
    {
        dev.oem_id = ATOI(sw_ver.substr(pos + 1).c_str());
        dev.firmware = ATOI(sw_ver.substr(0,pos).c_str());
    }

    if (dev.project_type == project::OFFICE)
    {
        dev.conn_type = csmain::OFFICE_DEV;
    }
    else
    {
        dev.conn_type = csmain::COMMUNITY_DEV;
    }

    //是不是单住户 办公和社区 = 0
    dev.is_personal = 0;  
    
    if (dev.grade != csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        dev.is_public = 1;
    }

    std::string srcpwd = dev.rtsppwd;
    PasswdDecode(srcpwd.c_str(), srcpwd.size(), dev.rtsppwd, sizeof(dev.rtsppwd));
    srcpwd = dev.sippwd;
    PasswdDecode(srcpwd.c_str(), srcpwd.size(), dev.sippwd, sizeof(dev.sippwd));
    return;
}

int ResidentDevices::InitDevicesBySip(const std::string& sip, ResidentDev &dev)
{
    std::stringstream sql;
    sql << "select " << devices_sec <<" from Devices where SipAccount = '"
              << sip
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetDevicesFromSql(dev, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int ResidentDevices::GetDevicesBySip(const std::string& sip, ResidentDev &dev)
{
    return InitDevicesBySip(sip, dev);    
}

int ResidentDevices::GetLocationAndNodeBySip(const std::string& sip, std::string& location, std::string& node)
{
    ResidentDev dev;
    int ret = InitDevicesBySip(sip, dev);
    if (0 == ret)
    {
        location = dev.location;
        node = dev.node;
    }
    else
    {
        location = "";
        node = "";
    }
    return ret;
}

int ResidentDevices::GetDevTypeBySip(const std::string& sip)
{
    ResidentDev dev;
    if (0 == InitDevicesBySip(sip, dev))
    {
        return dev.dev_type;
    }

    return -1;
}

std::string ResidentDevices::GetLocationBySip(const std::string& sip)
{
    ResidentDev dev;
    if (0 == InitDevicesBySip(sip, dev))
    {
        return dev.location;
    }
    else
    {
        return "";
    }
}

int ResidentDevices::GetNodeDevList(const std::string& node, ResidentDeviceList &devlist)
{
    if ( node.length() == 0 )
    {
        AK_LOG_WARN << "GetNodeDevList failed. node=null!";
        return -1;
    }
    std::stringstream sql;
    sql << "select " << devices_sec <<" from Devices where Node = '"
              << node
              << "' and Grade=" << csmain::COMMUNITY_DEVICE_TYPE_PERSONAL;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return (devlist.size() > 0) ? 0 : -1;  
}

//只查室内机
int ResidentDevices::GetNodeIndoorDevList(const std::string& node, ResidentDeviceList &devlist)
{
    if ( node.length() == 0 )
    {
        AK_LOG_WARN << "GetNodeDevList failed. node=null!";
        return -1;
    }
    std::stringstream sql;
    sql << "select " << devices_sec <<" from Devices where Node = '"
              << node
              << "' and Type=" << DEVICE_TYPE_INDOOR << " and Grade=" << csmain::COMMUNITY_DEVICE_TYPE_PERSONAL;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return (devlist.size() > 0) ? 0 : -1;  
}

//社区下室内机
int ResidentDevices::GetAllAptIndoorDevices(int project_id, std::set<std::string> &dev_list)
{
    if (project_id == 0 )
    {
        AK_LOG_WARN << "GetAllAptIndoorDevices failed. project id = 0";
        return -1;
    }
    std::stringstream sql;
    sql << "select MAC from Devices where MngAccountID = '" << project_id
              << "' and Type=" << DEVICE_TYPE_INDOOR << " and Grade=" << csmain::COMMUNITY_DEVICE_TYPE_PERSONAL;

    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        dev_list.insert(query.GetRowData(0));
    }
    return 0;  
}

/*获取部门所有公共设备*/
int ResidentDevices::GetDepartmentDevList(uint32_t department_id, ResidentDeviceList &devlist)
{
    std::stringstream sql;
    sql << "select " << devices_sec <<" from Devices where UnitID = '" << department_id
        << "' and Grade ="<< csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;  
}

/*获取最外层公共设备*/
int ResidentDevices::GetPubDevList(uint32_t mng_id, ResidentDeviceList &devlist)
{
    std::stringstream sql;
    sql << "select " << devices_sec <<" from Devices where MngAccountID = '"
              << mng_id
              << "' and Grade ="<< csmain::COMMUNITY_DEVICE_TYPE_PUBLIC;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;  
}

int ResidentDevices::UpdateDevMD5(ResidentDev &dev, DEVICES_MD5_TYPE type)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (nullptr == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    std::string md5_sec = "";
    std::string md5 = "";
    switch(type)
    {
        case CONFIG_MD5:
            md5 = "ConfigMD5";
            md5_sec = dev.config_md5;
            break;
        case FACE_MD5:
            md5 = "FaceMD5";
            md5_sec = dev.face_md5;
            break;
        case USER_MATE_MD5:
            md5 = "UserMetaMD5";
            md5_sec = dev.user_mate_md5;
            break;
        case SCHEDULE_MD5:
            md5 = "ScheduleMD5";
            md5_sec = dev.schedule_md5;;
            break;
        case CONTACT_MD5:
            md5 = "ContactMD5";
            md5_sec = dev.contact_md5;;
            break;                
    }

    std::stringstream sql;
    sql << "update Devices set  " << md5 <<"='" << md5_sec <<"' where ID =" << dev.id;           

    int ret = tmp_conn->Execute(sql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        char error_msg[1024];
        snprintf(error_msg, sizeof(error_msg), "Update devices failed, ID is [%d], %s is [%s]", dev.id, md5.c_str(), md5_sec.c_str());
        AK_LOG_WARN << error_msg;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csadapt", error_msg, AKCS_MONITOR_ALARM_UPDATE_DEVICE_MD5_FAILED);
    }       
    ReleaseDBConn(conn);
    return 0;
}

//这里csconfig会异步处理写文件，不能用mac。要用id或者uuid
int ResidentDevices::UpdateMd5ByID(uint32_t id, SHADOW_TYPE shadow_type, const std::string& value)
{
    std::stringstream sql;
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::string column = dbinterface::Shadow::GetMd5ColumnByType(shadow_type);
    if(column.size() == 0)
    {
        AK_LOG_WARN << "shadow type is illegal.";
        ReleaseDBConn(conn);
        return -1;
    }
    
    sql << "UPDATE Devices set " << column 
         << "='"  << value << "' where ID=" << id;
   
    int ret = tmp_conn->Execute(sql.str()) >= 0 ? 0 : -1; 
    if (-1 == ret)
    {
        char error_msg[1024];
        snprintf(error_msg, sizeof(error_msg), "Update Devices failed, ID is [%d], %s is [%s]", id, column.c_str(), value.c_str());
        AK_LOG_WARN << error_msg;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("dbinterface", error_msg, AKCS_MONITOR_ALARM_UPDATE_DEVICE_MD5_FAILED);
    }     
    ReleaseDBConn(conn);
    return ret;
}

int ResidentDevices::UpdateDevMD5(ResidentDeviceList &dev_list, DEVICES_MD5_TYPE type)
{
    for(auto dev : dev_list)
    {
        UpdateDevMD5(dev, type);
    }
    return 0;
}

/*获取小区所有的管理机*/
int ResidentDevices::GetAllMngDevList(uint32_t mng_id, ResidentDeviceList &devlist)
{
    std::stringstream sql;
    sql << "select " << devices_sec <<" from Devices where MngAccountID="<< mng_id << " and Type=" << DEVICE_TYPE_MANAGEMENT;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;  
}

int ResidentDevices::GetAllPubDevList(uint32_t mng_id, ResidentDeviceList &devlist)
{
    std::stringstream sql;
    sql << "select " << devices_sec <<" from Devices where MngAccountID = '"<< mng_id
        << "' and (Grade ="<< csmain::COMMUNITY_DEVICE_TYPE_PUBLIC << " or Grade =" << csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT <<")";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;       
}

int ResidentDevices::GetMacDev(const std::string& mac, ResidentDeviceList &dev_list)
{
    ResidentDev dev;
    GetMacDev(mac, dev);
    dev_list.push_back(dev);
    return 0;
}

int ResidentDevices::GetMacDev(const std::string& mac, ResidentDev &dev)
{
    std::stringstream sql;
    sql << "/*master*/select " << devices_sec <<" from Devices where Mac = '"
              << mac
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetDevicesFromSql(dev, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;  
}

int ResidentDevices::GetDevByID(int id, ResidentDev &dev)
{
    std::stringstream sql;
    sql << "select " << devices_sec <<" from Devices where ID = "
              << id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetDevicesFromSql(dev, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;  
}

int ResidentDevices::GetAllDevList(uint32_t mng_id, ResidentDeviceList &devlist)
{
    std::stringstream sql;
    sql << "select " << devices_sec <<" from Devices where MngAccountID="<< mng_id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;      
}

int ResidentDevices::GetMacListDevList(const std::set<std::string> &mac_set, ResidentDeviceList &devlist)
{
    int size = mac_set.size();
    if(size == 0)
    {
        return 0;
    }

    std::stringstream sql;
    sql << "select " << devices_sec << " from Devices where mac in(";
    std::string mas_str = ListToSeparatedFormatString(mac_set);
    sql << mas_str << ")";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }   
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;
}

int ResidentDevices::GetSipDev(const std::string& sip, ResidentDev &dev)
{
    std::stringstream sql;
    sql << "select " << devices_sec <<" from Devices where SipAccount = '" << sip << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, -1)
    CRldbQuery query(tmp_conn.get());
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetDevicesFromSql(dev, query);
    }
    else
    {
        return -1;
    }
    return 0;     
}

int ResidentDevices::GetUUIDDev(const std::string& uuid, ResidentDev &dev)
{
    std::stringstream sql;
    sql << "select " << devices_sec <<" from Devices where UUID = '"
              << uuid
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetDevicesFromSql(dev, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;  
}

int ResidentDevices::GetDevUUIDByMac(const std::string& mac, std::string& uuid)
{   
    std::stringstream sql;
    sql << "select UUID from Devices where MAC = '" << mac <<"'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if(query.MoveToNextRow())
    {
        uuid = query.GetRowData(0);
    }
    ReleaseDBConn(conn);
    return 0;      
}

int ResidentDevices::GetMacByUUID(const std::string& uuid, std::string& mac)
{
    std::stringstream sql;
    sql << "select MAC from Devices where UUID = '" << uuid <<"'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if(query.MoveToNextRow())
    {
        mac = query.GetRowData(0);
    }
    ReleaseDBConn(conn);
    return 0;      
}

int ResidentDevices::GetCommunityMngDevByMngID(int nMngID, std::vector<DEVICE_SETTING>& devs)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(conn.get());
    std::stringstream sql;
    sql << "select MAC,Grade,UnitID from Devices where MngAccountID = "
        << nMngID <<" and type= " << DEVICE_TYPE_MANAGEMENT;
    query.Query(sql.str());
    int ret = -1;
    while (query.MoveToNextRow())
    {
        DEVICE_SETTING dev;
        memset(&dev, 0, sizeof(dev));
        Snprintf(dev.mac, sizeof(dev.mac), query.GetRowData(0));
        dev.grade = ATOI(query.GetRowData(1));
        dev.unit_id = ATOI(query.GetRowData(2));
        devs.push_back(dev);
        ret = 0;
    }
    ReleaseDBConn(conn);

    return ret;
}

//更新设备版本号/状态等信息到数据库
int ResidentDevices::UpdateDeviceInfo(DEVICE_SETTING* device_setting)
{
    if (device_setting == NULL)
    {
        AK_LOG_WARN << "pDeviceSettingql is null.";
        return -1;
    }
    std::string logic_srv_ip = GetEth0IPAddr();    
    int flags_relay = device_setting->relay_status << 4;    //flags的4-7位为relay状态
    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql),"update Devices set SubnetMask='%s',Gateway='%s',PrimaryDNS='%s',SecondaryDNS='%s',Status=%u,outerIP='%s',\
                    Port=%u,Firmware='%s',Hardware='%s',LastConnection='%s',DclientVer=%d, AccSrvID='%s',Arming='%u',Flags=Flags&%d|%d, \
                    IPAddress='%s',IsIPV6=%d,IsDynamicsIV=%d,Function=%lu, WiredIPAddress='%s',WiredSubnetMask='%s',ConnUpdateVer=ConnUpdateVer + 1 where ID=%u",
                    device_setting->subnet_mask,
                   device_setting->gateway,
                   "",//device_setting->primary_dns, 线上出现过多次dns是特殊字符的,我们没有用到直接去掉
                   "",//device_setting->secondary_dns,
                   device_setting->status,
                   device_setting->outer_ip,
                   device_setting->port,
                   device_setting->SWVer,
                   device_setting->HWVer,
                   device_setting->last_connection,
                   device_setting->dclient_version,
                   logic_srv_ip.c_str(),
                   device_setting->indoor_arming,
                   DEVICE_FLAGS_WITHOUT_RELAY,
                   flags_relay,
                   device_setting->ip_addr,
                   device_setting->is_ipv6,
                   device_setting->dynamics_iv,
                   device_setting->fun_bit,
                   device_setting->wired_ip_addr,
                   device_setting->wired_subnet_mask,
                   device_setting->id);
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    conn->BeginTransAction();

    int affact_row = conn->Execute(sql);
    int ret = affact_row >= 0 ? affact_row : -1; //影响0行也是正确的
    uint64_t conn_version = 0;
    ::snprintf(sql, sizeof(sql),"select ConnUpdateVer from Devices where ID=%u ", device_setting->id);

    CRldbQuery query(tmp_conn);
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        conn_version = ATOULL(query.GetRowData(0));
    }

    device_setting->conn_version = conn_version;

    conn->EndTransAction();
    ReleaseDBConn(conn);
    return ret;
}

//根据MAC地址设置设备链接断开时间
int ResidentDevices::SetDeviceDisConnTime(const std::string& mac, const std::string& logic_srv_ip)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update Devices set LastDisConn=CURRENT_TIMESTAMP where MAC='%s' and AccSrvID='%s'",
        mac.c_str(), logic_srv_ip.c_str()
    );

    int ret = db_conn->Execute(sql) > 0 ? 0 : -1;
    return ret;
}

//根据MAC地址设置设备连接状态
int ResidentDevices::SetDeviceDisConnectStatus(const std::string& mac, const std::string& logic_srv_ip, uint64_t conn_version)
{
    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql),"update Devices set Status=0, DoorRelayStatus='',DoorSeRelayStatus='',LastDisConn=CURRENT_TIMESTAMP,ConnUpdateVer=%lu + 1  where MAC='%s' and AccSrvID='%s' and ConnUpdateVer = %lu",
                  conn_version,
                  mac.c_str(),
                  logic_srv_ip.c_str(),
                  conn_version);
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    int affact_row = conn->Execute(sql);
    int ret = affact_row > 0 ? 0 : -1; 
    if (affact_row == 0)
    {
        AK_LOG_WARN << "update device status=0 is affact row 0. mac=" << mac << " ver:" << conn_version;
    }
    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}

//根据MAC地址更新设备relay开关状态

int ResidentDevices::GetAllDeviceSettingCount()
{
    char sql[1024] = "";
    int device_count = 0;
    ::snprintf(sql, sizeof(sql),"select count(*) from Devices");

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(tmp_conn);
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        device_count = ATOI(query.GetRowData(0));
    }

    ::snprintf(sql, sizeof(sql),"select count(*) from PersonalDevices");
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        device_count += ATOI(query.GetRowData(0));
    }

    ReleaseDBConn(conn);
    return device_count;
}

int ResidentDevices::IsMacDeviceExist(const char* mac)
{
    if (mac == NULL)
    {
        return 0;
    }
    char sql[1024] = "";
    int exist = 0;
    ::snprintf(sql, sizeof(sql),"select 1 from Devices where Mac = '%s' limit 1;", mac);

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return 0;
    }

    CRldbQuery query(tmp_conn);
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        exist = ATOI(query.GetRowData(0));
    }
    if (1 == exist)
    {
        ReleaseDBConn(conn);
        return 1;
    }

    ::snprintf(sql, sizeof(sql),"select 1 from PersonalDevices where Mac = '%s' limit 1;", mac);
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        exist = ATOI(query.GetRowData(0));
    }

    ReleaseDBConn(conn);
    return exist;
}

/*查找这个社区下所有和此用户相关的联动设备(仅门口机、梯口机)*/
int ResidentDevices::GetRootCommunityDeviceListByAccount(const std::string account, int mng_id, std::vector<std::string>& mac_list)
{
    if (account.empty())
    {
        AK_LOG_WARN << "parameter error! account is null!";
        return -1;
    }

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql),"select D.MAC from PersonalAccount P left join Devices D \
        on (%d = D.MngAccountID and D.Grade = %d) or (P.UnitID = D.UnitID and D.Grade = %d) \
        or (P.Account = D.Node and D.Grade = %d) where P.Account = '%s' and (D.Type = %d or D.Type = %d);",
                  mng_id, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT,
                  csmain::COMMUNITY_DEVICE_TYPE_PERSONAL, account.c_str(), DEVICE_TYPE_STAIR, DEVICE_TYPE_DOOR);

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    query.Query(sql);
    while (query.MoveToNextRow())
    {
        mac_list.push_back(query.GetRowData(0));
    }
    ReleaseDBConn(conn);
    return 0;
}

int ResidentDevices::SetAllDevDisconnect(const std::string& csmain_sid)
{
    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql),"update Devices set Status=0 where AccSrvID = '%s'", csmain_sid.c_str());
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    int ret = conn->Execute(sql) > 0 ? 0 : -1;
    char sql2[1024] = "";
    ::snprintf(sql2, sizeof(sql2),"update PersonalDevices set Status=0 where AccSrvID = '%s'", csmain_sid.c_str());
    ret = conn->Execute(sql2) > 0 ? 0 : -1;
    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}

int ResidentDevices::SetDevAuthCode(DEVICE_SETTING* pDevice)
{
    char sql[1024] = "";
    if (pDevice->is_personal)
    {
        ::snprintf(sql, sizeof(sql),"update PersonalDevices set AuthCode='%s' where Mac='%s'", pDevice->auth_code, pDevice->mac);
    }
    else
    {
        ::snprintf(sql, sizeof(sql),"update Devices set AuthCode='%s' where Mac='%s'", pDevice->auth_code, pDevice->mac);
    }
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    int ret = conn->Execute(sql) > 0 ? 0 : -1;

    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}

int ResidentDevices::DevRegistInCloud(const std::string& mac)
{
    char sql[1024] = "";
    int exist = 0;
    ::snprintf(sql, sizeof(sql),"select ID from DeviceForRegister where Mac = '%s' limit 1;", mac.c_str());

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return 0;
    }

    CRldbQuery query(tmp_conn);
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        exist = ATOI(query.GetRowData(0));
    }
    ReleaseDBConn(conn);
    return exist;
}

//设置arming状态
int ResidentDevices::SetDeviceArmingStatus(const std::string& mac, int indoor_arming)
{
    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql),"update Devices set Arming=%d where mac='%s'", indoor_arming, mac.c_str());
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    int ret = conn->Execute(sql) > 0 ? 0 : -1;
    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}

int ResidentDevices::SetDeviceSensorTirggerInfo(const std::string& mac, int home, int away, int sleep)
{
    //TODO:先简单一个个处理，后期看下是否有更好的方法
    char sql[1024] = "";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    if (home)
    {
        ::snprintf(sql, sizeof(sql),"update Devices set Flags=Flags|%d where MAC='%s';",
            MysqlSegFlags::STHOME, mac.c_str());
        conn->Execute(sql);
    }
    else
    {
        ::snprintf(sql, sizeof(sql),"update Devices set Flags=Flags^%d where MAC='%s';",
            MysqlSegFlags::STHOME, mac.c_str());
        conn->Execute(sql);
    }

    if (away)
    {
        ::snprintf(sql, sizeof(sql),"update Devices set Flags=Flags|%d where MAC='%s';",
            MysqlSegFlags::STAWAY, mac.c_str());
        conn->Execute(sql);
    }
    else
    {
        ::snprintf(sql, sizeof(sql),"update Devices set Flags=Flags^%d where MAC='%s';",
            MysqlSegFlags::STAWAY, mac.c_str());
        conn->Execute(sql);
    }

    if (sleep)
    {
        ::snprintf(sql, sizeof(sql),"update Devices set Flags=Flags|%d where MAC='%s';",
            MysqlSegFlags::STSLEEP, mac.c_str());
        conn->Execute(sql);
    }
    else
    {
        ::snprintf(sql, sizeof(sql),"update Devices set Flags=Flags^%d where MAC='%s';",
            MysqlSegFlags::STSLEEP, mac.c_str());
        conn->Execute(sql);
    }

    //释放数据库连接
    ReleaseDBConn(conn);
    return 0;
}

int ResidentDevices::SetDeviceRelayStatus(const std::string& mac, int relay_status)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    int ret = 0;
    char sql[256] = {0};
    ::snprintf(sql, sizeof(sql), "update Devices set Flags=Flags&%d|%d where MAC='%s';", 
        DEVICE_FLAGS_WITHOUT_RELAY, (relay_status<<4), mac.c_str());
    ret = conn->Execute(sql) >= 0 ? 0 : -1;


    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}

bool ResidentDevices::CheckIndoorPlan(const std::string& account)
{
    bool ret = true;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ret;
    }

    std::stringstream sql;
    sql << "select MAC from DevicesSpecial where Account = '" << account << "'";

    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        std::string mac = query.GetRowData(0);
        std::stringstream sql1;
        sql1 << "select Flags from Devices where MAC = '" << mac << "'";
        query.Query(sql1.str());
        if (query.MoveToNextRow())
        {
            int flag = dbinterface::SwitchHandle(ATOI(query.GetRowData(0)), DeviceSwitch::INDOOR_ONLINE);   
            ret = (flag == 1 ? true : false);
        }
    }

    //释放数据库连接
    ReleaseDBConn(conn);

    return ret;
}

int ResidentDevices::GetRepostDev(const std::string& uid, std::string &mac)
{
    ResidentDeviceList dev_list;
    ResidentPerAccount user_info;

    if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(uid, user_info))
    {  
        return -1; 
    }

    std::string node = user_info.account;
    if (user_info.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
    {
        ResidentPerAccount node_info;
        if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(user_info.parent_id, node_info))
        {
            node = node_info.account;
        }
    }

    if (0 != GetNodeDevList(node, dev_list))
    {
        return -1;
    }

    for (const auto& dev : dev_list)
    {
        if (dev.dev_type == DEVICE_TYPE_INDOOR && dev.repost && dev.status)
        {
            mac = dev.mac;
            return 0;
        }
    }

    return -1;
}

//运维的接口
int ResidentDevices::GetMacByFirmware(const std::string& firmware, std::vector<std::string>& macs)
{
    int ret = -1;
    std::stringstream sql;
    sql << "select MAC from Devices where Firmware = '"
        << firmware
        << "' and Status=1 union all select MAC from PersonalDevices where Firmware ='"
        << firmware
        << "' and Status=1 limit 10";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ret = 0;
        std::string mac;
        mac = query.GetRowData(0);
        macs.push_back(mac);
    }

    ReleaseDBConn(conn);
    return ret;     
}
//社区更新设备door relay status信息
int ResidentDevices::UpdateDoorRelayStatus(const std::string &mac, 
const std::string &door_relay_status, const std::string& door_se_relay_status)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    std::stringstream sql;
    sql << "update Devices set DoorRelayStatus = '"
        << door_relay_status
        << "', DoorSeRelayStatus = '"
        << door_se_relay_status
        << "' where MAC = '"
        << mac
        << "'";
    int ret = 0;
    ret = conn->Execute(sql.str()) >= 0 ? 0 : -1;
    ReleaseDBConn(conn);
    return ret;
}

int ResidentDevices::GetCommunityAllNodeDevList(uint32_t mng_id, ResidentDeviceList& dev_list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(tmp_conn);

    std::stringstream stream_sql;
    stream_sql << "select " << devices_sec << " from Devices where MngAccountID = " << mng_id  << " and Grade = " << csmain::COMMUNITY_DEVICE_TYPE_PERSONAL;

    query.Query(stream_sql.str());
    
    
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);
        dev_list.push_back(dev);
    }

    ReleaseDBConn(conn);
    return 0;
}

int ResidentDevices::GetRootPubAndUnitPubDeviceSettingList(uint unit_id, uint mng_id, ResidentDeviceList& dev_list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(tmp_conn);

    std::stringstream stream_sql;
    stream_sql << "select " << devices_sec << " from Devices where " 
               << "(UnitID = " << unit_id << " and Grade = " << csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT << ") or "
               << "(MngAccountID = " <<  mng_id << " and Grade = " << csmain::COMMUNITY_DEVICE_TYPE_PUBLIC << ") "
               << "order by Grade ASC";

    query.Query(stream_sql.str());
    
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);
        dev_list.push_back(dev);
    }

    ReleaseDBConn(conn);
    return 0;
}

/*获取社区家庭用户关联的家庭设备*/
void ResidentDevices::GetNodesDevList(const std::vector<std::string> &node_list, std::set<std::string> &mac_set)
{
    int size = node_list.size();
    if (size <= 0 )
    {
        return;
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::string accounts = ListToSeparatedFormatString(node_list);
    std::stringstream stream_sql;
    stream_sql << "select mac from Devices where Node in (" << accounts << ") and Grade = " << csmain::COMMUNITY_DEVICE_TYPE_PERSONAL;

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        mac_set.insert(query.GetRowData(0));
    }
    ReleaseDBConn(conn);   
    
}

/*获取社区家庭用户关联的家庭设备*/
void ResidentDevices::GetNodesIndoorOrMngDevList(const std::string& node, std::set<std::string> &mac_set)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::stringstream stream_sql;
    stream_sql << "select mac from Devices where Node = '" << node << "' and Type in (" << DEVICE_TYPE_INDOOR << "," << DEVICE_TYPE_MANAGEMENT << ")";

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        mac_set.insert(query.GetRowData(0));
    }
    ReleaseDBConn(conn);
    
}


//获取默认权限组设备列表
void ResidentDevices::GetPubMacsByMngIDAndUnitID(int community_id, int unit_id, std::set<std::string> &mac_set)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    CRldbQuery query(tmp_conn);
    
    std::stringstream stream_sql;
    stream_sql << "select mac from Devices where "
                << "(UnitID = '" << unit_id << "' and Grade = "<< csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT << ") or " 
                << "(MngAccountID = '" << community_id << "' and Grade = " << csmain::COMMUNITY_DEVICE_TYPE_PUBLIC <<")";
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        mac_set.insert(query.GetRowData(0));
    }  
    ReleaseDBConn(conn);
}

void ResidentDevices::GetAllContactListDevices(const std::string &node, unsigned int unit_id, unsigned int manager_account_id, std::set<std::string> &mac_set)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    CRldbQuery query(tmp_conn);

    //先查公共设备
    std::stringstream stream_sql1;
    stream_sql1 << "select mac from Devices where "
                << "(UnitID = '" << unit_id << "' and Grade = "<< csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT << ") or " 
                << "(MngAccountID = '" << manager_account_id << "' and Grade = " << csmain::COMMUNITY_DEVICE_TYPE_PUBLIC <<")";

    query.Query(stream_sql1.str());
    while (query.MoveToNextRow())
    {
        mac_set.insert(query.GetRowData(0));
    }

    //再查个人设备
    std::stringstream stream_sql2;
    stream_sql2 << "select mac from Devices where Node = '" << node << "' and Grade =" << csmain::COMMUNITY_DEVICE_TYPE_PERSONAL;
    
    query.Query(stream_sql2.str());     
    while (query.MoveToNextRow())
    {
        mac_set.insert(query.GetRowData(0));
    }
    
    ReleaseDBConn(conn);
}

// 设备类型是否能管理楼栋
bool ResidentDevices::CIsManageBuildingType(uint32_t type)
{
    if (DEVICE_TYPE_MANAGEMENT == type || DEVICE_TYPE_DOOR == type || DEVICE_TYPE_STAIR == type || DEVICE_TYPE_ACCESS == type) 
    {
        return true;
    }

    return false;
}

void ResidentDevices::GetLocationAndNodeAndMngIDBySip(const std::string& sip, std::string& location, std::string& node, int& manager_id)
{
    ResidentDev dev;
    if (0 == dbinterface::ResidentPerDevices::GetSipDev(sip, dev) || 0 == dbinterface::ResidentDevices::GetSipDev(sip, dev))
    {
        node = dev.node;
        location = dev.location;
        manager_id = dev.project_mng_id;
    }

    return;
}

int ResidentDevices::GetLocationAndNodeBySip2(const std::string& sip, std::string& location, std::string& node,
     std::string &db_delivery_uuid, std::string &projectuuid, int &mng_id)
{
    ResidentDev per_dev;
    ResidentDev dev;
    int ret = dbinterface::ResidentPerDevices::GetSipDev(sip, per_dev);
    if (ret < 0)
    {
        ret = dbinterface::ResidentDevices::GetSipDev(sip, dev);
        if (ret == 0)
        {
            location = dev.location;
            node = dev.node;
            db_delivery_uuid = dev.project_uuid;
            projectuuid = dev.project_uuid;
            mng_id = dev.project_mng_id;
        }
    }
    else
    {
        location = per_dev.location;
        node = per_dev.node;
        ResidentPerAccount main_account;
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(per_dev.node, main_account))
        {
            //单住户不设置项目uuid
            db_delivery_uuid = main_account.uuid;
        }   
    }

    return ret == 0 ? 1 : 0;
}

int ResidentDevices::GetProjectEmergencyAlarmDevList(uint32_t mng_id, ResidentDeviceList &devlist)
{
    FirmwareList firmware_list;
    dbinterface::VersionModel::GetEmergencyControlList(firmware_list);

    std::stringstream sql;
    sql << "select UUID,Relay,SecurityRelay,Firmware from Devices where MngAccountID = " << mng_id;
        
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        Snprintf(dev.uuid, sizeof(dev.uuid), query.GetRowData(0)); 
        Snprintf(dev.relay, sizeof(dev.relay), query.GetRowData(1)); 
        Snprintf(dev.security_relay, sizeof(dev.security_relay), query.GetRowData(2)); 

        std::string firmware = query.GetRowData(3);
        auto pos = firmware.find(".");
        if (firmware_list.count(String2Int(firmware.substr(0, pos))))
        {
            devlist.push_back(dev);
        }
    }
    
    return 0;
}

int ResidentDevices::UpdateDevIPByMac(const std::string& mac,const std::string& ip)
{
    GET_DB_CONN_ERR_RETURN(conn, -1);
    std::stringstream sql;
    sql << "update Devices set IPAddress = '"
        << ip
        << "' where MAC = '"
        << mac
        << "'";
    int ret = 0;
    ret = conn->Execute(sql.str()) >= 0 ? 0 : -1;
    return ret;
}
void ResidentDevices::GetPubSupportOpenDoorDevices(const std::string& project_uuid, ResidentDeviceList& dev_list)
{
    FirmwareList firmware_list;
    VersionModel::GetEmergencyControlList(firmware_list);

    GET_DB_CONN_ERR_RETURN(conn, );

    std::stringstream stream_sql;
    stream_sql << "select " << devices_sec << " from Devices where AccountUUID = '" << project_uuid << "'"
               << " and Grade in (" << csmain::COMMUNITY_DEVICE_TYPE_PUBLIC << "," << csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT << ")";
    
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);

        if (dev.firmware > 0 && firmware_list.find(dev.firmware) != firmware_list.end())
        {
            dev_list.push_back(dev);
        }
    }
}

bool ResidentDevices::CheckProjectHighendDevOnline(const std::string& project_uuid, const std::string& highend_firm_list)
{
    if(highend_firm_list.empty())
    {
        return false;
    }
    GET_DB_CONN_ERR_RETURN(conn, false);
    std::stringstream stream_sql;
    stream_sql << "select MAC from Devices where AccountUUID = '" << project_uuid << "'"
               << " and SUBSTRING_INDEX(Firmware, '.', 1) IN (" << highend_firm_list << ")";
    
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        return true;
    }
    return false;
}

std::string ResidentDevices::GetDeviceNameByUUID(const std::string& uuid)
{
    ResidentDev dev;
    std::string dev_name;
    if (dbinterface::ResidentDevices::GetUUIDDev(uuid, dev) == 0)
    {
        dev_name = dev.location;
    }
    else if (dbinterface::ResidentPerDevices::GetUUIDDev(uuid, dev) == 0)
    {
        dev_name = dev.location;
    }
    return dev_name;
}

}
