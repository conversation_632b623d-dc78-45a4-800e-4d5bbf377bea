#!/bin/bash

CONTAINER_NAME=$1

#守护脚本启动前，先设置配置文件
/bin/bash /usr/local/akcs/csajax-ggw/scripts/sedconf.sh

if ! command -v curl &> /dev/null; then
    echo "$(date +'%Y-%m-%d %H:%M:%S') curl未安装，正在尝试安装..." >> $LOG_FILE
    apt-get update && apt-get install -y curl > /dev/null 2>&1
fi


# 启动 csajax-ggw
/usr/local/akcs/csajax-ggw/bin/csajax-ggw >/dev/null 2>&1

# 调试用 保证一直在跑即可
# while [ 1 ]
# do
#     sleep 10
# done