#ifndef __DB_COMM_PRIVATE_KEY_H__
#define __DB_COMM_PRIVATE_KEY_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "AkcsCommonDef.h"

typedef struct USER_PIN_INFO_T
{
    char pin[20];
    char account[32];
    char account_uuid[36];
    int credential_id; // 锁体对应的pin id
    SmartLockCredentialState state; // 锁返回的pin处理状态 
    bool is_create_by_pm;
    char pin_uuid[36];
    bool is_support_all_smartlock; // 凭证是否支持所有智能锁
    
    USER_PIN_INFO_T()
    {
        memset(this, 0, sizeof(*this));
    }

} UserPinInfo;

typedef std::list<UserPinInfo> UserPinInfoList;
typedef std::map<std::string/*uuid*/, std::vector<std::string>> UsersPinInfoMap;
typedef UsersPinInfoMap::iterator UsersPinInfoMapIter;

namespace dbinterface
{

class CommPerPrivateKey
{
public:
    CommPerPrivateKey();
    ~CommPerPrivateKey();
    static void GetAccountPrivatekeyList(const std::string& users, UsersPinInfoMap& pm_create_key_list, UsersPinInfoMap& user_create_key_list);
    static void GetOrderedAccountPrivatekeyList(const std::string& users, UserPinInfoList& pin_list);
    static void GetSmartLockOrderedPinInfoList(const std::string& users, UserPinInfoList& pin_list);
    static int GetPinInfoByUUID(const std::string& pin_uuid, UserPinInfo& pin_info);
private:
};

}
#endif
