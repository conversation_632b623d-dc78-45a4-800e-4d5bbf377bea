#ifndef __PARSE_REQUEST_ROBOT_MERCHANT_CALL_H__
#define __PARSE_REQUEST_ROBOT_MERCHANT_CALL_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

namespace akcs_msgparse
{
/*
<Msg>
 <Type>RequestRobotMerchantCall</Type>
  <Params>
    <TaskId></TaskId>                       // 任务ID,  devuuid_timestamp
    <TaskType></TaskType>                   // 任务类型, 0 取消召唤, 1 召唤机器人
    <DepositPinCode>1234</DepositPinCode>   // 取物码，如果传入该值机器人到达点位后需要输入正确的取物码机器人才会开门。只能是数字。特殊规则：目前长度只能是四位（可为空）
    <WithdrawPinCode>1234</WithdrawPinCode> // 放物码，如果传入该值机器人在被分配到任务后，需要输入有效的放物码才能将分配的舱门打开并且放物。只能是数字。特殊规则：目前长度只能是四位（可为空）
  </Params>
</Msg>
*/
static int ParseRequestRobotMerchantCallMsg(char *buf, SOCKET_MSG_ROBOT_TASK_CALL& robot_task_call)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseRequestRobotMerchantCallMsg \n " << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TASK_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), robot_task_call.task_id, sizeof(robot_task_call.task_id));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TASK_TYPE) == 0)
                {
                    robot_task_call.task_type = (RobotTaskType)ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_DEPOSIT_PIN_CODE) == 0)
                {
                    robot_task_call.deposit_pin_code = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_WITHDRAW_PIN_CODE) == 0)
                {
                    robot_task_call.withdraw_pin_code = ATOI(sub_node->GetText());
                }
            }
        }
    }
    return 0;
}


}

#endif 
