﻿#ifndef __CBASE_SHA1_H__
#define __CBASE_SHA1_H__

#include <string>
#include <cstdint>
#include <iostream>

class SHA1 final
{
public:
    SHA1();

    void update(const std::string &s);
    void update(std::istream &is);
    std::string final();
    std::string final_bin();

    static std::string from_file(const std::string &filename);

    static std::string encode(const std::string &s);
    static std::string encode_bin(const std::string &s);

private:
    uint32_t    digest[5];
    std::string buffer;
    uint64_t    transforms;
};

// HMAC-SHA1 class for Hash-based Message Authentication Code
class HMACSHA1 final
{
public:
    HMACSHA1();

    // HMAC-SHA1 computation with key and message
    static std::string compute(const std::string &key, const std::string &message);
    static std::string compute_bin(const std::string &key, const std::string &message);

private:
    static const size_t BLOCK_SIZE = 64; // SHA1 block size in bytes

    // Helper functions
    static std::string pad_key(const std::string &key);
    static std::string xor_with_byte(const std::string &data, uint8_t byte_val);
};

#endif
