#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "SubDisMngList.h"

namespace dbinterface {

static const std::string sub_dis_mng_list_info_sec = " InstallerUUID,DistributorUUID,UUID ";

void SubDisMngList::GetSubDisMngListFromSql(SubDisMngListInfo& sub_dis_mng_list_info, CRldbQuery& query)
{
    Snprintf(sub_dis_mng_list_info.installer_uuid, sizeof(sub_dis_mng_list_info.installer_uuid), query.GetRowData(0));
    Snprintf(sub_dis_mng_list_info.distributor_uuid, sizeof(sub_dis_mng_list_info.distributor_uuid), query.GetRowData(1));
    Snprintf(sub_dis_mng_list_info.uuid, sizeof(sub_dis_mng_list_info.uuid), query.GetRowData(2));
    return;
}

int SubDisMngList::GetSubDisMngListByInstallerUUID(const std::string& installer_uuid, SubDisMngListInfo& sub_dis_mng_list_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << sub_dis_mng_list_info_sec << " from SubDisMngList where InstallerUUID = '" << installer_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSubDisMngListFromSql(sub_dis_mng_list_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SubDisMngListInfo by InstallerUUID failed, InstallerUUID = " << installer_uuid;
        return -1;
    }
    return 0;
}


}