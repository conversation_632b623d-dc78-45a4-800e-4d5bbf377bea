#ifndef __DB_SMART_LOCK_H__
#define __DB_SMART_LOCK_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"


enum class SMARTLOCK_MODEL {
    SL20 = 1,
    SL50 = 2
};

typedef struct SmartLockInfo_T
{
    char uuid[64];
    char name[64];
    char mac[16];
    int wifi_status;
    int keep_alive; //锁是否开启保活
    char secret_key[36]; // 用于生成锁离线密码的密钥
    char last_connected_time[32];
    char device_uuid[36];
    int relay;
    int auto_lock_enable;
    int auto_lock_delay;
    char pin_code[8];
    int is_pin_code_synchronizing;
    int battery_level;
    char module_version[16];
    char lock_body_version[16];
    char combined_version[16];
    char installer_uuid[36];
    int project_type;
    char account_uuid[36];
    char community_unit_uuid[36];
    char personal_account_uuid[36];
    char mqtt_pwd[36];
    SMARTLOCK_MODEL model;
    SmartLockInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} SmartLockInfo;
    
typedef std::vector<SmartLockInfo> SmartLockInfoList;

namespace dbinterface {

class SmartLock
{
public:
    static int GetSmartLockListByDeviceUUID(const std::string& device_uuid, SmartLockInfoList& smart_lock_list);
    static int GetSmartLockInfoByUUID(const std::string& uuid, SmartLockInfo& smart_lock_info);
    static int GetSmartLockInfoListByPersonalAccountUUID(const std::string& per_uuid, SmartLockInfoList& smart_lock_list);
    static int GetSmartLockInfoListByAccountUUID(const std::string& account_uuid, SmartLockInfoList& smart_lock_list);
    static int UpdateSmartLockRelatedInfo(const SmartLockInfo& smart_lock_info,  bool pincode_already_sync);
    static int UpdateBatteryLevel(const std::string& lock_uuid, int battery_level);
    static void UpdateNodeSL20LockStatusSynchronizing(const std::string& node_uuid);
    static void UpdateProjectSL20LockStatusSynchronizing(const std::string& project_uuid);
    static int GetSmartLockInfoByMac(const std::string& mac, SmartLockInfo& smart_lock_info);
    static void GetSmartLockUUIDListByNode(const std::string& node, std::set<std::string>& smartlock_lock_uuid_list);
    static void UpdateOfflineCodeUsed(const std::string& lock_uuid, const std::string& note);
    static int UpdateSmartLockInfoByUUID(const std::string& uuid, const SmartLockInfo& smart_lock_info);
    static int UpdateSmartLockCombinedVersionByUUID(const std::string& lock_uuid, const std::string& module_version);
private:
    SmartLock() = delete;
    ~SmartLock() = delete;
    static void GetSmartLockFromSql(SmartLockInfo& smart_lock_info, CRldbQuery& query);
};

}
#endif
