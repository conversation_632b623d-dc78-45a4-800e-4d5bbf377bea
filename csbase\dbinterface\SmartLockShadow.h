#ifndef __DB_SMART_LOCK_SHADOW_H__
#define __DB_SMART_LOCK_SHADOW_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct SmartLockShadowInfo_T
{
    char uuid[36];
    char smartlock_uuid[36];
    char configuration_hash[32];
    char configuration[4096];
    SmartLockShadowInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} SmartLockShadowInfo;

namespace dbinterface {

class SmartLockShadow
{
public:
    static int GetSmartLockShadowBySmartLockUUID(const std::string& smartlock_uuid, SmartLockShadowInfo& smartlock_shadow_info);
    static int UpdateSmartLockConfigurationShadow(SmartLockShadowInfo& smartlock_shadow_info);

private:
    SmartLockShadow() = delete;
    ~SmartLockShadow() = delete;
    static void GetSmartLockShadowFromSql(SmartLockShadowInfo& smartlock_shadow_info, CRldbQuery& query);
};

}
#endif