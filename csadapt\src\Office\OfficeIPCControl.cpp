#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include "util_cstring.h"
#include "AdaptDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "OfficeIPCControl.h"
#include "UnixSocketControl.h"
#include "AKCSView.h"
#include "AdaptMQProduce.h"
#include "AK.Server.pb.h"
#include "AK.ServerOffice.pb.h"
#include "AK.Adapt.pb.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "AkcsCommonDef.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/DistributorInfo.h"
#include "dbinterface/Account.h"

#define IPC_RECONNECT_INTERVAL  1000
#define IPC_SELECT_TIMEOUT      2000

extern CSADAPT_CONF gstCSADAPTConf;
extern RouteMQProduce* g_nsq_producer;
static OfficeIPCControl* instance = nullptr;

OfficeIPCControl* GetOfficeIPCControlInstance()
{
    return OfficeIPCControl::GetInstance();
}

OfficeIPCControl::OfficeIPCControl()
{

}
OfficeIPCControl::~OfficeIPCControl()
{

}
OfficeIPCControl* OfficeIPCControl::instance = NULL;

OfficeIPCControl* OfficeIPCControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new OfficeIPCControl();
    }

    return instance;
}

int OfficeIPCControl::SendOfficeCreateUidMail(CSP2A_USER_CREATE_INFO* usercreateinfo)
{
    Json::Value root;
    Json::Value item_data;
    root["OEM"] = gstCSADAPTConf.oem_name;
    
    item_data["email_type"] = "office_create_uid";
    item_data["uid"] = usercreateinfo->szUser;
    item_data["email"] = usercreateinfo->szEmail;
    item_data["language"] = getOfficeEmailLanguage(EMAIL_OFFICE_CREATE_UID, usercreateinfo->szEmail);
    item_data["pwd"] = usercreateinfo->szPwd;
    item_data["qrcode_body"] = usercreateinfo->szQRCodeBody;
    item_data["qrcode_url"] = usercreateinfo->szQRCodeUrlPath;
    item_data["gw_code"] = std::to_string(gstCSADAPTConf.gateway_num);
    item_data["is_fake"] = usercreateinfo->is_fake;

    DistributorInfoSt dis_info;
    if (0 == dbinterface::DistributorInfo::GetDisInfoByUserAccount(usercreateinfo->szUser, project::OFFICE, dis_info))
    {
        if (strlen(dis_info.oem_name) > 0)
        {
            root["OEM"] = dis_info.oem_name;
        }
    }
 
    std::string username;
    std::string community;
    uint32_t office_id = 0;
    dbinterface::OfficePersonalAccount::GetAccountNameAndOfficeIdByUid(usercreateinfo->szUser, username, office_id);
    community = dbinterface::Account::GetOfficeNameById(office_id);
    item_data["user"] = username;
    item_data["community"] = community;
    item_data["project_type"] = project::OFFICE;

    AK_LOG_INFO << "Send office create uid email. user:" << username 
                << " uid:" << usercreateinfo->szUser 
                << " email:" << usercreateinfo->szEmail 
                << " community:" << community
                << " oem:" << root["OEM"].asString();

    sendEmailNotification(root, item_data, usercreateinfo->szEmail);
    return 0;
}

int OfficeIPCControl::SendPMOfficeRenewEmail(const CSP2A_PM_INFO* pminfo)
{
    Json::Value root;
    Json::Value item_data;
    root["OEM"] = gstCSADAPTConf.oem_name;
    
    item_data["email_type"] = "office_account_renew";
    item_data["account_num"] = pminfo->nAccountNum;
    item_data["community"] = pminfo->szCommunity;
    item_data["email"] = pminfo->szEmail;
    item_data["user"] = pminfo->szName;
    item_data["list"] = pminfo->list;
    item_data["language"] = getOfficeEmailLanguage(EMAIL_OFFICE_ACCOUNT_RENEW, pminfo->szEmail);
    item_data["project_type"] = project::OFFICE;

    if (strlen(pminfo->szDisUUID) > 0)
    {
        DistributorInfoSt dis_info;
        if (0 == dbinterface::DistributorInfo::GetDistributorInfoByDisUUID(pminfo->szDisUUID, dis_info))
        {
            root["OEM"] = dis_info.oem_name;
        }
    }

    AK_LOG_INFO << "Send office account renew email. pm_name:" << pminfo->szName 
                << " email:" << pminfo->szEmail 
                << " community:" << pminfo->szCommunity
                << " account_num:" << pminfo->nAccountNum
                << " oem:" << root["OEM"].asString();

    sendEmailNotification(root, item_data, pminfo->szEmail);
    return 0;
}

int OfficeIPCControl::SendPmOfficeAccountWillExpire(const CSP2A_PM_INFO* pminfo)
{
    AK::Server::P2PAdaptPMAccountWillExpireMsg msg;
    msg.set_community(pminfo->szCommunity);
    msg.set_email(pminfo->szEmail);
    msg.set_pm_name(pminfo->szName);
    msg.set_account_num(pminfo->nAccountNum);
    msg.set_before(pminfo->nBefore);
    msg.set_list(pminfo->list);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_PM_ACCOUNT_WILL_EXPIRE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendPmOfficeAccountExpire(const CSP2A_PM_INFO* pminfo)
{
    AK::ServerOffice::PMAppExpire msg;
    msg.set_community(pminfo->szCommunity);
    msg.set_email(pminfo->szEmail);
    msg.set_name(pminfo->szName);
    msg.set_account_num(pminfo->nAccountNum);
    msg.set_list(pminfo->list);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_PM_ACCOUNT_EXPIRE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendOfficePerResetPwdMail(CSP2A_USER_EAMIL_INFO* useremailinfo)
{
    Json::Value root;
    Json::Value item_data;
    root["OEM"] = gstCSADAPTConf.oem_name;
    
    item_data["email_type"] = "office_reset_pwd";
    item_data["email"] = useremailinfo->szEmail;
    item_data["token"] = useremailinfo->szToken;
    item_data["role_type"] = useremailinfo->szRoleType;

    DistributorInfoSt dis_info;
    if (0 == dbinterface::DistributorInfo::GetDisInfoByUserAccount(useremailinfo->szUser, project::OFFICE, dis_info))
    {
        if (strlen(dis_info.oem_name) > 0)
        {
            root["OEM"] = dis_info.oem_name;
        }
    }
    
    std::string username;
    uint32_t office_id = 0;
    dbinterface::OfficePersonalAccount::GetAccountNameAndOfficeIdByUid(useremailinfo->szUser, username, office_id);
    std::string community = dbinterface::Account::GetOfficeNameById(office_id);
    
    item_data["user"] = username;
    item_data["community"] = community;
    item_data["language"] = getOfficeEmailLanguage(EMAIL_OFFICE_RESET_PWD, useremailinfo->szEmail);
    item_data["project_type"] = project::OFFICE;

    AK_LOG_INFO << "Send office reset pwd email. user:" << useremailinfo->szUser 
                << " username:" << username 
                << " email:" << useremailinfo->szEmail 
                << " community:" << community
                << " token:" << useremailinfo->szToken
                << " role_type:" << useremailinfo->szRoleType
                << " oem:" << root["OEM"].asString();

    sendEmailNotification(root, item_data, useremailinfo->szEmail);
    return 0;
}

int OfficeIPCControl::SendOfficePerChangePwdMail(CSP2A_USER_CREATE_INFO* usercreateinfo)
{
    Json::Value root;
    Json::Value item_data;

    root["OEM"] = gstCSADAPTConf.oem_name;
        
    DistributorInfoSt dis_info;
    if (0 == dbinterface::DistributorInfo::GetDisInfoByUserAccount(usercreateinfo->szUser, project::OFFICE, dis_info))
    {
        if (strlen(dis_info.oem_name) > 0)
        {
            root["OEM"] = dis_info.oem_name;
        }
    }
    
    item_data["email_type"] = "office_change_pwd";
    item_data["uid"] = usercreateinfo->szUser;
    item_data["email"] = usercreateinfo->szEmail;
    item_data["pwd"] = usercreateinfo->szPwd;
    item_data["qrcode_body"] = usercreateinfo->szQRCodeBody;
    item_data["qrcode_url"] = usercreateinfo->szQRCodeUrlPath;
    item_data["gw_code"] = std::to_string(gstCSADAPTConf.gateway_num);
    
    // 获取用户名和办公室信息
    std::string username;
    uint32_t office_id = 0;
    dbinterface::OfficePersonalAccount::GetAccountNameAndOfficeIdByUid(usercreateinfo->szUser, username, office_id);
    std::string community = dbinterface::Account::GetOfficeNameById(office_id);
    
    item_data["user"] = username;
    item_data["community"] = community;
    item_data["language"] = getOfficeEmailLanguage(EMAIL_OFFICE_CHANGE_PWD, usercreateinfo->szEmail);
    item_data["project_type"] = project::OFFICE;

    AK_LOG_INFO << "Send office change pwd email. uid:" << usercreateinfo->szUser 
                << " username:" << username 
                << " email:" << usercreateinfo->szEmail 
                << " community:" << community
                << " qrcode_url:" << usercreateinfo->szQRCodeUrlPath
                << " oem:" << root["OEM"].asString();

    sendEmailNotification(root, item_data, usercreateinfo->szEmail);
    return 0;
}


int OfficeIPCControl::SendPmFeatureWillExpire(const CSP2A_PM_EXPIRE* expire)
{
    AK::Server::P2PAdaptPmFeatureWillExpireMsg msg;
    msg.set_user_name(expire->username);
    msg.set_email(expire->email);
    msg.set_before(expire->nbefore);
    msg.set_location(expire->community);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_PM_FEATURE_WILL_EXPIRE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendInstallerFeatureWillExpire(const CSP2A_INSTALLER_EXPIRE* expire)
{
    AK::Server::P2PAdaptInstallerFeatureWillExpireMsg msg;
    msg.set_user_name(expire->szUserName);
    msg.set_email(expire->szEmail);
    msg.set_before(expire->nBefore);
    msg.set_location(expire->community);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_INSTALLER_FEATURE_WILL_EXPIRE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendPmFeatureExpire(const CSP2A_PM_EXPIRE* expire)
{
    AK::Server::P2PAdaptPmFeatureWillExpireMsg msg;
    msg.set_user_name(expire->username);
    msg.set_email(expire->email);
    msg.set_before(expire->nbefore);
    msg.set_location(expire->community);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_PM_FEATURE_EXPIRE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendInstallerFeatureExpire(const CSP2A_INSTALLER_EXPIRE* expire)
{
    AK::Server::P2PAdaptInstallerFeatureWillExpireMsg msg;
    msg.set_user_name(expire->szUserName);
    msg.set_email(expire->szEmail);
    msg.set_before(expire->nBefore);
    msg.set_location(expire->community);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_INSTALLER_FEATURE_EXPIRE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendUserAddNewSite(const CSP2A_PER_ADD_NEWSITE& per_add_new_site)
{
    Json::Value root;
    Json::Value item_data;

    root["OEM"] = gstCSADAPTConf.oem_name;

    DistributorInfoSt dis_info;
    if (0 == dbinterface::DistributorInfo::GetDisInfoByUserAccount(per_add_new_site.main_site_account, project::OFFICE, dis_info))
    {
        if (strlen(dis_info.oem_name) > 0)
        {
            root["OEM"] = dis_info.oem_name;
        }
    }
    
    item_data["email_type"] = "office_add_new_site";
    item_data["name"] = per_add_new_site.name;
    item_data["project_name"] = per_add_new_site.project_name;
    item_data["email"] = per_add_new_site.email;
    item_data["gw_code"] = std::to_string(gstCSADAPTConf.gateway_num);
    item_data["language"] = getOfficeEmailLanguage(EMAIL_OFFICE_ADD_NEW_SITE, per_add_new_site.email);
    item_data["project_type"] = project::OFFICE;

    AK_LOG_INFO << "Send office add new site email. name:" << per_add_new_site.name 
                << " project_name:" << per_add_new_site.project_name 
                << " email:" << per_add_new_site.email 
                << " oem:" << root["OEM"].asString();

    sendEmailNotification(root, item_data, per_add_new_site.email);
	return 0;
}

std::string OfficeIPCControl::getOfficeEmailLanguage(const OfficeEmailType email_type, const std::string& email)
{
    std::string language = "en";

    if (email_type == EMAIL_OFFICE_CREATE_UID
               || email_type == EMAIL_OFFICE_RESET_PWD
               || email_type == EMAIL_OFFICE_CHANGE_PWD
               || email_type == EMAIL_OFFICE_ADD_NEW_SITE)
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if (0 == dbinterface::ResidentPersonalAccount::GetEmailAccount(email, account))
        {
            language = account.language;
        }
    }
    else if (email_type == EMAIL_OFFICE_ACCOUNT_RENEW)
    {
        dbinterface::AccountInfo account;
        memset(&account, 0, sizeof(account));
        if (0 == dbinterface::Account::GetAccountByEmail(email, account))
        {
            language = account.language;
        }
    }

    if (language.empty() || language == "zh-CN")
    {
        language = "zh";
    }

    return language;
}

void OfficeIPCControl::sendEmailNotification(const Json::Value& root_value, const Json::Value& item_data, const std::string& email)
{
    Json::Value root_copy = root_value;
    
    // 设置固定字段
    root_copy["app_type"] = "email";
    root_copy["ver"] = "1";
    
    Json::FastWriter writer;
    root_copy["data"] = writer.write(item_data);
    std::string data_json = writer.write(root_copy);

    AK::Adapt::SendEmailNotifyMsg msg;
    msg.set_key(email);
    msg.set_payload(data_json);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_SEND_EMAIL_NOTIFY);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
}


