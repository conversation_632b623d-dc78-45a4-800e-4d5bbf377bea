#ifndef __DB_INDOOR_MONITOR_ZIGBEE_DEVICE_H__
#define __DB_INDOOR_MONITOR_ZIGBEE_DEVICE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include <map>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"
#include "DclientMsgSt.h"
#include "util.h"

// Zigbee设备完整信息(用于上报设备详细信息)
typedef struct IndoorMonitorZigbeeDeviceInfo_T
{
    char uuid[36];
    char device_uuid[36];
    char zigbee_device_id[64];
    char name[64*4];
    char version[16];
    int device_type;          // 0:灯控; 1:温控; 2:窗帘
    int online_status;        // 0:离线，1:在线
    int switch_status;        // 开关状态
    int hold_delay_time;      // 灯控延迟关闭时间
    int thermostats_mode;     // 温控模式：0:仅加热,1:仅制冷,2:加热+制冷
    char temperature[8];        // 温控:当前温度
    char target_temperature[8]; // 温控:目标温度
    char min_temperature[8];    // 最低可调节温度
    char max_temperature[8];    // 最高可调节温度
    int temperature_unit;     // 温度单位：0=摄氏度(°C)，1=华氏度(°F)
    int hvac_mode;            // HVAC模式
    IndoorMonitorZigbeeDeviceInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} IndoorMonitorZigbeeDeviceInfo;

// Zigbee设备状态信息（用于状态更新）
typedef struct ZigbeeDeviceInfo_T
{
    char zigbee_device_id[64];    // Zigbee设备ID
    int device_type;              // 设备类型
    int switch_status;            // 开关状态
    char temperature[8];         // 当前温度（字符串存储）
    char target_temperature[8];  // 目标温度（字符串存储）
    int hvac_mode;                // HVAC模式
    
    ZigbeeDeviceInfo_T() 
    {
        memset(this, 0, sizeof(*this));
    }
} ZigbeeDeviceInfo;

// 类型别名定义
using ZigbeeDeviceIDAndInfoMap = std::map<std::string, IndoorMonitorZigbeeDeviceInfo>;
using ZigbeeDeviceDetailsList = std::vector<SOCKET_MSG_ZIGBEE_DEVICE_DETAILS>;

namespace dbinterface {

class IndoorMonitorZigbeeDevice
{
public:
    // 获取指定设备UUID下的ZigbeeID和Zigbee设备信息的map
    static int GetZigbeeDeviceInfoMap(const std::string& device_uuid, ZigbeeDeviceIDAndInfoMap& device_map);
    
    // 删除Zigbee设备
    static int DeleteZigbeeDevices(const std::string& device_uuid, const AkcsStringList& zigbee_device_ids);
    
    // 更新或插入Zigbee设备详细信息
    static int UpdateAndInsertZigbeeDeviceDetails(const std::string& device_uuid, const ZigbeeDeviceDetailsList& zigbee_device_details);
    
    // 更新Zigbee设备状态
    static int UpdateZigbeeDeviceStatus(const std::string& device_uuid, const ZigbeeDeviceInfo& device_info);

private:
    IndoorMonitorZigbeeDevice() = delete;
    ~IndoorMonitorZigbeeDevice() = delete;
    static void GetIndoorMonitorZigbeeDeviceFromSql(IndoorMonitorZigbeeDeviceInfo& indoor_monitor_zigbee_device_info, CRldbQuery& query);
};

}
#endif