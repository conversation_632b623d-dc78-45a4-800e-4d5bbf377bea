#include <sstream>
#include <string.h>
#include "util.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "DistributorInfo.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/Account.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "Rldb/ConnectionManager.h"
#include "ConnectionManager.h"

namespace dbinterface
{

static const std::vector<std::string> oem_name = {"Akuvox", "hager","PalwinteCs","Fasttel"};
static const std::string distibutor_info_sec = " d.Account,d.<PERSON>,d.<PERSON>illa<PERSON>oni<PERSON>,d.<PERSON>,d.IsEnableOfflineSolution,d.IsCreateSubDis,d.IsProjectLandLine,d.IsEnableRfCardControl,d.<PERSON>,d.Is<PERSON>ble<PERSON>ptChargePlan,d.IsEnableYJRobot ";
DistributorInfo::DistributorInfo()
{

}

void DistributorInfo::GetDistributorInfoFromSql(DistributorInfoSt& distributor_info, CRldbQuery& query)
{
    Snprintf(distributor_info.account, sizeof(distributor_info.account), query.GetRowData(0));
    distributor_info.enable_pin_encrypt = ATOI(query.GetRowData(1));
    distributor_info.show_villa_monitor = ATOI(query.GetRowData(2));
    distributor_info.enable_yale_lock = ATOI(query.GetRowData(3));
    distributor_info.enable_offline_solution = ATOI(query.GetRowData(4));
    distributor_info.enable_create_subdis = ATOI(query.GetRowData(5));
    distributor_info.enable_project_landline = ATOI(query.GetRowData(6));
    distributor_info.enable_rfcard_control = ATOI(query.GetRowData(7));

    int oem_index = ATOI(query.GetRowData(8));
    // 确保索引在有效范围内
    if (oem_index < 0 || oem_index >= static_cast<int>(oem_name.size())) {
        AK_LOG_INFO << "OEM index is out of bounds,oem_index:  " << oem_index <<", use default oem:akuvox";
        oem_index = 0;    
    }
    Snprintf(distributor_info.oem_name, sizeof(distributor_info.oem_name), oem_name[oem_index].c_str());   
    distributor_info.enable_apt_charge_plan = ATOI(query.GetRowData(9));
    distributor_info.enable_yunji_robot = ATOI(query.GetRowData(10));
}

int DistributorInfo::GetDistributorInfo(const std::string& account, DistributorInfoSt& distributor_info)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return 0;
    }
    CRldbQuery query(tmp_conn);
    
    std::stringstream stream_sql;
    stream_sql << "select" << distibutor_info_sec << "from DistributorInfo d where Account = '" << account << "'";
    
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetDistributorInfoFromSql(distributor_info, query);
        ReleaseDBConn(conn);
        return 0;
    }

    ReleaseDBConn(conn);
    return -1;
}

int DistributorInfo::GetDisInfoByProjectId(int project_id, DistributorInfoSt& dis_info)
{
    std::stringstream stream_sql;
    stream_sql << "SELECT" << distibutor_info_sec 
               << "FROM Account a "
               << "INNER JOIN Account dis ON a.ParentUUID = dis.UUID "
               << "INNER JOIN DistributorInfo d ON dis.Account = d.Account "
               << "WHERE a.ID = " << project_id;
    
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetDistributorInfoFromSql(dis_info, query);
        return 0;
    }

    return -1;
}

int DistributorInfo::GetDistributorInfoByDisUUID(const std::string& uuid, DistributorInfoSt& dis_info)
{
    std::stringstream stream_sql;
    stream_sql << "SELECT" << distibutor_info_sec 
               << "FROM Account a "
               << "INNER JOIN DistributorInfo d ON a.Account = d.Account "
               << "WHERE a.UUID = '" << uuid << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetDistributorInfoFromSql(dis_info, query);
        return 0;
    }

    return -1;
}

int DistributorInfo::GetDistributorInfoByPMAccountUUID(const std::string& pm_account_uuid, DistributorInfoSt& dis_info)
{

    std::stringstream stream_sql;
    stream_sql << "SELECT" << distibutor_info_sec 
               << "FROM Account pm "
               << "INNER JOIN Account ins ON pm.ParentUUID = ins.UUID "
               << "INNER JOIN Account dis ON ins.ParentUUID = dis.UUID "
               << "INNER JOIN DistributorInfo d ON dis.Account = d.Account "
               << "WHERE pm.UUID = '" << pm_account_uuid << "'";
    
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetDistributorInfoFromSql(dis_info, query);
        return 0;
    }

    return -1;
}

int DistributorInfo::GetDistributorInfoByPersonalAccount(const ResidentPerAccount& personal_account, DistributorInfoSt& dis_info)
{
    AccountInfo dis_account;
    memset(&dis_account, 0, sizeof(dis_account));
    
    if (0 != Account::GetDisAccount(personal_account, dis_account))
    {
        return -1;
    }
    
    if (0 != GetDistributorInfo(dis_account.account, dis_info))
    {
        return -1;
    }
    return 0;
}

int DistributorInfo::GetDistributorInfoByOfficePersonalAccount(const OfficeAccount& office_account, DistributorInfoSt& dis_info)
{
    AccountInfo dis_account;
    memset(&dis_account, 0, sizeof(dis_account));
    
    if (0 != Account::GetOfficeDisAccount(office_account, dis_account))
    {
        return -1;
    }
    
    if (0 != GetDistributorInfo(dis_account.account, dis_info))
    {
        return -1;
    }
    return 0;
}

int DistributorInfo::GetDisInfoByUserAccount(const std::string& user, int project_type, DistributorInfoSt& dis_info)
{
    if (project_type == project::OFFICE)
    {
        OfficeAccount personal_account;
        memset(&personal_account, 0, sizeof(personal_account));
        
        if (0 == dbinterface::OfficePersonalAccount::GetUserAccount(user, personal_account))
        {
            return GetDistributorInfoByOfficePersonalAccount(personal_account, dis_info);
        }
    }
    else
    {
        ResidentPerAccount personal_account;
        memset(&personal_account, 0, sizeof(personal_account));
        
        if (0 == dbinterface::ResidentPersonalAccount::GetUserAccountFromMaster(user, personal_account))
        {
            return GetDistributorInfoByPersonalAccount(personal_account, dis_info);
        }
    }
    
    return -1;
}
// 社区主账号获取distributorinfo
int DistributorInfo::GetDistributorInfoByNode(const std::string& node, DistributorInfoSt& distributor_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << distibutor_info_sec << " from DistributorInfo d "
               << "left join Account A on A.Account = D.Account "
               << "left join Account AA ON AA.ParentUUID = A.UUID "
               << "left join PersonalAccount P ON P.ParentUUID = AA.UUID "
               << "where P.Account = '" << node << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetDistributorInfoFromSql(distributor_info, query);
        return 0;
    }
    return -1;
}

// 社区主账号UUID获取distributorinfo
int DistributorInfo::GetDistributorInfoByNodeUUID(const std::string& node_uuid, DistributorInfoSt& distributor_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << distibutor_info_sec << " from DistributorInfo d "
               << "left join Account A on A.Account = D.Account "
               << "left join Account AA ON AA.ParentUUID = A.UUID "
               << "left join PersonalAccount P ON P.ParentUUID = AA.UUID "
               << "where P.UUID = '" << node_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetDistributorInfoFromSql(distributor_info, query);
        return 0;
    }
    return -1;
}

}