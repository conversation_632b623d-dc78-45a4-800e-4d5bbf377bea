#ifndef __ADAPT_DEFINE_H__
#define __ADAPT_DEFINE_H__


#define ADAPTPORT           8503
#define CONFFILEPATH        "/usr/local/akcs/csadapt/conf/csadapt.conf"

//SOCKET相关
#define SOCKET_MULTICAST_PORT       8500
#define SOCKET_TCP_LISTEN_PORT      8501

#define CSADAPT_CONF_COMMON_LEN 64

typedef struct CSADAPT_CONF_T
{
    /* 本机IP配置信息 */
    char szServerOutIP[CSADAPT_CONF_COMMON_LEN];
    char szServerInnerIP[CSADAPT_CONF_COMMON_LEN];
    char szServerHostname[CSADAPT_CONF_COMMON_LEN];

    /* csmain本机配置信息 */
    char szCsadaptOuterIP[CSADAPT_CONF_COMMON_LEN];
    char web_domain[CSADAPT_CONF_COMMON_LEN];
    int nLogLevel; //日志打印级别 LOG_LEVEL_E
    char szLogFile[CSADAPT_CONF_COMMON_LEN];

    /* cspbx本机配置信息 */
    char szCspbxOuterIP[CSADAPT_CONF_COMMON_LEN];
    char szCspbxOuterPort[CSADAPT_CONF_COMMON_LEN];

    /* DB配置项 */
    char szDbIP[CSADAPT_CONF_COMMON_LEN];
    char szDbUserName[CSADAPT_CONF_COMMON_LEN];
    char szDbPassword[CSADAPT_CONF_COMMON_LEN];
    char szDbDatabase[CSADAPT_CONF_COMMON_LEN];
    char szDbSocketFile[CSADAPT_CONF_COMMON_LEN]; //如果这个文件不为空 代表用unix连接
    int  nDbPort;
    /*OEM 配置*/
    char szOEMConfig[1024];
    char oem_name[CSADAPT_CONF_COMMON_LEN];

    /*远程服务器ssh代理地址*/
    char szSshProxyDomain[128];


    int nNoEncrypt;
    char szNSQTopicForDelPic[32];
    char szNSQRouteTopic[CSADAPT_CONF_COMMON_LEN];//跟route的通信topic
    char szEtcdServerAddr[CSADAPT_CONF_COMMON_LEN];
    char szBeanStalkAddr[CSADAPT_CONF_COMMON_LEN];
    char beanstalk_backup_ip[CSADAPT_CONF_COMMON_LEN];
    char web_ip[CSADAPT_CONF_COMMON_LEN];

    //Area list:1)ccloud 2)scloud 3)ecloud 4)ucloud 5)other 6)rcloud    
    int server_type;
    char community_ids[128];
    //是否亚马逊云
    int is_aws; 
    char aws_db_ip[CSADAPT_CONF_COMMON_LEN];
    int aws_redirect;

    //kafka配置
    char kafka_broker_ip[CSADAPT_CONF_COMMON_LEN];
    char notify_app_backend_topic[CSADAPT_CONF_COMMON_LEN];
    char notify_app_backend_group[CSADAPT_CONF_COMMON_LEN];
    int notify_app_backend_thread_num;
    char special_mng_id[256];

    int write_thread_number;//默认两个转发线程 其他为写配置线程

    char log_db_database[CSADAPT_CONF_COMMON_LEN];
    char log_db_ip[CSADAPT_CONF_COMMON_LEN];
    int log_db_port;

    char server_tag[16];

    char appbackend_analysis_topic[CSADAPT_CONF_COMMON_LEN];
    char appbackend_analysis_group[CSADAPT_CONF_COMMON_LEN];
    int appbackend_analysis_thread_num;
        
    char appbackend_push_topic[CSADAPT_CONF_COMMON_LEN];
    char appbackend_push_group[CSADAPT_CONF_COMMON_LEN];
    int appbackend_push_thread_num;
        
    char appbackend_notify_topic[CSADAPT_CONF_COMMON_LEN];
    char appbackend_notify_group[CSADAPT_CONF_COMMON_LEN];
    int appbackend_notify_thread_num;

    char notify_web_message_topic[CSADAPT_CONF_COMMON_LEN];
    char notify_web_message_group[CSADAPT_CONF_COMMON_LEN];
    int notify_web_message_thread_num;

    char notify_csconfig_topic[CSADAPT_CONF_COMMON_LEN];

    int gateway_num;
} CSADAPT_CONF;


#endif// __ADAPT_DEFINE_H__

