#ifndef __RESIDENT_DEVICES_H__
#define __RESIDENT_DEVICES_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include <set>
#include "AkcsCommonDef.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "AkcsCommonSt.h"
#include "dbinterface/Shadow.h"

typedef struct ResidentDev_T
{
    uint32_t id;
    uint32_t project_mng_id;
    TCHAR node[32]; // role=12,单住户终端公共设备的虚拟账号,account长度为20
    TCHAR ipaddr[64];
    TCHAR wired_ipaddr[64];
    TCHAR outer_ip[64];
    TCHAR mac[16];
    //TCHAR firmware[16];
    TCHAR config_md5[36];
    TCHAR face_md5[36];
    short sip_type;// siptype
    TCHAR sip[16];
    TCHAR sippwd[64]; //sip密码
    TCHAR location[DEV_LOCATION_SIZE];
    TCHAR rtsppwd[64]; //个人终端用户使用
    int dev_type; //设备类型 室内机/门口机
    uint32_t unit_id;// 单元id
    short grade;//设备的归属等级，是社区共享，单元共享，还是用户独占
    TCHAR contact_md5[36];
    TCHAR pushbutton[512];
    short netgroup_num;//网络编号
    short stair_show; //梯口机列表显示配置  0默认 1roomnum 2app/indoor
    TCHAR relay[4096];//dtmf 信息。#,name,1,1;*,xx,是否显示在home,是否显示在talking;
    TCHAR security_relay[4096];//dtmf 信息。#,name,1,1;*,xx,是否显示在home,是否显示在talking;
    TCHAR autop_config[2048];//新增的配置
    uint32_t dclient_ver;//dclient版本，用于兼容旧版本
    TCHAR user_mate_md5[36];
    TCHAR user_info_md5[36];
    TCHAR schedule_md5[36];
    //其他
    TCHAR call_seq[256];//呼叫顺序
    TCHAR acc_srv_id[24];//呼叫顺序
    TCHAR uuid[64];
    TCHAR community[64];
    TCHAR gateway[40];
    TCHAR subnet_mask[40];
    TCHAR wired_subnet_mask[40];
    TCHAR primary_dns[40];
    TCHAR secondary_dns[40];
    TCHAR sw_ver[32];
    TCHAR hw_ver[32];
    int firmware;
    int oem_id;
    int status;
    TCHAR last_connection[24];
    TCHAR private_key_md5[36];
    TCHAR rf_id_md5[36];
    TCHAR auth_code[32];
    int project_type; //0住宅，1办公 2单住户
    int brand;        //品牌 0-akuvox 1-other
    int port;
    int arming;
    int extension;
    bool is_ipv6;
    bool is_dy_iv;
    int flags; // '按位标识 1=home;2=away;3=sleep;4=管理机是否开启全选,默认开启;5-8位代表设备relay的开关情况 0关1开;9=室内机上线标识;10=室内机所属的家庭是否为Kit方案',
    int flag;  //'个人类型设备的标示符，位计算。第1位标示设备是否为个人终端管理员的，第二位之后暂时保留',
    int repost; //转流标识
    // node_conf
    short enable_motion;
    short motion_time;
    short enable_robin_call;
    short robin_call_time;
    char robin_call_val[256];
    short is_public;
    short is_personal;//是不是单住户 办公和社区=0
    uint32_t allow_end_user_hold_door;//设备是否允许终端用户设置门常开

    uint64_t fun_bit; // 按位标识功能 0=夏令时功能;1=监控是否支持SRTP;2=https下载是否支持tls1.2;3=是否支持authcode为空上云;4=家居设备;5=新办公设备;6=是否支持远程WEB访问的服务地址下发
    //表之外的数据
    csmain::DeviceType conn_type;
    bool init;//是否初始化过(管理员uuid/项目uuid等) 
    char project_uuid[64]; //社区Account表uuid
    int allow_end_user_monitor;
    char unit_uuid[64];    //楼栋uuit
    char node_uuid[64];
    char create_time[32]; //创建时间
    uint64_t conn_version; //链接更新的版本
    DatabaseExistenceStatus is_attendance;   //是否考勤设备
    int enable_package_detection;
    // int enable_sound_detection;
    // int sound_type;

    ResidentDev_T() {
        memset(this, 0, sizeof(*this));
    }
} ResidentDev;

typedef std::vector<ResidentDev> ResidentDeviceList;
typedef ResidentDeviceList::iterator ResidentDeviceListIter;

namespace dbinterface
{

class ResidentDevices
{
public:
    ResidentDevices();
    ~ResidentDevices();

    static std::string GetLocationBySip(const std::string& sip);
    static int GetLocationAndNodeBySip(const std::string& sip, std::string& location, std::string& node);
    static int GetDevTypeBySip(const std::string& sip);
    /*家庭*/
    static int GetNodeDevList(const std::string& node, ResidentDeviceList &devlist);
    static int GetSipDev(const std::string& sip, ResidentDev &dev);
    static int UpdateDevMD5(ResidentDeviceList &dev_list, DEVICES_MD5_TYPE type);
    static int UpdateDevMD5(ResidentDev &dev, DEVICES_MD5_TYPE type);
    static int UpdateMd5ByID(uint32_t id, SHADOW_TYPE shadow_type, const std::string& value);
    static int GetMacDev(const std::string& mac, ResidentDev &dev);
    static int GetMacDev(const std::string& mac, ResidentDeviceList &dev_list);
    static int GetUUIDDev(const std::string& uuid, ResidentDev &dev);
    static int GetDevUUIDByMac(const std::string& mac, std::string& uuid);
    static std::string GetDeviceNameByUUID(const std::string& uuid);
    /*获取所有管理机*/
    static int GetAllMngDevList(uint32_t mng_id, ResidentDeviceList &devlist);
    /*单元公共设备*/
    static int GetDepartmentDevList(uint32_t department_id, ResidentDeviceList &devlist);
    /*最外围公共设备*/
    static int GetPubDevList(uint32_t mng_id, ResidentDeviceList &devlist);
    /*所有单元+最外围公共设备*/
    static int GetAllPubDevList(uint32_t mng_id, ResidentDeviceList &devlist);
    /*所有小区的设备*/
    static int GetAllDevList(uint32_t mng_id, ResidentDeviceList &devlist);
    //小区内所有的个人设备
    static int GetCommunityAllNodeDevList(uint32_t mng_id, ResidentDeviceList &dev_list);
    // 社区所有最外围 + 指定楼栋设备
    static int GetRootPubAndUnitPubDeviceSettingList(uint unit_id, uint mng_id, ResidentDeviceList& dev_list);
    // 获取所有支持一键开门的公共设备
    static void GetPubSupportOpenDoorDevices(const std::string& project_uuid, ResidentDeviceList& dev_list);
    
    static int GetMacListDevList(const std::set<std::string> &mac_set, ResidentDeviceList &devlist);
    static int GetDevicesBySip(const std::string& sip, ResidentDev &dev);
    static int GetMacByUUID(const std::string& uuid, std::string& mac);
    static int GetNodeIndoorDevList(const std::string& node, ResidentDeviceList &devlist);
    static int GetDevByID(int id, ResidentDev &dev);
    static int GetCommunityMngDevByMngID(int mng_id, std::vector<DEVICE_SETTING>& devs);
    static int UpdateDeviceInfo(DEVICE_SETTING* device_setting);
    static int SetDeviceDisConnTime(const std::string& mac, const std::string& logic_srv_ip);
    static int SetDeviceDisConnectStatus(const std::string& mac, const std::string& logic_srv_ip, uint64_t conn_version);
    static int GetAllDeviceSettingCount();
    static int IsMacDeviceExist(const char* mac);
    static int GetRootCommunityDeviceListByAccount(const std::string account, int mng_id,  std::vector<std::string>& mac_list);
    static int SetAllDevDisconnect(const std::string& csmain_sid);
    static int SetDevAuthCode(DEVICE_SETTING* pDevice);
    static int DevRegistInCloud(const std::string& mac);
    static int SetDeviceArmingStatus(const std::string& mac, int indoor_arming);
    static int SetDeviceSensorTirggerInfo(const std::string& mac, int home, int away, int sleep);
    static int SetDeviceRelayStatus(const std::string& mac, int relay_status);
    static bool CheckIndoorPlan(const std::string& account);
    static int GetRepostDev(const std::string& uid, std::string& mac);
    static int GetMacByFirmware(const std::string& firmware, std::vector<std::string>& macs);
    static int UpdateDoorRelayStatus(const std::string &mac, const std::string &door_relay_status, const std::string& door_se_relay_status);
    static void GetNodesDevList(const std::vector<std::string> &node_list, std::set<std::string> &mac_set);
    static void GetNodesIndoorOrMngDevList(const std::string& node, std::set<std::string> &mac_set);
    static void GetPubMacsByMngIDAndUnitID(int community_id, int unit_id, std::set<std::string> &mac_set);
    static void GetAllContactListDevices(const std::string &node, unsigned int unit_id, unsigned int manager_account_id, std::set<std::string> &mac_set);
    //获取项目下所有家庭室内机
    static int GetAllAptIndoorDevices(int project_id, std::set<std::string> &dev_list);
    static void GetLocationAndNodeAndMngIDBySip(const std::string& sip, std::string& location, std::string& node, int& manager_id);
    static int GetLocationAndNodeBySip2(const std::string& sip, std::string& location, std::string& node, 
         std::string &db_delivery_uuid, std::string &projectuuid, int &mng_id);
    static int GetProjectEmergencyAlarmDevList(uint32_t project_id, ResidentDeviceList& dev_list);
    static bool CheckProjectHighendDevOnline(const std::string& project_uuid, const std::string& highend_firm_list);

public:
    static bool CIsManageBuildingType(uint32_t type);
    static int UpdateDevIPByMac(const std::string& mac,const std::string& ip);
private:
    static void GetDevicesFromSql(ResidentDev& dev, CRldbQuery& query);
    static int InitDevicesBySip(const std::string& sip, ResidentDev &dev);
};


}
#endif
