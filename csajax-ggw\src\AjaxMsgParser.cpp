#include "AjaxMsgParser.h"
#include "AkLogging.h"

AjaxMsgParser::AjaxMsgParser(const std::string& msg) : status_(FAIL)
{
    Parse(msg);
}

void AjaxMsgParser::Parse(const std::string& msg)
{
    Json::Reader reader;
    Json::Value root;
    Json::Value data;

    AK_LOG_INFO << "AjaxMsgParser msg=" << msg;

    if (!reader.parse(msg, root))
    {
        AK_LOG_WARN << "AjaxMsgParser parse error. msg=" << msg << " error msg=" << reader.getFormatedErrorMessages();
        return;
    }

    ParseSqsMessageInfoKv(root);

    if (!msg_kv_list_.empty())
    {
        status_ = OK;
    }
}

//解析body
void AjaxMsgParser::ParseSqsMessageInfoKv(const Json::Value& body)
{
    if (!body.isObject() || !body.isMember("event"))
    {
        AK_LOG_WARN << "ajax msg body wrong.";
        return;
    }

    Json::Value event = body["event"];

    // hub id
    if (!event.isObject() || !event.isMember("hubId"))
    {
        AK_LOG_WARN << "ajax msg event wrong.";
        return;
    }
    
    msg_kv_list_.emplace("hub_id", event["hubId"].asString());

    // Json::Value final_body = GetRemovedAddtionalDataJson(body);
    Json::FastWriter writer;
    // body
    msg_kv_list_.emplace("body", writer.write(body));
}
 

std::string AjaxMsgParser::GetHubID()
{
    auto it = msg_kv_list_.find("hub_id");
    if (it != msg_kv_list_.end()) 
    {
        return it->second;
    } 

    return "";
}