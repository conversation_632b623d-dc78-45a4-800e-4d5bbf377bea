#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <list>
#include <boost/crc.hpp>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "AdaptDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "SysEnv.h"
#include "AkcsWebMsgSt.h"
#include "AKCSView.h"
#include "UnixSocketControl.h"
#include "beanstalk.hpp"
#include "AK.Adapt.pb.h"
#include "AK.Crontab.pb.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "json/json.h"
#include "OfficeUnixSocketControl.h"
#include "AkcsCommonDef.h"
#include "AK.AdaptOffice.pb.h"
#include "dbinterface/AwsRedirect.h"
#include "SafeCacheConn.h"
#include "zipkin/AkcsZipkin.h"
#include "zipkin/ZipkinConf.h"
#include "Azer/AzerUnixSocketControl.h"
#include "KafkaConsumerPushTopicHandle.h"
#include "KafkaConsumerNotifyTopicHandle.h"
#include "KafkaConsumerPushTopicHandle.h"
#include "kafka/AkcsKafkaProducerNotifyConfig.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "MetricService.h"



extern const char* g_redis_db_proc_record;
extern const char* g_proc_record_bigprojec;
extern CSADAPT_CONF gstCSADAPTConf;
extern AKCS_ZIPKIN_CONF g_zipkin_conf;
Beanstalk::Client* g_bs_client_ptr = nullptr;
Beanstalk::Client* g_bs_backup_client_ptr = nullptr;
std::mutex g_bs_mutex;
std::mutex g_bs_backup_mutex;

const std::string AKCS_ATTACK_IP_TUBE = "akcs_attack_ips"; //beanstalkd的tube,专用于akcs业务判断攻击者的来源ip用

#define UNIX_DOMAIN "/var/adapt_sock/adapt.sock"
#define BEANSTALK_SERVER_PORT  (8519)


CUnixSocketControl* CUnixSocketControl::instance = NULL;
CUnixSocketControl::CUnixSocketControl()
{
    polling_cnt_ = 0;
    //下面参数会在调用init时候进行修改
    thread_num_ = 4;    //消费线程数， 每个线程对应其队列web_to_adapt%d     
    thread_wirte_file_num_ = 2;//写配置；线程0 1
    thread_common_num_ = 2;//转发的；线程2 3
}

CUnixSocketControl::~CUnixSocketControl()
{
}

CUnixSocketControl* GetUnixSocketControlInstance()
{
    return CUnixSocketControl::GetInstance();
}
CUnixSocketControl* CUnixSocketControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CUnixSocketControl();
    }

    return instance;
}

void CUnixSocketControl::Init(const std::string& beanstalk_ip)
{    
    if(0 == beanstalk_ip.size())
    {
        return;
    }
    write_file_queue_consistent_hash_.InitQueueNumList(gstCSADAPTConf.write_thread_number);
    //启动消费者线程
    thread_num_ = thread_common_num_ + gstCSADAPTConf.write_thread_number;
    thread_wirte_file_num_ = gstCSADAPTConf.write_thread_number;
    for (int i = thread_wirte_file_num_; i < thread_num_; ++i)  //配置相关的消息由csconfig去消费
    {    
        std::thread thread(ProcessMsgForBeanstalk, i, beanstalk_ip);
        thread.detach();
    }
    return;
}

bool CUnixSocketControl::IsBigProject(int project_id)
{
    if (project_id <= 0)
    {
        return false;
    }
    
    std::string key = g_proc_record_bigprojec + std::to_string(project_id);
    
    SafeCacheConn redis(g_redis_db_proc_record);
    return redis.isExists(key);
}

VOID* CUnixSocketControl::IPCServerThread()
{
    int listen_fd;
    int read_fd;
    int ret;
    int len;
    struct sockaddr_un srv_addr;
    struct sockaddr_un cli_addr;
    char read_buf[CS_COMMON_MSG_MAX_SIZE];

    listen_fd = socket(AF_UNIX, SOCK_STREAM, 0);
    if (listen_fd < 0)
    {
        AK_LOG_WARN << "The IPCServerThread CreateSocket failed";
        return NULL;
    }
    srv_addr.sun_family = AF_UNIX;
    Snprintf(srv_addr.sun_path, sizeof(srv_addr.sun_path), UNIX_DOMAIN);
    unlink(UNIX_DOMAIN);

    ret = bind(listen_fd, (struct sockaddr*)&srv_addr, sizeof(srv_addr));
    if (ret < 0)
    {
        AK_LOG_WARN << "cannot bind server socket";
        close(listen_fd);
        unlink(UNIX_DOMAIN);
        return NULL;
    }

    ret = listen(listen_fd, 64);
    if (ret < 0)
    {
        AK_LOG_WARN << "listen failed";
        close(listen_fd);
        unlink(UNIX_DOMAIN);
        return NULL;
    }

    /*change the authority of file*/
    chmod(srv_addr.sun_path, 00777);

    while (1)
    {
        len = sizeof(cli_addr);
        read_fd = accept(listen_fd, (struct sockaddr*)&cli_addr, (socklen_t*)&len);
        if (read_fd < 0)
        {
            AK_LOG_WARN << "accept failed";
            continue;
        }

        memset(read_buf, 0, CS_COMMON_MSG_MAX_SIZE);
        len = read(read_fd, read_buf, sizeof(read_buf));
        close(read_fd);

        if (len < CS_COMMON_MSG_HEADER_SIZE)
        {
            AK_LOG_WARN << "ipc read return error: " << len << " " << strlen(read_buf);
            continue;
        }

        GetUnixSocketControlInstance()->AddMsg(read_buf, len);
    }
    close(listen_fd);
    return NULL;
}

int CUnixSocketControl::GetWriteFileTube(const std::string &str, int write_num)
{   
    return write_file_queue_consistent_hash_.GetQueueNumByKey(str);
}

int CUnixSocketControl::GetCommTube()
{
    std::lock_guard<std::mutex> guard(mutex_cnt_);
    polling_cnt_++;
    if(polling_cnt_ >= thread_common_num_)
    {
        polling_cnt_ = 0;
    }
    return polling_cnt_ + thread_wirte_file_num_;
}


int CUnixSocketControl::CheckToAwsByAccountID(int account_id)
{
    AwsInfo account_info;
    memset(&account_info, 0, sizeof(AwsInfo));
    account_info.account_id = account_id;
    int to_aws = dbinterface::AwsRedirect::CheckUserRedirect(account_info, gstCSADAPTConf.aws_redirect);
    return to_aws;
}
int CUnixSocketControl::CheckToAwsByUid(const std::string &uid, int is_dev)
{
    AwsInfo account_info;
    memset(&account_info, 0, sizeof(AwsInfo));
    snprintf(account_info.uid, sizeof(account_info.uid), "%s", uid.c_str());
    account_info.is_dev = is_dev;
    int to_aws = dbinterface::AwsRedirect::CheckUserRedirect(account_info, gstCSADAPTConf.aws_redirect);
    return to_aws;
}


void CUnixSocketControl::AddMsg(char* msg, int len)
{
    if ((NULL == msg) || (len < CS_COMMON_MSG_HEADER_SIZE))
    {
        return;
    }
    
    if(gstCSADAPTConf.is_aws)   //亚马逊迁移迁刷配置专用tube
    {
        int tube_id = 100;
        int to_aws = 1;
        AddMsgToBeanstalk(msg, len, tube_id, to_aws, BEANSTALK_NORMAL_DELAY);   //分配到不同队列，由不同的线程消费
        return;
    }
    
    uint32_t from_id = NTOHL(*((unsigned int*)((unsigned char*)msg + 8)));
    if (from_id == project::OFFICE_NEW) 
    {
        std::string json_data = std::string((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE);
        AKCS::Singleton<HandleKafkaNotifyTopicMsg>::instance().HandleTcpMessage(json_data);
        AKCS::Singleton<HandleKafkaPushTopicMsg>::instance().HandleTcpMessage(json_data); //处理slim过来的发邮件消息
        return;
    }

    uint32_t id = NTOHL(*((unsigned int*)((unsigned char*)msg + 4)));
    int tube_id = 0;
    int to_aws = 0;
    std::string kafka_key;
    switch (id)
    {
        case MSG_P2A_NOTIFY_PERSONAL_MESSAGE:   //根据Node Hash分配到不同队列（线程），避免同文件并发写
        {
            AK::Adapt::WebPersonalModifyNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            tube_id = GetWriteFileTube(notify.node(), thread_wirte_file_num_);
            to_aws = CheckToAwsByUid(notify.node());
            ResidentPerAccount per_account;
            dbinterface::ResidentPersonalAccount::InitAccountByUid(notify.node(), per_account);
            kafka_key = per_account.parent_id;
            
            // 添加监控计数 - AK.Adapt.proto
            MetricService* metric_service = MetricService::GetInstance();
            if(metric_service) 
            {
                metric_service->AddValue("csadapt_adapt_proto_message_received_total", 1);
            } 
            break;
        }
        case MSG_P2A_NOTIFY_COMMUNITY_MESSAGE:  //根据MngID Hash分配到不同队列（线程），避免同文件并发写
        {        
            AK::Adapt::WebCommunityModifyNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mng  = std::to_string(notify.community_id());
            tube_id = GetWriteFileTube(mng, thread_wirte_file_num_);
            to_aws = CheckToAwsByAccountID(notify.community_id());
            kafka_key = std::to_string(notify.community_id());
             
            // 添加监控计数 - AK.Adapt.proto
            MetricService* metric_service = MetricService::GetInstance();
            if(metric_service) 
            {
                metric_service->AddValue("csadapt_adapt_proto_message_received_total", 1);
            } 
            break;
        }
        case MSG_P2A_NOTIFY_ACCESS_GROUP_MODIFY:
        {
            AK::Adapt::AccessGroupModifyNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mng  = std::to_string(notify.community_id());
            tube_id = GetWriteFileTube(mng, thread_wirte_file_num_);
            to_aws = CheckToAwsByAccountID(notify.community_id());
            kafka_key = std::to_string(notify.community_id());
            break;
        }
        case MSG_P2A_NOTIFY_COMMUNITY_PERSONAL_MODIFY:
        {
            AK::Adapt::CommunityPersonalModifyNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mng  = std::to_string(notify.community_id());
            tube_id = GetWriteFileTube(mng, thread_wirte_file_num_);
            to_aws = CheckToAwsByAccountID(notify.community_id());
            kafka_key = std::to_string(notify.community_id());
            break;
        }
        case MSG_P2A_NOTIFY_COMMUNITY_ACCOUNT_MODIFY:
        {
            AK::Adapt::CommunityAccountModifyNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mng  = std::to_string(notify.community_id());
            tube_id = GetWriteFileTube(mng, thread_wirte_file_num_);
            to_aws = CheckToAwsByAccountID(notify.community_id());
            kafka_key = std::to_string(notify.community_id());
            break;
        } 
        case MSG_P2A_NOTIFY_COMMUNITY_IMPORT_ACCOUNT_DATAS:
        {
            AK::Adapt::CommunityImportAccountDataNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mng  = std::to_string(notify.community_id());
            tube_id = GetWriteFileTube(mng, thread_wirte_file_num_);
            to_aws = CheckToAwsByAccountID(notify.community_id());
            kafka_key = std::to_string(notify.community_id());
            break;
        } 
        case MSG_P2A_REGULAR_AUTOP:
        {
            AK::Adapt::RegularAutopNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mng  = std::to_string(notify.project_id());
            AK_LOG_INFO << "receive mng id=" << mng;
            tube_id = GetWriteFileTube(mng, thread_wirte_file_num_);
            to_aws = CheckToAwsByAccountID(notify.project_id());
            kafka_key = std::to_string(notify.project_id());
            break;
        } 
        case MSG_P2A_NOTIFY_DATA_ANALYSIS_NOTIFY:
        {
            AK::Adapt::DataAnalysisNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));         
            std::string mng  = std::to_string(notify.project_id());
            tube_id = GetWriteFileTube(mng, thread_wirte_file_num_);
            to_aws = CheckToAwsByAccountID(notify.project_id());            
            kafka_key = std::to_string(notify.project_id());
            break;
        }        
        //OFFICE
        case MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE:
        {
            AK::AdaptOffice::WebOfficeModifyNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mng  = std::to_string(notify.office_id());
            tube_id = GetWriteFileTube(mng, thread_wirte_file_num_);
            to_aws = CheckToAwsByAccountID(notify.office_id());
            kafka_key = std::to_string(notify.office_id());
            
            // 添加监控计数 - AK.Adapt.Office.proto
            MetricService* metric_service = MetricService::GetInstance();
            if(metric_service) 
            {
                metric_service->AddValue("csadapt_adapt_office_proto_message_received_total", 1);
            } 
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_ACCESS_GROUP_MODIFY:
        {
            AK::Adapt::AccessGroupModifyNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mng  = std::to_string(notify.community_id());
            tube_id = GetWriteFileTube(mng, thread_wirte_file_num_);
            to_aws = CheckToAwsByAccountID(notify.community_id());
            kafka_key = std::to_string(notify.community_id());
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_COMMUNITY_ACCOUNT_MODIFY:
        {
            AK::Adapt::CommunityAccountModifyNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mng  = std::to_string(notify.community_id());
            tube_id = GetWriteFileTube(mng, thread_wirte_file_num_);
            to_aws = CheckToAwsByAccountID(notify.community_id());
            kafka_key = std::to_string(notify.community_id());
            break;
        }
        case MSG_P2A_NOTIFY_OFFICE_COMMUNITY_IMPORT_ACCOUNT_DATAS:
        {
            AK::Adapt::CommunityImportAccountDataNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mng  = std::to_string(notify.community_id());
            tube_id = GetWriteFileTube(mng, thread_wirte_file_num_);
            to_aws = CheckToAwsByAccountID(notify.community_id());
            kafka_key = std::to_string(notify.community_id());
            break;
        }    
        case MSG_P2A_NOTIFY_OFFICE_PERSONAL_MODIFY:
        {
            AK::Adapt::CommunityPersonalModifyNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mng  = std::to_string(notify.community_id());
            tube_id = GetWriteFileTube(mng, thread_wirte_file_num_);
            to_aws = CheckToAwsByAccountID(notify.community_id());
            kafka_key = std::to_string(notify.community_id());
            break;
        }

        case MSG_P2A_ACCOUNT_ACCESS_UPDATE_NOTIFY:
        {
            AK::Crontab::CronUserAccessGroupNotifyMsg notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mng  = std::to_string(notify.community_id());
            tube_id = GetWriteFileTube(mng, thread_wirte_file_num_);
            to_aws = CheckToAwsByAccountID(notify.community_id());
            kafka_key = std::to_string(notify.community_id());
            break;
        }

        //亚马逊特有
        case MSG_P2A_REBOOT_TO_DEVICE:  //新增 用于亚马逊迁移调度处理
        {
            tube_id = GetCommTube();
            
            AK::Adapt::DevRebootNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mac  = notify.mac();
            
            to_aws = CheckToAwsByUid(mac, 1);
            break;
        } 
        case MSG_P2A_PERSONNAL_ALARM_DEAL:  //新增 用于亚马逊迁移调度处理
        case MSG_P2A_COMMUNITY_ALARM_DEAL:
        {
            tube_id = GetCommTube();

            AK::Adapt::PersonalAlarmDeal notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));

            to_aws = CheckToAwsByUid(notify.area_node());
            break;
        }         
        case MSG_P2A_PERSONNAL_NEW_TEXT_MESSAGE:    //新增 用于亚马逊迁移调度处理
        case MSG_P2A_COMMUNITY_NEW_TEXT_MESSAGE:
        {
            tube_id = GetCommTube();
            
            AK::Adapt::PerNewMessage notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));

            to_aws = CheckToAwsByAccountID(ATOI(notify.data().c_str()));
            break;
        }
        case MSG_P2A_NOTIFY_CREATE_REMOTE_DEV_CONTORL:  //新增 用于亚马逊迁移调度处理
        {
            tube_id = GetCommTube();
            
            AK::Adapt::CreateRemoteDevContorl notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mac  = notify.mac();

            to_aws = CheckToAwsByUid(mac, 1);
            break;
        }
        case MSG_P2A_UPDATE_MAC_CONFIG: //新增 用于亚马逊迁移调度处理
        {
            tube_id = GetCommTube();
            
            AK::Adapt::DevConfigUpdateNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mac  = notify.mac();
            to_aws = CheckToAwsByUid(mac, 1);
            break;
        }
        case MSG_P2A_NOTIFY_REMOTE_OPENDOOR:    //新增 用于亚马逊迁移调度处理
        {
            tube_id = GetCommTube();
            
            AK::Adapt::OpenDoorNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mac  = notify.mac();
            to_aws = CheckToAwsByUid(mac, 1);
            break;
        }
        case MSG_P2A_ONCE_AUTOP:    //用于亚马逊迁移调度处理
        {
            AK::Adapt::OnceAutopNotify notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mng  = std::to_string(notify.project_id());
            tube_id = GetCommTube();

            to_aws = CheckToAwsByAccountID(notify.project_id());
            break;
        }
        case MSG_P2A_DEVICE_NOTIFY_REMOTE_OPENDOOR:    //家居设备通过web远程开门
        {
            tube_id = GetCommTube();
            
            AK::Adapt::OpenDoorNotifyMsg notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mac  = notify.mac();
            to_aws = CheckToAwsByUid(mac, 1);
            break;
        }
        case MSG_P2A_DEVICE_NOTIFY_REQUEST_CAPTURE:    //家居设备通过web监控截屏
        {
            tube_id = GetCommTube();

            AK::Adapt::RequestDeviceCaptureNotifyMsg notify;
            CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));
            std::string mac  = notify.mac();
            to_aws = CheckToAwsByUid(mac, 1);
            break;
        }
        //非配置类消息，轮询分配
        default:    
        {
            tube_id = GetCommTube();
            break;
        }
    }

    //写配置相关的，改为走kafka投递
    if(tube_id < thread_wirte_file_num_)
    {
        AddCommonMsgToKafka(msg, len, kafka_key);
    }
    else
    {
        AddMsgToBeanstalk(msg, len, tube_id, to_aws, BEANSTALK_NORMAL_DELAY);   //分配到不同队列，由不同的线程消费
    }
}

//beanstalk因为目前是单节点 因此状态检查判断生产者状态即可
bool CUnixSocketControl::CheckBeanstalkStatus()
{
    if(!g_bs_client_ptr)
    {
        return false;
    }
    return true;
}

void CUnixSocketControl::InitPduBeanstalk()
{
    g_bs_client_ptr = new Beanstalk::Client(gstCSADAPTConf.szBeanStalkAddr, BEANSTALK_SERVER_PORT);
}


//beanstalk因为目前是单节点 因此状态检查判断生产者状态即可
bool CUnixSocketControl::CheckBeanstalkBackUpStatus()
{
    if(!g_bs_backup_client_ptr)
    {
        return false;
    }
    return true;
}

void CUnixSocketControl::AddMsgToBeanstalk(const char* msg, int len, int tube_id, int to_aws, int delay_interval)
{
    //beanstalk写文件队列分配失效引起
    if(tube_id < 0)
    {
        AK_LOG_WARN << "get incorrect tube id. tube =" << tube_id;
        return;
    }

    char tube[128];
    //默认1秒，aws延迟3秒，异常刷配置延迟180s
    uint32_t delay = delay_interval;

    if(to_aws)
    {
        snprintf(tube, sizeof(tube), "aws_web_to_adapt%d_%s", tube_id, gstCSADAPTConf.szServerInnerIP);
    }
    else
    {
        snprintf(tube, sizeof(tube), "web_to_adapt%d_%s", tube_id, gstCSADAPTConf.szServerInnerIP);
    }
    AK_LOG_INFO << "Add msg to beanstalk tube:" << tube;
    int is_put_ok = 1;
    if (!g_bs_client_ptr)
    {
        std::lock_guard<std::mutex> lock(g_bs_mutex);
        g_bs_client_ptr = new Beanstalk::Client(gstCSADAPTConf.szBeanStalkAddr, BEANSTALK_SERVER_PORT);
        g_bs_client_ptr->use(tube);
        if (!g_bs_client_ptr->put(msg, len, 0, delay, 1))
        {
            if (g_bs_client_ptr)
            {
                delete g_bs_client_ptr;
                g_bs_client_ptr = nullptr;
            }
            AK_LOG_WARN << "beanstalk put message error, put another";
            is_put_ok = 0;
        }
    }
    else
    {
        std::lock_guard<std::mutex> lock(g_bs_mutex);
        g_bs_client_ptr->use(tube);
        if (!g_bs_client_ptr->put(msg, len, 0, delay, 1))
        {
            if (g_bs_client_ptr)
            {
                delete g_bs_client_ptr;
                g_bs_client_ptr = nullptr;
            }
            AK_LOG_WARN << "beanstalk put message error, put another";
            is_put_ok = 0;
        }
    }
    if (!is_put_ok)
    {
        AddMsgToBeanstalkBackup(msg, len, tube_id, to_aws, delay);
    }
}

void CUnixSocketControl::AddMsgToBeanstalkBackup(const char* msg, int len, int tube_id, int to_aws, int delay_interval)
{
    if(0 == strlen(gstCSADAPTConf.beanstalk_backup_ip))
    {
        return;
    }
    
    char tube[128];
    uint32_t delay = delay_interval;
    if(to_aws)
    {
        snprintf(tube, sizeof(tube), "aws_web_to_adapt%d_%s", tube_id, gstCSADAPTConf.szServerInnerIP);
    }
    else
    {
        snprintf(tube, sizeof(tube), "web_to_adapt%d_%s", tube_id, gstCSADAPTConf.szServerInnerIP);
    }
    AK_LOG_INFO << "Add msg to backup beanstalk tube:" << tube;
    
    if (!g_bs_backup_client_ptr)
    {
        std::lock_guard<std::mutex> lock(g_bs_backup_mutex);
        g_bs_backup_client_ptr = new Beanstalk::Client(gstCSADAPTConf.beanstalk_backup_ip, BEANSTALK_SERVER_PORT);
        g_bs_backup_client_ptr->use(tube);
        if (!g_bs_backup_client_ptr->put(msg, len, 0, delay, 1))
        {
            if (g_bs_backup_client_ptr)
            {
                delete g_bs_backup_client_ptr;
                g_bs_backup_client_ptr = nullptr;
            }
            AK_LOG_WARN << "backup beanstalk put message error";
        }
    }
    else
    {
        std::lock_guard<std::mutex> lock(g_bs_backup_mutex);
        g_bs_backup_client_ptr->use(tube);
        if (!g_bs_backup_client_ptr->put(msg, len, 0, delay, 1))
        {
            if (g_bs_backup_client_ptr)
            {
                delete g_bs_backup_client_ptr;
                g_bs_backup_client_ptr = nullptr;
            }
            AK_LOG_WARN << "backup beanstalk put message error";
        }
    }
}


void CUnixSocketControl::ProcessMsgForBeanstalk(int tube_id, const std::string& beanstalk_ip)
{
    char tube[128];
    if(gstCSADAPTConf.is_aws)
    {
        snprintf(tube, sizeof(tube), "aws_web_to_adapt%d_%s", tube_id, gstCSADAPTConf.szServerInnerIP);    //每个线程处理不同队列
    }
    else
    {
        snprintf(tube, sizeof(tube), "web_to_adapt%d_%s", tube_id, gstCSADAPTConf.szServerInnerIP);    //每个线程处理不同队列
    }
    AK_LOG_INFO << "ProcessThread:" << tube;
    while (1)
    {
        Beanstalk::Client client(beanstalk_ip.c_str(), BEANSTALK_SERVER_PORT);
        bool bs_stas = client.watch(tube);
        Beanstalk::Job job;
        while (client.reserve(job)) //reserve接口阻塞，正常情况不会跳出循环
        {
            client.del(job.id());
            GetUnixSocketControlInstance()->DispatchMsg((void*)job.body().c_str(), job.body().length());
        }
        sleep(1);
    }
}

//nMsgLen:udp消息的整体长度,包括消息头
int CUnixSocketControl::DispatchMsg(void* pMsgBuf, unsigned int nMsgLen)
{
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);

    if ((NULL == pMsgBuf) || (nMsgLen < CS_COMMON_MSG_HEADER_SIZE))
    {
        return -1;
    }
    BOOL IsNeedResp = FALSE;
    uint32_t id = NTOHL(*((unsigned int*)((unsigned char*)pMsgBuf + 4)));
    uint32_t project_type = NTOHL(*((unsigned int*)((unsigned char*)pMsgBuf + 8)));
    uint32_t oem_type = NTOHL(*((unsigned int*)((unsigned char*)pMsgBuf + 12)));

    if(oem_type == oem::OEM_TYPE::AZER) //阿塞拜疆定制云特殊消息处理
    {
        AzerUnixSocketControl::GetInstance()->OnSocketMsg(pMsgBuf, nMsgLen);
        return 1;
    }

    // 透传不区分项目类型
    switch (id)
    {
        case MSG_P2A_NOTIFY_REMOTE_OPENDOOR:
        {
            GetAKCSViewInstance()->OnRemoteOpenDoor(pMsgBuf, nMsgLen);
            return 1;
        }
        case MSG_P2A_NOTIFY_REMOTE_OPEN_SECURITY_RELAY:
        {
            GetAKCSViewInstance()->OnRemoteOpenSecurityRelay(pMsgBuf, nMsgLen);
            return 1;
        }
    }

    //办公
    if (project_type ==project::OFFICE)
    {
        //办公处理过了直接退出
        OfficeUnixMsgControl::Instance()->OnSocketMsg(pMsgBuf, nMsgLen);
        return 1;
    }
    
    switch (id)
    {
        //php请求远程REBOOT设备  added by chenyc, 2019-03-06,分布式,web上面已经不支持了.
        case MSG_P2A_REBOOT_TO_DEVICE:
        {
            GetAKCSViewInstance()->OnDeviceReboot(pMsgBuf, nMsgLen);
            break;
        }
        //added by chenyc,2017-09-01,以下开始为个人终端用户部分
        case MSG_P2A_RESET_PASSWD:
        {
            GetAKCSViewInstance()->OnResetPasswd(pMsgBuf, nMsgLen);
            break;
        }
        //同一联动系统中的用户在界面上或者app上处理告警的时候通知csmain去通知设备告警处理的结果
        case MSG_P2A_PERSONNAL_ALARM_DEAL:
        {
            GetAKCSViewInstance()->OnPersonalAlarmDeal(pMsgBuf, nMsgLen);
            break;
        }

        //个人终端用户删除图片
        case MSG_P2A_PERSONNAL_DEL_PIC:
        case MSG_P2A_COMMUNITY_DEL_PIC:
        {
            GetAKCSViewInstance()->OnPersonalDelPic(pMsgBuf, nMsgLen);
            break;
        }
        ///////////////////////////////// v3.2 ////////////////

        case MSG_P2A_PERSONNAL_CREATE_UID:
        case MSG_P2A_COMMUNITY_CREATE_UID:
        {
            GetAKCSViewInstance()->OnCreateUid(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_PERSONNAL_CHANGE_PWD:
        case MSG_P2A_COMMUNITY_CHANGE_PWD:
        {
            GetAKCSViewInstance()->OnChangePwd(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_PERSONNAL_NEW_TEXT_MESSAGE:
        case MSG_P2A_COMMUNITY_NEW_TEXT_MESSAGE:
        {
            GetAKCSViewInstance()->OnSendMessage(pMsgBuf, nMsgLen);
            break;
        }
        /**************************社区****************************************/
        //社区警告消息处理
        case MSG_P2A_COMMUNITY_ALARM_DEAL:
        {
            GetAKCSViewInstance()->OnCommunityAlarmDeal(pMsgBuf, nMsgLen);
            break;
        }
        //社区修改主账号信息
        case MSG_P2A_COMMUNITY_MODIFY_MASTER_USER:
        {
            AK_LOG_INFO << "Request community change master user.";
            //GetAKCSViewInstance()->OnCommunityAlarmDeal(pMsgBuf, nMsgLen);
            break;
        }
        //设备app过期,modified by chenyc,2019-07-01,v4.5版本设备不设置过期时间
        case MSG_P2A_APP_EXPIRE:
        {
            GetAKCSViewInstance()->OnAppExpire(pMsgBuf, nMsgLen);
            break;
        }
        //app即将过期,modified by chenyc,2019-07-01,v4.5版本设备不设置过期时间
        case MSG_P2A_APP_WILLBE_EXPIRE:
        {
            GetAKCSViewInstance()->OnAppWillBeExpire(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_DEL_PER_PUBLIC_DEV_VIRTUAL_ACCOUNT:/*弃用*/
        {
            //$MngAccount 管理员的账号,平台修改这个管理员下的所有联动
            GetAKCSViewInstance()->OnPerPublicDevDelVirtAccount(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_ADD_VIDEO_SCHED:
        {
            GetAKCSViewInstance()->OnAddVideoStorageSched(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_DEL_VIDEO_SCHED:
        {
            GetAKCSViewInstance()->OnDelVideoStorageSched(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_DEL_VIDEO_STORAGE: //删除视频片段
        {
            GetAKCSViewInstance()->OnDelVideoStorage(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_ACOUNT_ACTIVE:
        {
            GetAKCSViewInstance()->OnAccountActiveEmail(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_SHARE_TEMPKEY:
        {
            GetAKCSViewInstance()->OnShareTempKeyEmail(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_PM_EMERGENCY_DOOR_CONTROL:
        {	
            GetAKCSViewInstance()->OnPmEmergencyDoorControl(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_CREATE_PROPERTY_WORK:
        {
            GetAKCSViewInstance()->OnCreatePropertyWorkEmail(pMsgBuf, nMsgLen);
            break;
        }
        //////////////////////// v4.5/////////////////////////
        case MSG_P2A_NOTIFY_PM_ACCOUNT_WILL_EXPIRE://通知PM有多少账号即将过期
        {
            GetAKCSViewInstance()->OnPMAccountWillExpire(pMsgBuf, nMsgLen);
            break;
        }
        //////////////////////// v4.6/////////////////////////
        case MSG_P2A_NOTIFY_ALEXA_LOGIN:
        {
            GetAKCSViewInstance()->OnAlexaLogin(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_ALEXA_SET_ARMING:
        {
            GetAKCSViewInstance()->OnAlexaSetArming(pMsgBuf, nMsgLen);
            break;
        }
        /*
        {
            GetAKCSViewInstance()->OnUpdateMonthlyFee(pMsgBuf, nMsgLen);
            break;
        }
        */
        case MSG_P2A_PHONE_EXPIRE:
        {
            GetAKCSViewInstance()->OnPhoneExpire(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_PHONE_WILL_EXPIRE:
        {
            GetAKCSViewInstance()->OnPhoneWillExpire(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_INSTALLER_APP_WILL_EXPIRE:
        {
            GetAKCSViewInstance()->OnInstallerAppWillExpire(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_INSTALLER_PHONE_WILL_EXPIRE:
        {
            GetAKCSViewInstance()->OnInstallerPhoneWillExpire(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_INSTALLER_APP_EXPIRE:
        {
            GetAKCSViewInstance()->OnInstallerAppExpire(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_INSTALLER_PHONE_EXPIRE:
        {
            GetAKCSViewInstance()->OnInstallerPhoneExpire(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_PM_FEATURE_WILL_EXPIRE:
        {
            GetAKCSViewInstance()->OnPMFeatureWillExpire(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_INSTALLER_FEATURE_WILL_EXPIRE:
        {
            GetAKCSViewInstance()->OnInstallerFeatureWillExpire(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_CREATE_REMOTE_DEV_CONTORL:
        {
            GetAKCSViewInstance()->OnCreateRemoteDevContorl(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_FACESERVER_PIC_DOWNLOAD:
        {
            GetAKCSViewInstance()->OnFaceServerPicDownload(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_FACESERVER_PIC_MODIFY:
        {
            GetAKCSViewInstance()->OnFaceServerPicModify(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_FACESERVER_PIC_DELETE:
        {
            GetAKCSViewInstance()->OnFaceServerPicDelete(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_FACESERVER_PIC_BATCH_DOWNLOAD:
        {
            GetAKCSViewInstance()->OnFaceServerPicBatchDownload(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_FACESERVER_PIC_BATCH_MODIFY:
        {
            GetAKCSViewInstance()->OnFaceServerPicBatchModify(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_FACESERVER_PIC_BATCH_DELETE:
        {
            GetAKCSViewInstance()->OnFaceServerPicBatchDelete(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_PM_EXPORT_LOG:
        {
            GetAKCSViewInstance()->OnPmExportLog(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_APP_FEEDBACK:
        {
            GetAKCSViewInstance()->OnFeedbackNotify(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_INS_APP_FEEDBACK:
        {
            GetAKCSViewInstance()->OnInsAppFeedback(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_SEND_SMS_CODE:
        {
            GetAKCSViewInstance()->OnSendSmsCode(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_UPDATE_MAC_CONFIG:
        {
            GetAKCSViewInstance()->OnUpdateMacConfig(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_ONCE_AUTOP:
        {
            GetAKCSViewInstance()->OnOnceAutopNotify(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_DELETE_APP_ACCOUNT:
        {
            GetAKCSViewInstance()->OnDelAppAccountNotify(pMsgBuf, nMsgLen);
            break;
        }
        //PM APP用户即将过期
        case MSG_P2A_NOTIFY_PM_APP_ACCOUNT_WILL_EXPIRE:
        {
            GetAKCSViewInstance()->OnPMAppAccountWillBeExpire(pMsgBuf, nMsgLen);
            break;
        }
        //PM APP用户过期
        case MSG_P2A_NOTIFY_PM_APP_ACCOUNT_EXPIRE:
        {
            GetAKCSViewInstance()->OnPMAppAccountExpire(pMsgBuf, nMsgLen);
            break;
        }
        //PM APP用户激活
        case MSG_P2A_NOTIFY_PM_ACOUNT_ACTIVE:
        {
            GetAKCSViewInstance()->OnPMAccountActiveEmail(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_OPERATE_THIRD_PARTYA_LOCK:
        {
            GetAKCSViewInstance()->OnThirdPartyLockNotify(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_RESET_TO_DEVICE:
        {
            GetAKCSViewInstance()->OnDeviceReset(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_COMMUNITY_ADD_NEW_SITE:
        case MSG_P2A_PERSONAL_ADD_NEW_SITE:
        case MSG_P2A_PM_APP_ADD_NEW_SITE:
        {
            GetAKCSViewInstance()->OnUserAddNewSite(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_PM_LINK_NEW_SITES:
        {
            GetAKCSViewInstance()->OnPmWebLinkNewSites(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_PM_WEB_CREATE_UID:
        {
            GetAKCSViewInstance()->OnPmWebCreateUid(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_PM_WEB_CHANGE_PWD:
        {
            GetAKCSViewInstance()->OnPmWebChangePwd(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_SEND_COMMON_SMS_CODE:
        {
            GetAKCSViewInstance()->OnSendCommonSmsCode(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_SEND_COMMON_EMAIL_CODE:
        {
            GetAKCSViewInstance()->OnSendCommonEmailCode(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_PCAP_CAPTURE_CONTROL:
        {
            GetAKCSViewInstance()->OnPcapCaptureNotify(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_SEND_EMAIL_NOTIFY:
        {
            GetAKCSViewInstance()->OnSendEmailNotify(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_SEND_EMAIL_CRONTAB_NOTIFY:
        {
            GetAKCSViewInstance()->OnSendCrontabEmailNotify(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_KIT_ACCOUNT_LOG_OFF:
        {
            GetAKCSViewInstance()->OnKitAccountLogOff(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_SIP_PCAP_CAPTURE_CONTROL:
        {
            GetAKCSViewInstance()->OnSipPcapCaptureNotify(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_C2S_OPENAPI_SOCKET_HEALTH_CHECK:
        {
            GetAKCSViewInstance()->OnOpenApiHealthCheckNotify(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_DEVICE_NOTIFY_REMOTE_OPENDOOR:
        {
            GetAKCSViewInstance()->OnDeviceRemoteOpenDoor(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_DEVICE_NOTIFY_REQUEST_CAPTURE:
        {
            GetAKCSViewInstance()->OnRequestDeviceCapture(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_NOTIFY_SMARTLOCK_UPDATE:
        {
            GetAKCSViewInstance()->OnSmartLockUpdate(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_SMARTLOCK_HTTP_UP_MSG_ROUTE:
        {
            GetAKCSViewInstance()->OnSmartLockHttpUpMessageRoute(pMsgBuf, nMsgLen);
            break;
        }
        case MSG_P2A_SMARTLOCK_UNLOCK_SL50:
        {
            GetAKCSViewInstance()->OnSmartLockUnlockSL50(pMsgBuf, nMsgLen);
            break;
        }
        default:
        {
            //TODO,chenyc:响应消息类型不匹配(由于具体消息ID未知,故只能由通用消息头带回)
            AK_LOG_WARN << "Failed to match msg id:" << id;
            return -1;
        }
    }


    return 0;
}


int CUnixSocketControl::GetWirteFileThreadNum()
{
    return thread_wirte_file_num_;
}

void CUnixSocketControl::AddCommonMsgToKafka(char* msg, int len, const std::string& kafka_key)
{
    if ((nullptr == msg) || (len < CS_COMMON_MSG_HEADER_SIZE))
    {
        return;
    }
        
    std::string kafka_msg(msg, len);
    AKCS::Singleton<AkcsKafkaProducerNotifyConfig>::instance().ProduceMsgWithLock(kafka_key, kafka_msg);
}
