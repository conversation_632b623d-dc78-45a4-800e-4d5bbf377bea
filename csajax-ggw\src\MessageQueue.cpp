#include "MessageQueue.h"



void MessageQueue::Push(const std::string& message) 
{
    std::unique_lock<std::mutex> lock(mutex_);
    queue_.push(message);
    lock.unlock();
    condition_.notify_one();
}

std::string MessageQueue::Pop() 
{
    std::unique_lock<std::mutex> lock(mutex_);
    condition_.wait(lock, [this] 
                { 
                    return !queue_.empty() || stop_; 
                });

    std::string message = queue_.front();
    queue_.pop();
    return message;
}

bool MessageQueue::Empty() const 
{
    std::lock_guard<std::mutex> lock(mutex_);
    return queue_.empty();
}

size_t MessageQueue::Size() const 
{
    std::lock_guard<std::mutex> lock(mutex_);
    return queue_.size();
} 

void MessageQueue::ShutDown()
{
    std::lock_guard<std::mutex> lock(mutex_);
    stop_ = true;
    condition_.notify_all();
}