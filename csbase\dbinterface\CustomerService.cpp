#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "CustomerService.h"

namespace dbinterface {

static const std::string customer_service_info_sec = " MngAccount,Phone,Email,ReceiveFeedback,EmailForRentManager ";

void CustomerService::GetCustomerServiceFromSql(CustomerServiceInfo& customer_service_info, CRldbQuery& query)
{
    Snprintf(customer_service_info.mng_account, sizeof(customer_service_info.mng_account), query.GetRowData(0));
    Snprintf(customer_service_info.phone, sizeof(customer_service_info.phone), query.GetRowData(1));
    Snprintf(customer_service_info.email, sizeof(customer_service_info.email), query.GetRowData(2));
    customer_service_info.receive_feedback = ATOI(query.GetRowData(3));
    Snprintf(customer_service_info.email_for_rent_manager, sizeof(customer_service_info.email_for_rent_manager), query.GetRowData(4));
    return;
}

int CustomerService::GetCustomerServiceByMngAccount(const std::string& mng_account, CustomerServiceInfo& customer_service_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << customer_service_info_sec << " from CustomerService where MngAccount = '" << mng_account << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetCustomerServiceFromSql(customer_service_info, query);
    }
    else
    {
        AK_LOG_WARN << "get CustomerServiceInfo by MngAccount failed, MngAccount = " << mng_account;
        return -1;
    }
    return 0;
}


}