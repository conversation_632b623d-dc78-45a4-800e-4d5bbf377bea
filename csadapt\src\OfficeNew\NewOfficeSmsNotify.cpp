#include "NewOfficeSmsNotify.h"
#include "KafkaConsumerPushTopicHandle.h"

extern CSADAPT_CONF gstCSADAPTConf;
extern RouteMQProduce* g_nsq_producer;

static const std::vector<std::string> kKeys = {"type", "area_code", "phone", "language", "code"};

void NewOfficeSmsNotify::Handle(const std::string& web_notify_msg, const std::string& msg_type, const KakfaMsgKV &kv, const std::string& oem_name)
{
    if (!KafkaWebMsgParse::CheckKeysExist(kv, kKeys))
    {
        AK_LOG_WARN << "NewOfficeSmsNotify CheckKeysExist error, web_notify_msg = " << web_notify_msg;
        return;
    }
    
    AK_LOG_INFO << "NewOfficeSmsNotify web_notify_msg = " << web_notify_msg;

    SmsType sms_type = SmsType(ATOI(kv.at("type").c_str()));
    if (sms_type == SmsType::SMS_COMMON_SEND_CODE)
    {
        SendCommonSmsCode(kv);
    }
    else if (sms_type == SmsType::SMS_DEL_APP_ACCOUNT)
    {
        SendDeleteAppAccountCode(kv);
    }
    else if (sms_type == SmsType::SMS_RESET)
    {
        SendAdminAppResetPWD(kv);
    }
    return;
}

void NewOfficeSmsNotify::SendCommonSmsCode(const KakfaMsgKV &kv)
{
    if (!KafkaWebMsgParse::CheckKeysExist(kv, {"code", "phone", "code_type", "language", "area_code"}))
    {
        AK_LOG_WARN << "SendCommonSmsCode error.";
        return;
    }

    AK::Server::P2PSendCodeToMobile send_sms_code;
    send_sms_code.set_code(kv.at("code"));
    send_sms_code.set_phone(kv.at("phone"));
    send_sms_code.set_type(kv.at("code_type"));
    send_sms_code.set_language(kv.at("language"));
    send_sms_code.set_area_code(kv.at("area_code"));
    
    AK_LOG_INFO << "SendCommonSmsCode = " << send_sms_code.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&send_sms_code);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_CODE_TO_MOBILE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);    
  
    return;
}

void NewOfficeSmsNotify::SendDeleteAppAccountCode(const KakfaMsgKV &kv)
{
    if (!KafkaWebMsgParse::CheckKeysExist(kv, {"code", "phone", "language", "area_code"}))
    {
        AK_LOG_WARN << "SendDeleteAppAccountCode error.";
        return;
    }

    AK::Server::SendSmsCodeDelAppAccount send_sms_code;
    send_sms_code.set_code(kv.at("code"));
    send_sms_code.set_phone(kv.at("phone"));
    send_sms_code.set_language(kv.at("language"));
    send_sms_code.set_area_code(kv.at("area_code"));
    send_sms_code.set_type(SMS_DEL_APP_ACCOUNT);
    
    AK_LOG_INFO << "SendDeleteAppAccountCode = " << send_sms_code.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&send_sms_code);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_SMS_DELETE_APP_ACCOUNT);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return;
}

void NewOfficeSmsNotify::SendAdminAppResetPWD(const KakfaMsgKV &kv)
{
    if (!KafkaWebMsgParse::CheckKeysExist(kv, {"code", "phone", "language", "area_code"}))
    {
        AK_LOG_WARN << "SendAdminAppResetPWD error.";
        return;
    }

    AK::Adapt::SendSmsCode send_sms_code;
    send_sms_code.set_type(SMS_RESET);
    send_sms_code.set_code(kv.at("code"));
    send_sms_code.set_area_code(kv.at("area_code"));
    send_sms_code.set_phone(kv.at("phone"));
    send_sms_code.set_user_type(static_cast<google::protobuf::uint32>(PhoneUserType::ADMIN));
    
    AK_LOG_INFO << "SendAdminAppResetPWDCode = " << send_sms_code.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&send_sms_code);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_SMS_CODE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return;
}


