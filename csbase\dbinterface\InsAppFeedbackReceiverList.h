#ifndef __DB_INS_APP_FEEDBACK_RECEIVER_LIST_H__
#define __DB_INS_APP_FEEDBACK_RECEIVER_LIST_H__

#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

namespace dbinterface {

class InsAppFeedbackReceiverList
{
public:
    static int AddAppFeedbackReceiver(const std::string& receiver_account, const std::string& feedback_uuid);

private:
    InsAppFeedbackReceiverList() = delete;
    ~InsAppFeedbackReceiverList() = delete;
};

}
#endif 