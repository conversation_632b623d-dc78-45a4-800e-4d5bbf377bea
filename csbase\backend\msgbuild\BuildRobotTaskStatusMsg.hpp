#ifndef __BUILD_ROBOT_TASK_STATUS_MSG_HPP__
#define __BUILD_ROBOT_TASK_STATUS_MSG_HPP__

#include "util.h"
#include <string>
#include "XmlMsgBuilder.h"
#include "AkLogging.h"

namespace akcs_msgbuild {

/*
发送机器人任务状态给室内机
<Msg>
<Type>RobotTaskStatus</Type>
    <Params>
        <TaskId></TaskId>  // 任务ID
        <Status></Status>  // 任务状态，0~10
        <WithdrawTimeout></WithdrawTimeout>  // 取物超时时间
   </Params>
</Msg>
*/
inline int BuildRobotTaskStatusMsg(const std::string &task_id, RobotTaskStatus task_status, int withdraw_timeout, std::string &msg)
{
    XmlBuilder xml_builder("RobotTaskStatus");
    
    XmlKV tag_map;
    tag_map["TaskId"] = task_id;
    tag_map["Status"] = std::to_string((int)task_status);
    tag_map["WithdrawTimeout"] = std::to_string(withdraw_timeout);

    xml_builder.AddKeyValue(tag_map);
    
    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "BuildRobotTaskStatusMsg msg:\n" << msg;
    return 0;
}

} // namespace CMsgBuildHandle

#endif // __BUILD_ROBOT_TASK_STATUS_MSG_HPP__ 