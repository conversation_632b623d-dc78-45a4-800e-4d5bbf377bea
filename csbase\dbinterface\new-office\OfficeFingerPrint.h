#ifndef __DB_FINGER_H__
#define __DB_FINGER_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct FingerPrintInfo_T
{
    char uuid[36];
    char account_uuid[36];
    char personal_account_uuid[36];
    char fingerprint[2048];
    char fingerprint_md5[33];
    FingerPrintInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} FingerPrintInfo;

using UserFingerPrintMap = std::map<std::string/*user uuid*/, FingerPrintInfo>;

namespace dbinterface {

class UserFingerPrint
{
public:
    static int GetFingerPrintByProjectUUID(const std::string& personal_account_uuid, UserFingerPrintMap& fingerprint_info);
private:
    UserFingerPrint() = delete;
    ~UserFingerPrint() = delete;
    static void GetFingerPrintFromSql(FingerPrintInfo& fingerprint_info, CRldbQuery& query);
};

}
#endif
