#include <sstream>
#include "PersonalCapture.h"
#include <string.h>
#include "AkLogging.h"
#include <boost/algorithm/string.hpp>
#include <boost/functional/hash.hpp>
#include <boost/format.hpp>
#include <boost/crc.hpp>
#include "dbinterface/Log/LogSlice.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include <string.h>
#include "dbinterface/InterfaceComm.h"
#include "LogConnectionPool.h"
#include "AkcsCommonDef.h"
#include "ConnectionManager.h"
#include "dbinterface/UUID.h"

namespace dbinterface
{

static const char table_personnal_capture[] = "PersonalCapture";
static const char table_personnal_motion[] = "PersonalMotion";
static const char open_door_pm_manually[] = "Manually Unlock";
static const char open_door_automatically[] = "Automatically Unlock";
static const char lock_door_pm_manually[] = "Manually Lock";
static const char emergency_contorl_room_num[] = "--";
static const char emergency_contorl_key[] = "--";


PersonalCapture::PersonalCapture()
{
    
}

int PersonalCapture::AddPersonalCapture(SOCKET_MSG_DEV_REPORT_ACTIVITY& personnal_act_log, int delivery)
{
    if (delivery == 0)
    {
        AK_LOG_WARN << "AddPersonalCapture failed: delivery is 0";
        return -1;
    } 
    
    if (strlen(personnal_act_log.room_num) == 0)
    {
        Snprintf(personnal_act_log.room_num, sizeof(personnal_act_log.room_num),"--");
    }
    //人脸类型，key置为"--"
    if (personnal_act_log.act_type == ACT_OPEN_DOOR_TYPE::FACE)
    {
        Snprintf(personnal_act_log.key, sizeof(personnal_act_log.key), "--");
    }

    //插入数据构造
    std::map<std::string, std::string> strMap;
    strMap.emplace("MAC", personnal_act_log.mac);
    if(strlen(personnal_act_log.dev_uuid) > 0){
        strMap.emplace("DevicesUUID", personnal_act_log.dev_uuid);
    }
    strMap.emplace("PicName", personnal_act_log.pic_name);
    strMap.emplace("ThirdCameraPicName", personnal_act_log.third_camera_pic_name);
    if (personnal_act_log.capture_time > 0) {
        strMap.emplace("sql_CaptureTime", "FROM_UNIXTIME(" + std::to_string(personnal_act_log.capture_time) + ")");
    } else {
        strMap.emplace("sql_CaptureTime", "now()");//数据库系统函数，key以"sql_"为前缀
    }
    strMap.emplace("CaptureAction", personnal_act_log.capture_action);
    strMap.emplace("Initiator", personnal_act_log.initiator_sql);
    strMap.emplace("Node", personnal_act_log.account);
    strMap.emplace("RoomNum", personnal_act_log.room_num);
    strMap.emplace("KeyNum", personnal_act_log.key);
    strMap.emplace("SipAccount", personnal_act_log.sip_account);
    strMap.emplace("Location", personnal_act_log.location); 
    strMap.emplace("VideoRecordName", personnal_act_log.video_record_name);
    if(strlen(personnal_act_log.video_url) > 0){
        strMap.emplace("VideoRecordUrl", personnal_act_log.video_url); 
    }
    if(strlen(personnal_act_log.company_uuid) > 0){
        strMap.emplace("OfficeCompanyUUID", personnal_act_log.company_uuid); 
    }
    if(strlen(personnal_act_log.project_uuid2) > 0){
        strMap.emplace("ProjectUUID", personnal_act_log.project_uuid2); 
    }
    if (strlen(personnal_act_log.door_name_list) > 0) {
        strMap.emplace("DoorNameList", personnal_act_log.door_name_list); 
    }

    // 离线时直接处理
    if (strlen(personnal_act_log.pic_url) > 0) {
        strMap.emplace("PicUrl", personnal_act_log.pic_url); 
    }
    if (strlen(personnal_act_log.spic_url) > 0) {
        strMap.emplace("SPicUrl", personnal_act_log.spic_url); 
    }

    // CallTraceID字段定义为NULL, CallLog也插入空字符串会关联到CallTraceID为空的图片
    if (strlen(personnal_act_log.call_trace_id) > 0)
    {
        strMap.emplace("CallTraceID", personnal_act_log.call_trace_id); 
    }

    std::map<std::string, int> intMap;
    intMap.emplace("Response", personnal_act_log.resp);
    intMap.emplace("CaptureType", personnal_act_log.act_type);
    intMap.emplace("MngAccountID", personnal_act_log.mng_id);
    intMap.emplace("DevType", personnal_act_log.is_public);
    intMap.emplace("MngType", personnal_act_log.mng_type);
    intMap.emplace("UnitID", personnal_act_log.unit_id);
    intMap.emplace("Relay", DoornumToRelayStatus(personnal_act_log.relay));
    intMap.emplace("SecurityRelay", DoornumToRelayStatus(personnal_act_log.srelay));
    intMap.emplace("UserType", (int)personnal_act_log.user_type);
    intMap.emplace("AccessMode", personnal_act_log.access_mode);
    if (strlen(personnal_act_log.account_uuid) > 0) {
        strMap.emplace("PersonalAccountUUID", personnal_act_log.account_uuid);
    }

    //表名构造
    std::string table_name = PersonalCapture::GetLogTableName(table_personnal_capture, personnal_act_log.db_delivery_uuid, delivery);
    
    GET_LOG_DB_CONN_ERR_RETURN(conn, -1, personnal_act_log.db_delivery_uuid);
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }   
    int ret = tmp_conn->InsertData(table_name, strMap, intMap);

    return ret;
}

// int PersonalCapture::AddOfflineLogCapture(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_log, int delivery)
// {
//     if (delivery == 0)
//     {
//         AK_LOG_WARN << "AddOfflineLogCapture failed: delivery is 0";
//         return -1;
//     }

//     RldbPtr conn = GetLogDBConnPollInstance()->GetConnection();
//     CRldb* temp_conn = conn.get();
//     if (NULL == temp_conn)
//     {
//         AK_LOG_WARN << "Get DB conn failed.";
//         return -1;
//     }

//     if (strlen(act_log.room_num) == 0)
//     {
//         Snprintf(act_log.room_num, sizeof(act_log.room_num),"--");
//     }
//     //人脸类型，key置为"--"
//     if (act_log.act_type == ACT_OPEN_DOOR_TYPE::FACE)
//     {
//         Snprintf(act_log.key, sizeof(act_log.key), "--");
//     }

//     //插入数据构造
//     std::map<std::string, std::string> strMap;
//     strMap.emplace("MAC", act_log.mac);
//     if(strlen(act_log.dev_uuid) > 0){
    //     strMap.emplace("DevicesUUID", act_log.dev_uuid);
    // }
    // strMap.emplace("PicName", act_log.pic_name);
//     strMap.emplace("sql_CaptureTime", "FROM_UNIXTIME(" + std::to_string(act_log.capture_time) + ")");
//     strMap.emplace("CaptureAction", act_log.capture_action);
//     strMap.emplace("Initiator", act_log.initiator_sql);
//     strMap.emplace("Node", act_log.account);
//     strMap.emplace("RoomNum", act_log.room_num);
//     strMap.emplace("KeyNum", act_log.key);
//     strMap.emplace("SipAccount", act_log.sip_account);
//     strMap.emplace("Location", act_log.location);
//     strMap.emplace("PicUrl", act_log.pic_url);
//     strMap.emplace("SPicUrl", act_log.spic_url);
//     strMap.emplace("VideoRecordName", act_log.video_record_name);
//     strMap.emplace("VideoRecordUrl", act_log.video_url);

//     if(strlen(act_log.project_uuid) > 0){
//         strMap.emplace("ProjectUUID", act_log.project_uuid); 
//     }

//     std::map<std::string, int> intMap;
//     intMap.emplace("Response", act_log.resp);
//     intMap.emplace("CaptureType", act_log.act_type);
//     intMap.emplace("MngAccountID", act_log.mng_id);
//     intMap.emplace("DevType", act_log.is_public);
//     intMap.emplace("MngType", act_log.mng_type);
//     intMap.emplace("UnitID", act_log.unit_id);
//     intMap.emplace("Relay", DoornumToRelayStatus(act_log.relay));
//     intMap.emplace("SecurityRelay", DoornumToRelayStatus(act_log.srelay));

//     //表名构造
//     std::string table_name = PersonalCapture::GetLogTableName(table_personnal_capture, act_log.project_uuid, delivery);

//     int ret = conn->InsertData(table_name, strMap, intMap);

//     //释放数据库连接
//     ReleaseLogDBConn(conn);
//     return ret;
// }


//添加Capture, app request capture.
int PersonalCapture::AddUidReqCapture(UIPC_MSG_CAPTURE_RTSP& personnal_request_capture, int delivery)
{
    if (delivery == 0)
    {
        AK_LOG_WARN << "AddUidReqCapture failed: delivery is 0";
        return -1;
    }

    //插入数据构造
    std::map<std::string, std::string> str_map;
    str_map.emplace("MAC", personnal_request_capture.mac);
    if(strlen(personnal_request_capture.dev_uuid) > 0){
        str_map.emplace("DevicesUUID", personnal_request_capture.dev_uuid);
    }
    str_map.emplace("PicName", personnal_request_capture.picture_name);
    str_map.emplace("sql_CaptureTime", "now()");
    str_map.emplace("Node", personnal_request_capture.node);
    str_map.emplace("Initiator", personnal_request_capture.username);
    str_map.emplace("RoomNum", personnal_request_capture.room_num);
    str_map.emplace("CaptureAction", "Capture on SmartPlus");
    str_map.emplace("SipAccount", personnal_request_capture.sip_account);
    str_map.emplace("Location", personnal_request_capture.location);
    if(strlen(personnal_request_capture.project_uuid) > 0)
    {
        str_map.emplace("ProjectUUID", personnal_request_capture.project_uuid);
    }
    if(strlen(personnal_request_capture.company_uuid) > 0)
    {
        str_map.emplace("OfficeCompanyUUID", personnal_request_capture.company_uuid);
    }

    std::map<std::string, int> int_map;
    int_map.emplace("CaptureType", personnal_request_capture.capture_type);
    int_map.emplace("Response", 0);
    int_map.emplace("MngAccountID", personnal_request_capture.manager_id);
    int_map.emplace("DevType", personnal_request_capture.device_type);
    int_map.emplace("MngType", personnal_request_capture.manager_type);


    //表名构造
    std::string table_name = PersonalCapture::GetLogTableName(table_personnal_capture, personnal_request_capture.db_delivery_uuid, delivery);

    GET_LOG_DB_CONN_ERR_RETURN(conn, -1, personnal_request_capture.db_delivery_uuid);
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    int ret = conn->InsertData(table_name, str_map, int_map);

    return ret;
}

int PersonalCapture::UpdateLogCapturePicUrl(const std::string& mac, const std::string& pic_name, 
    const std::string& pic_url, const std::string& spic_url, int delivery, const std::string& db_delivery_uuid)
{
    if (delivery == 0)
    {
        AK_LOG_WARN << "UpdateLogCapturePicUrl failed: delivery is 0";
        return -1;
    }

    //modified by chenyc,2022.08.18,禁止多次更新同一个pic_name的url,防止同名文件的攻击

    int hash = GetLogTableHash(db_delivery_uuid, delivery);

    std::stringstream stream_sql;
    stream_sql << "update PersonalCapture_" << hash << " set PicUrl = if (PicUrl = '', '" << pic_url << "', PicUrl)"
               << ", SPicUrl = if (SPicUrl = '', '" << spic_url << "', SPicUrl)"
               << " where MAC= '"  << mac << "' and PicName = '"  << pic_name << "' and PicUrl = ''";

    GET_LOG_DB_CONN_ERR_RETURN(conn, -1, db_delivery_uuid);
    CRldb* pTmpConn = conn.get();
    if (NULL == pTmpConn)
    {
        AK_LOG_WARN << "Get DB conn failed";
        return -1;
    }
    int nRet = pTmpConn->Execute(stream_sql.str()) > 0 ? 0 : -1;
    return nRet;
}

int PersonalCapture::UpdateLogCaptureVideoUrl(const std::string& mac, const std::string& video_name,
  const std::string& video_url, int delivery, const std::string& db_delivery_uuid)
{
    int hash = GetLogTableHash(db_delivery_uuid, delivery);

    std::stringstream stream_sql;
    stream_sql << "update PersonalCapture_" << hash << " set VideoRecordUrl = '" << video_url << "'"
               << " where MAC  = '" << mac << "' and VideoRecordName = '" << video_name << "' and VideoRecordUrl = ''";

    GET_LOG_DB_CONN_ERR_RETURN(conn, -1, db_delivery_uuid);
    return conn.get()->Execute(stream_sql.str()) > 0 ? 0 : -1;
}

int PersonalCapture::UpdateLogCaptureThirdCameraPicUrl(const std::string& mac, const std::string& pic_name, 
    const std::string& pic_url, const std::string& spic_url, int delivery, const std::string& db_delivery_uuid)
{
    if (delivery == 0)
    {
        AK_LOG_WARN << "UpdateLogCaptureThirdCameraPicUrl failed: delivery is 0";
        return -1;
    }

    int hash = GetLogTableHash(db_delivery_uuid, delivery);

    std::stringstream streamSQL;
    streamSQL << "update PersonalCapture_" << hash << " set ThirdCameraPicUrl = '" << pic_url << "', ThirdCameraSPicUrl = '" << spic_url << "'"
          << " where MAC= '"
          << mac
          << "' and ThirdCameraPicName = '"
          << pic_name
          << "' and ThirdCameraPicUrl = ''";

    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldb* pTmpConn = conn.get();
    int nRet = pTmpConn->Execute(streamSQL.str()) > 0 ? 0 : -1;
    return nRet;
}

int PersonalCapture::UpdateLogMotionPicUrl(const std::string& mac, const std::string& pic_name, const std::string& pic_url,
    const std::string& spic_url, int delivery, const std::string& db_delivery_uuid)
{
    if (delivery == 0)
    {
        AK_LOG_WARN << "UpdateLogMotionPicUrl failed: delivery is 0";
        return -1;
    }

    int hash = GetLogTableHash(db_delivery_uuid, delivery);
    
    std::stringstream streamSQL;
    streamSQL << "update PersonalMotion_" << hash << " set PicUrl = if(PicUrl='','" << pic_url << "',PicUrl)"
      << ", SPicUrl = if(SPicUrl='','" << spic_url << "',SPicUrl)"
      << " where MAC= '"
      << mac
      << "' and PicName = '"
      << pic_name
      << "'";   

    GET_LOG_DB_CONN_ERR_RETURN(conn, -1, db_delivery_uuid);
    CRldb* pTmpConn = conn.get();
    if (NULL == pTmpConn)
    {
        AK_LOG_WARN << "Get DB conn failed";
        return -1;
    }
    int nRet = pTmpConn->Execute(streamSQL.str()) > 0 ? 0 : -1;
    return nRet;
}

int PersonalCapture::UpdateLogMotionVideoUrl(const std::string& mac, const std::string& video_name, 
  const std::string& video_url, int delivery, const std::string& db_delivery_uuid)
{
    int hash = GetLogTableHash(db_delivery_uuid, delivery);

    std::stringstream stream_sql;
    stream_sql << "update PersonalMotion_" << hash << " set VideoRecordUrl = '" << video_url << "' "
               << "where MAC = '" << mac << "'" << " and VideoRecordName = '" << video_name  << "'" << " and VideoRecordUrl = ''";

    GET_LOG_DB_CONN_ERR_RETURN(conn, -1, db_delivery_uuid);
    return conn.get()->Execute(stream_sql.str()) > 0 ? 0 : -1;
}

std::string PersonalCapture::GetLastSaveMonth(int max_save_month)
{
    GET_LOG_DB_CONN_ERR_RETURN(conn, "", LOG_DB_DELIVERY_UUID_DEFAULT);
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return "";
    }

    std::stringstream stream_sql;
    stream_sql << "select DATE_FORMAT(DATE_SUB(curdate(),INTERVAL " << max_save_month << " MONTH), '%Y%m')";

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());

    std::string last_save_month;
    if (query.MoveToNextRow())
    {
        last_save_month = query.GetRowData(0);
    }

    return last_save_month;
}

//获取上一次分片是否在最大保存月份的时间内。
int PersonalCapture::CheckDeliveryInMaxMonth(std::time_t delivery_time, int max_save_month)
{
    GET_LOG_DB_CONN_ERR_RETURN(conn, -1, LOG_DB_DELIVERY_UUID_DEFAULT);
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream stream_sql;
    stream_sql << "select unix_timestamp(NOW() -interval 30*" << max_save_month <<" day) < " << delivery_time;

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());

    int ret = 0;
    if (query.MoveToNextRow())
    {
        ret = ATOI(query.GetRowData(0));
    }

    return ret;
}

void PersonalCapture::DelPicByID(CRldb* conn, const std::string& table_name, const std::string& id)
{
    if (id.length() == 0 || conn == nullptr)
    {
        return;
    }

    char sql[128];
    snprintf(sql, sizeof(sql), "delete from %s where id = %s", table_name.c_str(), id.c_str());

    int ret = conn->Execute(sql);
    if (ret < 0)
    {
        AK_LOG_WARN << "execute failed:%s" << sql;
    }
    return;
}

void PersonalCapture::DelPicByRange(CRldb* conn, const std::string& table_name, int begin_id, int end_id)
{
    if (conn == nullptr)
    {
        return;
    }

    char sql[128];
    snprintf(sql, sizeof(sql), "delete from %s where id between %d and %d", table_name.c_str(), begin_id, end_id);

    int ret = conn->Execute(sql);
    if (ret < 0)
    {
        AK_LOG_WARN << "execute failed:%s" << sql;
    }
    return;
}

int PersonalCapture::delAkcsPicExpired(const std::string& table_name, std::string& id, std::set<std::string>& del_urls)
{
    // int begin_id = ATOI(id.c_str());
    // int end_id;

    char sql[1024];
    snprintf(sql, sizeof(sql),
             "/*master*/select ID,PicUrl,SPicUrl "
             " from %s "
             " where CaptureTime > (curdate() - interval 31 day) "
             " and CaptureTime < (curdate() - interval 30 day) and ID > %s" 
             " order by ID limit 0,500", table_name.c_str(),id.c_str());

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return 0;
    }

    CRldbQuery query(tmp_conn);
    query.Query(sql);

    int count = 0;
    while (query.MoveToNextRow())
    {        
        del_urls.insert(query.GetRowData(1));
        del_urls.insert(query.GetRowData(2));

        id = query.GetRowData(0);
        //DelPicByID(tmp_conn, table_name, id);     //AWS迁移 先不批量删记录
        ++count;
    }

    // end_id = ATOI(id.c_str());
    // DelPicByRange(tmp_conn, table_name, begin_id, end_id);

    AK_LOG_INFO << "delAkcsPicExpired Successed,count=" << count << ";sql=" << sql;
    ReleaseDBConn(conn);
    return count;
}

int PersonalCapture::delLOGPicExpired(const std::string& table_name, const LOG_SLICE_INFO& slice_info, 
    std::string& id, std::set<std::string>& del_urls)
{
    //int begin_id = ATOI(id.c_str());
    //int end_id;

    char sql[1024];
    snprintf(sql, sizeof(sql),
             "/*master*/select ID,PicUrl,SPicUrl "
             " from %s "
             " where CaptureTime > (curdate() - interval (30*%d+1) day) "
             " and CaptureTime < (curdate() - interval 30*%d day) and ID > %s" 
             " order by ID limit 0,500", table_name.c_str(), slice_info.max_save_month, slice_info.max_save_month, id.c_str());

    GET_LOG_DB_CONN_ERR_RETURN(conn, 0, LOG_DB_DELIVERY_UUID_DEFAULT);
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return 0;
    }

    CRldbQuery query(tmp_conn);
    query.Query(sql);

    int count = 0;
    while (query.MoveToNextRow())
    {        
        del_urls.insert(query.GetRowData(1));
        del_urls.insert(query.GetRowData(2));

        id = query.GetRowData(0);
        //DelPicByID(tmp_conn, table_name, id);     //AWS迁移 先不批量删记录
        ++count;
    }
    
    //end_id = ATOI(id.c_str());
    //DelPicByRange(tmp_conn, table_name, begin_id, end_id);

    AK_LOG_INFO << "delLOGPicExpired Successed,count=" << count << ";sql=" << sql;
    return count;
}

int PersonalCapture::GetPicExpiredTables(const std::string& basic_table, const LOG_SLICE_INFO& slice_info,
    std::vector<std::string>& akcs_tables, std::vector<std::string>& log_tables)
{
    std::string last_month;
    std::string real_table_name;
    int flag = CheckDeliveryInMaxMonth(slice_info.delivery_time, slice_info.max_save_month);
    if (slice_info.last_delivery == 0 && flag)
    {
        //第一次分片不需要处理LOG库
        last_month = GetLastSaveMonth(1);
        real_table_name = basic_table+"_"+last_month;
        akcs_tables.push_back(real_table_name);
    }
    else
    {
        last_month = GetLastSaveMonth(slice_info.max_save_month);
        for (int i=0; i<slice_info.delivery; i++)
        {
            real_table_name = basic_table+"_"+to_string(i)+"_"+last_month;
            log_tables.push_back(real_table_name);
        }
    }

    return 0;
}

//AKCS写死最近两个月
std::vector<std::string> PersonalCapture::GetAllAkcsTables(const char* table_name)
{
    std::vector<std::string> tables;
    tables.push_back(table_name);

    std::string last_month = GetLastSaveMonth(1);
    std::string basic_table = table_name;
    std::string last_month_table = basic_table+"_"+last_month;
    tables.push_back(last_month_table);

    return tables;
}

std::vector<std::string> PersonalCapture::GetAllLOGTables(const char* table_name)
{
    std::vector<std::string> tables;

    GET_LOG_DB_CONN_ERR_RETURN(conn, tables, LOG_DB_DELIVERY_UUID_DEFAULT);
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return tables;
    }

    char sqlselect[1024];
    ::snprintf(sqlselect, sizeof(sqlselect), "show tables like '%s_%%'", table_name);

    CRldbQuery query(tmp_conn);
    std::string sqlselect2 = sqlselect;
    query.Query(sqlselect2);
    while (query.MoveToNextRow())
    {
        tables.push_back(query.GetRowData(0));
    }

    return tables;
}

int PersonalCapture::DelCapturePic(const std::string& mac, std::set<std::string>& del_urls)
{
    //不存在akcs表了，因此要不再处理删除akcs的capture表
    std::vector<std::string> log_tables = GetAllLOGTables("PersonalCapture");

    for (auto& table : log_tables)
    {
        char cond[128];
        snprintf(cond,  sizeof(cond), " MAC='%s' ", mac.c_str());
        DelOneLOGTablePic(table, cond, del_urls);
    }

    return 0;
}

int PersonalCapture::DelMotionPic(const std::string& mac, std::set<std::string>& del_urls)
{
    //不存在akcs表了，因此要不再处理删除akcs的capture表
    std::vector<std::string> log_tables = GetAllLOGTables("PersonalMotion");

    for (auto& table : log_tables)
    {
        char cond[128];
        snprintf(cond,  sizeof(cond), " MAC='%s' ", mac.c_str());
        DelOneLOGTablePic(table, cond, del_urls);
    }

    return 0;
}

//删除整个社区得截图
int PersonalCapture::DelCapturePicByMngid(unsigned int mngid, std::set<std::string>& del_urls)
{
    //不存在akcs表了，因此要不再处理删除akcs的capture表
    std::vector<std::string> log_tables = GetAllLOGTables("PersonalCapture");

    for (auto& table : log_tables)
    {
        char cond[128];
        snprintf(cond,  sizeof(cond), " MngAccountID = %u ", mngid);
        DelOneLOGTablePic(table, cond, del_urls);
    }

    return 0;
}

int PersonalCapture::DelMotionPicByMngid(unsigned int mngid, std::set<std::string>& del_urls)
{
    //不存在akcs表了，因此要不再处理删除akcs的capture表
    std::vector<std::string> log_tables = GetAllLOGTables("PersonalMotion");

    for (auto& table : log_tables)
    {
        char cond[128];
        snprintf(cond,  sizeof(cond), " MngAccountID = %u ", mngid);
        DelOneLOGTablePic(table, cond, del_urls);
    }

    return 0;
}

void PersonalCapture::DelOneLOGTablePic(const std::string& table_name, const std::string& cond,
    std::set<std::string>& del_urls)
{
    GET_LOG_DB_CONN_ERR_RETURN(conn, , LOG_DB_DELIVERY_UUID_DEFAULT);
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    int i = 1;
    while (1)
    {
        int index_start = 0;
        if (i == 1)
        {
            index_start = 0;
        }
        else
        {
            index_start = (i - 1) * 5000 + 1;
        }
        int index_count = 5000;

        char sqlselect[1024];
        ::snprintf(sqlselect, sizeof(sqlselect), "select PicUrl,SPicUrl from %s where %s limit %d, %d", table_name.c_str(), cond.c_str(), index_start, index_count);

        CRldbQuery query(tmp_conn);
        int row = query.Query(sqlselect);
        if (row <= 0)
        {
            break;
        }

        int count = 0;
        while (query.MoveToNextRow())
        {
            ++count;
            char url[1024] = "";
            char surl[1024] = "";
            snprintf(url, sizeof(url), "%s", query.GetRowData(0));
            snprintf(surl, sizeof(surl), "%s", query.GetRowData(1));
            del_urls.insert(query.GetRowData(0));
            del_urls.insert(query.GetRowData(1));
            if (strlen(url) > 0)
            {
                del_urls.insert(url);
                //g_storage_mng_ptr->DeleteFile(url);
            }

            if (IsSUrlPicToBeDel(surl))
            {
                del_urls.insert(surl);
                //g_storage_mng_ptr->DeleteFile(surl);
            }
        }
        AK_LOG_INFO << "Delete Pic Num=" << count << ";sql=" << sqlselect;
        i++;
    }

    char sqldel[1024];
    ::snprintf(sqldel, sizeof(sqldel), "delete from %s where %s", table_name.c_str(), cond.c_str());
    int ret = conn->Execute(sqldel);
    if (ret < 0)
    {
        AK_LOG_WARN << "Execute failed,sql=" << sqldel;
    }
    else
    {
        AK_LOG_INFO << "Delete Record rows=" << ret << ";sql=" << sqldel;
    }
}

bool PersonalCapture::IsSUrlPicToBeDel(const char* surl)
{
    //40x40旧版本的，不需要删除
    if (strlen(surl) > 0 && !strstr(surl, "40x40"))
    {
        return true;
    }

    return false;
}

//添加测温Capture
int PersonalCapture::AddTemperatureCapture(SOCKET_MSG_DEV_REPORT_ACTIVITY& personnal_act_log)
{
	//对于用户昵称,可能含有mysql特殊字符,特别是<'>会影响sql语句,需要用\来转义
    std::string initiaor = personnal_act_log.initiator_sql;
    boost::replace_all(initiaor, "'", "\\'");
    
    char sql[1024] = {0};
    ::snprintf(sql, 1024, "insert into Temperature (MAC,PicName,CaptureTime,Fahrenheit,Status,MngAccountID)\
                           values('%s','%s',now(),'%s',%d,%d)",
                     personnal_act_log.mac,
                     personnal_act_log.pic_name,
                     initiaor.c_str(),
                     personnal_act_log.resp,
                     personnal_act_log.mng_id);

    GET_DB_CONN_ERR_RETURN(conn, -1)
    std::string sql2 = sql;
    return conn->Execute(sql2) > 0 ? 0 : -1;   
}

int PersonalCapture::UpdateTempPicUrl(const std::string& mac, const std::string& pic_name, const std::string& pic_url, const std::string& spic_url)
{
    std::stringstream streamSQL;
    streamSQL << "update Temperature set PicUrl = if(PicUrl='','" << pic_url << "',PicUrl)"
      << ", SPicUrl = if(SPicUrl='','" << spic_url << "',SPicUrl)"
      << " where MAC= '"
      << mac
      << "' and PicName = '"
      << pic_name
      << "'";	

    GET_DB_CONN_ERR_RETURN(conn, -1)

    return conn->Execute(streamSQL.str()) > 0 ? 0 : -1;
}

std::string PersonalCapture::GetLogTableName(const std::string& table_name, const std::string& db_delivery_uuid, int delivery)
{
    //hash取模
    uint32_t uuid_hash = crc32_hash(db_delivery_uuid);
    int hash = uuid_hash % delivery;

    //表名构造
    return table_name + "_" + std::to_string(hash);
}


int PersonalCapture::GetLogTableHash(const std::string& db_delivery_uuid, int delivery)
{
    uint32_t uuid_hash = crc32_hash(db_delivery_uuid);
    int hash = uuid_hash % delivery;
    return hash;
}

void PersonalCapture::RecordEmergencyContorlDoorLog(const std::string& device_uuid, const std::string& initiator, ACT_OPEN_DOOR_TYPE act_type, RelayStatus status_type, int delivery)
{
    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetUUIDDev(device_uuid, dev))
    {
        AK_LOG_WARN << "GetUUIDDev failed";
        return;
    }

    std::string account = dev.node;
    std::string sip_account = dev.sip;
    std::string location = dev.location;
	
    SOCKET_MSG_DEV_REPORT_ACTIVITY act_msg;
    act_msg.act_type = act_type;
    act_msg.unit_id = dev.unit_id;
    act_msg.mng_id = dev.project_mng_id;
    act_msg.mng_type = MANAGER_TYPE::COMMUNITY;
    act_msg.is_public= 1; //默认只开公共设备
    
    Snprintf(act_msg.mac, sizeof(act_msg.mac), dev.mac);
    Snprintf(act_msg.key, sizeof(act_msg.key), emergency_contorl_key);
    Snprintf(act_msg.account, sizeof(act_msg.account),  account.c_str());
    Snprintf(act_msg.location, sizeof(act_msg.location),  location.c_str());
    Snprintf(act_msg.project_uuid2, sizeof(act_msg.project_uuid2), dev.project_uuid);
    Snprintf(act_msg.db_delivery_uuid, sizeof(act_msg.db_delivery_uuid), dev.project_uuid);
    Snprintf(act_msg.sip_account, sizeof(act_msg.sip_account),  sip_account.c_str());
    Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  emergency_contorl_room_num);
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  initiator.c_str());
    Snprintf(act_msg.dev_uuid, sizeof(act_msg.dev_uuid), device_uuid.c_str());

    if (act_type == ACT_OPEN_DOOR_TYPE::PM_UNLOCK)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_pm_manually);
    }
    else if (act_type == ACT_OPEN_DOOR_TYPE::PM_LOCK)
    {
       Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  lock_door_pm_manually);
    }
    else if (act_type == ACT_OPEN_DOOR_TYPE::AUTO_UNLOCK)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_automatically);
    }

    //设备离线door log记录为离线;超时,版本不支持door log记录为失败
    if (status_type == RelayStatus::OFFLINE)
    {
        act_msg.resp = CAPTURE_LOG_RET_TYPE::OFFILNE;
    }
    else
    {
        act_msg.resp = CAPTURE_LOG_RET_TYPE::FAILURE;
    }
    
    dbinterface::PersonalCapture::AddPersonalCapture(act_msg, delivery);
}

int PersonalCapture::UpdateVideoRecordName(const std::string& mac, const std::string& pic_name, const std::string& video_record_name, int delivery, const std::string& db_delivery_uuid)
{
    std::string table_name = PersonalCapture::GetLogTableName(table_personnal_capture, db_delivery_uuid, delivery);
    
    std::stringstream stream_sql;
    stream_sql << "update " << table_name << " set VideoRecordName = '" << video_record_name << "'"
               << " where MAC = '" << mac << "' and PicName = '" << pic_name << "' and VideoRecordName=''";

    GET_LOG_DB_CONN_ERR_RETURN(conn, -1, db_delivery_uuid);
    return conn.get()->Execute(stream_sql.str()) > 0 ? 0 : -1;
}

DatabaseExistenceStatus PersonalCapture::GetPersonalCaptureTableList(const std::string& basic_table, std::vector<std::string>& tables_list)
{
    GET_LOG_DB_CONN_ERR_RETURN(conn, DatabaseExistenceStatus::QUERY_ERROR, LOG_DB_DELIVERY_UUID_DEFAULT);
    
    std::stringstream stream_sql;
    stream_sql << "select table_name from information_schema.tables where table_name like '" << basic_table << "%'"
               << " order by case when table_name = '" << basic_table << "' then 0 else 1 end, table_name desc";

    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        tables_list.push_back(query.GetRowData(0));
    }
    return DatabaseExistenceStatus::EXIST;
}

DatabaseExistenceStatus PersonalCapture::GetVideoRecordInfo(const std::string& call_trace_id, const std::string& db_delivery_uuid, int delivery, std::string& video_record_name, std::string& video_record_url)
{
    std::vector<std::string> table_list;
    std::string basic_table = GetLogTableName(table_personnal_capture, db_delivery_uuid, delivery);
    GetPersonalCaptureTableList(basic_table, table_list);

    GET_LOG_DB_CONN_ERR_RETURN(conn, DatabaseExistenceStatus::QUERY_ERROR, db_delivery_uuid);
    CRldbQuery query(conn.get());
    for (const auto& table : table_list)
    {
        std::stringstream stream_sql;
        stream_sql << "select VideoRecordUrl,VideoRecordName from " << table << " where CallTraceID = '" << call_trace_id << "'";

        query.Query(stream_sql.str());
        if (query.MoveToNextRow())
        {
            video_record_url = query.GetRowData(0);
            video_record_name = query.GetRowData(1);
            return DatabaseExistenceStatus::EXIST;
        }
    }
    
    return DatabaseExistenceStatus::NOT_EXIST;
}

std::string PersonalCapture::GetRandomPicName(const std::string& mac, const std::string& server_tag)
{
    std::string uuid;
    dbinterface::UUID::GenerateUUID(server_tag, uuid);
    return "fake-" + mac + "-" + uuid;
}


}
