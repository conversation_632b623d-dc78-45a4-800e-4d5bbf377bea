#ifndef AKCS_SQS_MSG_PARSE_H_
#define AKCS_SQS_MSG_PARSE_H_

#include <map> 
#include <string>
#include <list>
#include "json/json.h"

typedef std::map<std::string, std::string> SqsMsgKvList;

class AjaxMsgParser
{
public:
    enum ParseMsgStatus
    {
        FAIL = 0,
        OK = 1,
    };

    AjaxMsgParser(const std::string& msg);
    bool ParseOk()
    {
        return status_ == OK;
    }
    void Parse(const std::string& msg);

    std::string GetHubID();

    const SqsMsgKvList& GetMsgKvList() const
    {
        return msg_kv_list_;
    }
private:
    void ParseSqsMessageInfoKv(const Json::Value& body);
    
    ParseMsgStatus status_;
    SqsMsgKvList   msg_kv_list_;
};




#endif

