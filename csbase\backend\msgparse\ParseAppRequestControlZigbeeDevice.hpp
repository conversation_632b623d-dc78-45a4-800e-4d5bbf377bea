#ifndef __PARSE_APP_REQUEST_CONTROL_ZIGBEE_DEVICE_H__
#define __PARSE_APP_REQUEST_CONTROL_ZIGBEE_DEVICE_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "DclientMsgSt.h"

namespace akcs_msgparse
{

/*
<Msg>
<Type>ControlZigbeeDevice</Type>
<Params>
    <TraceID>0123233000</TraceID> // 32bit 
    <MAC>0Cxxxxxxx</MAC>
    <ZigbeeDeviceID>1</ZigbeeDeviceID>
    <DeviceType>0</DeviceType> //0:灯控; 1:温控; 2:窗帘
    <Switch>1</Switch> // 开关控制
    <!-- 以下字段根据设备类型选择性使用 -->
    <TargetTemperature>26</TargetTemperature> //温控设备：目标温度 
    <HVACMode>0</HVACMode> 
</Params>
</Msg>
*/

static int ParseAppRequestControlZigbeeDeviceMsg(char *buf, SOCKET_MSG_APP_REQUEST_CONTROL_ZIGBEE_DEVICE &control_request)
{
    if (buf == nullptr)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseAppRequestControlZigbeeDeviceMsg text: \n" << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (nullptr == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    // 主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "Mismatched " << XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* params_node = root_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM);
    if (nullptr == params_node)
    {
        AK_LOG_WARN << "Params Node is NULL";
        return -1;
    }

    // 解析参数节点
    TiXmlElement* item_node = nullptr;
    for (item_node = params_node->FirstChildElement(); item_node; item_node = item_node->NextSiblingElement())
    {
        if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_TRACE_ID) == 0)
        {
            if (item_node->GetText())
            {
                TransUtf8ToTchar(item_node->GetText(), control_request.trace_id, sizeof(control_request.trace_id) / sizeof(TCHAR));
            }
        }
        else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
        {
            if (item_node->GetText())
            {
                TransUtf8ToTchar(item_node->GetText(), control_request.mac, sizeof(control_request.mac) / sizeof(TCHAR));
            }
        }
        else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_ZIGBEE_DEVICE_ID) == 0)
        {
            if (item_node->GetText())
            {
                TransUtf8ToTchar(item_node->GetText(), control_request.zigbee_device_id, sizeof(control_request.zigbee_device_id) / sizeof(TCHAR));
            }
        }
        else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_DEVICETYPE) == 0)
        {
            if (item_node->GetText())
            {
                control_request.device_type = ATOI(item_node->GetText());
            }
        }
        else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_SWITCH) == 0)
        {
            if (item_node->GetText())
            {
                control_request.switch_status = ATOI(item_node->GetText());
            }
        }
        else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_TARGET_TEMPERATURE) == 0)
        {
            if (item_node->GetText())
            {
                TransUtf8ToTchar(item_node->GetText(), control_request.target_temperature, sizeof(control_request.target_temperature) / sizeof(TCHAR));
            }
        }
        else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_HVAC_MODE) == 0)
        {
            if (item_node->GetText())
            {
                control_request.hvac_mode = ATOI(item_node->GetText());
            }
        }
    }

    return 0;
}

}

#endif // __PARSE_APP_REQUEST_CONTROL_ZIGBEE_DEVICE_H__ 