#!/bin/bash

# 替换配置文件

# csajax.conf
sed -i "
    s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
    s/^.*gateway_num=.*/gateway_num=${GATEWAY_NUM}/g
    s/^.*kafka_ajax_global_broker_ip=.*/kafka_ajax_global_broker_ip=${AJAX_GLOBAL_KAFKA_IP}:8599/g
    " /usr/local/akcs/csajax/conf/csajax.conf

# dbproxy 配置
if [ "$ENABLE_AKCS_DBPROXY" = "1" ]; then
    sed -i "
        s/^.*db_port=.*/db_port=3308/g
        s/^.*db_ip=.*/db_ip=${DBPROXY_INNER_IP}/g" /usr/local/akcs/csajax/conf/csajax.conf
else
    sed -i "
        s/^.*db_port=.*/db_port=3306/g
        s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" /usr/local/akcs/csajax/conf/csajax.conf
fi
