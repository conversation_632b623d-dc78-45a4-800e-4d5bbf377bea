#ifndef __CSBASE_ETCD_CLI_MANAGE_H__
#define __CSBASE_ETCD_CLI_MANAGE_H__

#include <iostream>
#include <etcd/Client.hpp>
#include <etcd/Watcher.hpp>
#include <boost/noncopyable.hpp>
#include <evpp/event_loop.h>
#include <set>
#include <mutex>
#include <map>
#include <vector>

namespace csbase {
enum RegType
{
    REG_INNER = 0,
	REG_OUTER = 1,

};

}

struct GateSrvConf
{
    //csgate的配置中心具体信息
    char db_ip[64];
    int db_port;
};

struct SrvDbConf
{
    //配置中心数据库s信息
    char db_ip[64];
    int db_port;
    char db_master_ip[64];
    int db_master_port;

    SrvDbConf() {
        memset(this, 0, sizeof(*this));
    }
};


//etcd的grpc接口文档详见: https://github.com/etcd-io/etcd/blob/release-3.3/etcdserver/etcdserverpb/rpc.proto
typedef std::function<void()> WatchSrvCallback;
typedef std::function<void()> ServerConfCallback;
typedef std::function<void()> WatchKeyCallback;

class CAkEtcdCliManager : boost::noncopyable
{

private:
	//added by chenyc v5.0,支持etcd集群,集群格式形如: "127.0.0.1:4001, *************,..."
    CAkEtcdCliManager(const std::string& addr);
    ~CAkEtcdCliManager()
    {
        if (etcd_cli_)
			delete etcd_cli_;
		etcd_cli_ = nullptr;
    }
    
public:   
    static CAkEtcdCliManager* GetInstance(const std::string& addr);
 	static void destroyInstance();
    
public://以下是从注册中心获取各个应用服务器的地址
    int GetAllRouteSrvs(std::set<std::string>& route_srvs);
    int GetAllAccSrvs(std::vector<std::string>& acc_srvs);
    int GetAllAccRpcInnerSrvs(std::set<std::string>& acc_srvs);
    int GetAllRtspSrvs(std::vector<std::string>& vrtspd_srvs);
    int GetAllOpsSrvs(std::vector<std::string>& ops_srvs);
    int GetAllSessionSrvs(std::set<std::string>& session_srvs);
    int GetAllNsqlookupdHttpSrvs(std::set<std::string>& nsqlookupd_srvs);
    const std::string GetRandomAdaptSrv();
    const std::string GetRandomPushSrv();
    int GetAllPushSrvs(std::set<std::string>& push_srvs);
    int GetAllFtpSrvs(std::vector<std::string>& ftp_srvs);
    int GetAllPbxRpcInnerSrvs(std::set<std::string>& srv_addrs);
    int GetAllRbacSrvs(std::set<std::string>& rbac_srvs);
    int GetAllVideoRecordSrvs(std::vector<std::string>& video_record_srvs);
    int GetAllVideoRecordRpcSrvs(std::set<std::string>& video_record_rpc_srvs);
    int GetAllYunJiRobotRpcSrvs(std::set<std::string>& yunji_robot_rpc_srvs);

    void UpdateEtcdAddrs(const std::vector<std::string>& addresss);
    bool CheckEtcdCliStatus();
    void CheckEtcdHealth(evpp::EventLoop* loop);
    void UpdateEtcdServerStatus();

    
public://以下是从配置中心获取各个应用服务器的配置项信息
    int LoadGateSrvOuterConf(GateSrvConf &csgate_conf);
    int LoadSrvDbOuterConf(SrvDbConf &conf);
    int LoadSrvSmgConf(std::string &smg_addr);
    int LoadSrvSmgAlexaConf(std::string &smg_alexa_addr);
    int LoadSrvZipkinConf(std::string &kafka_addr);
    int LoadSrvWebAdaptEntryConf(std::string& key, std::string& web_adapt_entry);    
    int LoadSrvMqttConf(std::string &mqtt_addr);    
    int LoadSrvMqttOuterTlsConf(std::string &mqtt_addr);
    int LoadSrvMqttInnerApiHttpConf(std::string &mqtt_inner_api_http);
public:
    //added by chenyc, 2022.03.16,同步接口,调用方的线程会陷入,除特殊情况,基本不推荐使用，推荐使用下面的AddAsyncWatchKey+EnterWatchChanges
    //eg：监听/akcs的目的是监听所有的/akcs/xxx的subkey
    int WatchSrvs(const std::string& key, WatchSrvCallback& cb);
    
    //启动watch loop,线程陷入死循环中，所有/ak下面的subkeys的变化都会被监听到并执行相应的callback()，对应AddAsyncWatchKey()接口,
    void EnterWatchChanges();
    void EnterWatchWebDomainServerChanges();
    //added by chenyc, 2022.03.16,增加非阻塞的异步watch接口,与EnterWatchChanges强相关,启动EnterWatchChanges()之后，key的变化才能监听到
    //调用方可以多次调用该接口,添加多个ETCD监听的key==callback, 一旦监听的key发生变化,cb会在etcd_cli内部线程执行,调用方不需要关心
    //但是这些key如果有覆盖关系(eg：/a、/ab就属于覆盖,/a  、/b就没有)那么可能存在一个subkey发生变化，调用两次对应cb的情况
    //例如在akcs的场景下,监听/akconf/csmain 跟 /akconf/csmain/rate_limit 就会存在这个问题,建议避开这样的使用.
    void AddAsyncWatchKey(const std::string& key, WatchKeyCallback cb);
    
    int64_t RegKeepAliveSrv(const std::string& key, const std::string& value,
                                   int type, const int ttl, evpp::EventLoop* loop);
    int64_t RegSrv(const std::string& key, const std::string& value,const int ttl, int type);
    
    int KeepAliveSrv(const int64_t lease_id);
    void LeaseRevoke();

private:
    void WatchForChanges(const std::string& key, const WatchSrvCallback& cb);
    void WatchForAKChanges(const char* watch_key);
    //void AsynsWatchDefaultCallBack(etcd::Response const & resp);
    const std::string GetHealthyNodeUri() const;
	void onInnerLeaseKeepAliveTimer();
	void onOuterLeaseKeepAliveTimer();
private:
    static CAkEtcdCliManager *instance_;
    etcd::Client *etcd_cli_;
    std::vector<std::string> etcd_addrs_;
	bool is_inner_need_register_again_;//是否需要重新注册
	bool is_outer_need_register_again_;
	std::string inner_key_;
	std::string inner_value_ ;
	int inner_ttl_;
	std::string outer_key_;
	std::string outer_value_ ;
	int outer_ttl_;
	int64_t inner_lease_id_;
	int64_t outer_lease_id_;
    typedef std::map<std::string/*watch key*/, WatchKeyCallback> WatchKeyCbMap;
    typedef std::shared_ptr<etcd::Watcher> WatcherPtr;
    typedef std::map<std::string/*watch key*/, WatcherPtr> KeyWatcherMap;
    std::mutex watch_key_lock_;
    WatchKeyCbMap watch_key_cb_;
    bool etcd_stat_;
};

#endif // __CSBASE_ETCD_CLI_MANAGE_H__

