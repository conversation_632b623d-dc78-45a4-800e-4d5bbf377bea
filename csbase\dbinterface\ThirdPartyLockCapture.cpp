#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "ThirdPartyLockCapture.h"

namespace dbinterface {

static const std::string third_party_lock_capture_info_sec = " MAC,PicName,LockName,Initiator,CaptureTime,Status,PersonalAccountUUID,CaptureType,Response,LockType ";

void ThirdPartyLockCapture::GetThirdPartyLockCaptureFromSql(ThirdPartyLockCaptureInfo& third_party_lock_capture_info, CRldbQuery& query)
{
    Snprintf(third_party_lock_capture_info.mac, sizeof(third_party_lock_capture_info.mac), query.GetRowData(0));
    Snprintf(third_party_lock_capture_info.pic_name, sizeof(third_party_lock_capture_info.pic_name), query.GetRowData(1));
    Snprintf(third_party_lock_capture_info.lock_name, sizeof(third_party_lock_capture_info.lock_name), query.GetRowData(2));
    Snprintf(third_party_lock_capture_info.initiator, sizeof(third_party_lock_capture_info.initiator), query.GetRowData(3));
    Snprintf(third_party_lock_capture_info.capture_time, sizeof(third_party_lock_capture_info.capture_time), query.GetRowData(4));
    third_party_lock_capture_info.status = ATOI(query.GetRowData(5));
    Snprintf(third_party_lock_capture_info.personal_account_uuid, sizeof(third_party_lock_capture_info.personal_account_uuid), query.GetRowData(6));
    third_party_lock_capture_info.capture_type = ATOI(query.GetRowData(7));
    third_party_lock_capture_info.response = ATOI(query.GetRowData(8));
    third_party_lock_capture_info.lock_type = static_cast<ThirdPartyLockType>(ATOI(query.GetRowData(9)));
    return;
}

int ThirdPartyLockCapture::InsertThirdPartyLockCapture(const ThirdPartyLockCaptureInfo& third_party_lock_capture_info)
{   
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    // 插入数据构造
    std::map<std::string, std::string> strMap;
    strMap.emplace("MAC", third_party_lock_capture_info.mac);
    strMap.emplace("PicName", third_party_lock_capture_info.pic_name);
    strMap.emplace("LockName", third_party_lock_capture_info.lock_name);
    strMap.emplace("Initiator", third_party_lock_capture_info.initiator);
    strMap.emplace("PersonalAccountUUID", third_party_lock_capture_info.personal_account_uuid);
    if (strlen(third_party_lock_capture_info.capture_time) > 0)
    {
        strMap.emplace("CaptureTime", third_party_lock_capture_info.capture_time);
    }


    std::map<std::string, int> intMap;
    intMap.emplace("Status", third_party_lock_capture_info.status);
    intMap.emplace("CaptureType", third_party_lock_capture_info.capture_type);
    intMap.emplace("Response", third_party_lock_capture_info.response);
    intMap.emplace("LockType", third_party_lock_capture_info.lock_type);

    // 表名直接使用 ThirdPartyLockCapture
    std::string table_name = "ThirdPartyLockCapture";

    int ret = tmp_conn->InsertData(table_name, strMap, intMap);

    // 释放数据库连接
    ReleaseDBConn(conn); 
    return ret;
}

}
