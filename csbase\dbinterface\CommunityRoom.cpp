#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/CommunityRoom.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"
#include "ConnectionManager.h"
namespace dbinterface
{

CommunityRoom::CommunityRoom()
{

}

int CommunityRoom::GetCommunityRoomByID(int room_id, CommunityRoomInfo &info)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream sql;
    sql << "SELECT RoomName,Floor,UnitID,CommunityUnitUUID,UUID FROM CommunityRoom WHERE ID = '" << room_id << "'";
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        Snprintf(info.room_number, sizeof(info.room_number), query.GetRowData(0));
        Snprintf(info.floor, sizeof(info.floor), query.GetRowData(1));
        info.unit_id = ATOI(query.GetRowData(2));
        Snprintf(info.unit_uuid, sizeof(info.unit_uuid), query.GetRowData(3));
        Snprintf(info.uuid, sizeof(info.uuid), query.GetRowData(4));
    }
    ReleaseDBConn(conn);
    return 0;
}

int CommunityRoom::GetCommunityRoomByUUID(const std::string& uuid, CommunityRoomInfo &info)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    std::stringstream sql;
    sql << "SELECT RoomName,Floor,UnitID FROM CommunityRoom WHERE UUID = '" << uuid << "'";
    
    CRldbQuery query(db_conn.get());
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        Snprintf(info.room_number, sizeof(info.room_number), query.GetRowData(0));
        Snprintf(info.floor, sizeof(info.floor), query.GetRowData(1));
        info.unit_id = ATOI(query.GetRowData(2));
    }
    
    return 0;
}

//通过主账号列表查找Room和Unit
int CommunityRoom::GetCommunityUnitAndRoomByNodes(const std::string &nodes, CommunityRoomInfoMap &map)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    std::stringstream str_sql;
    str_sql << "select C.RoomName,C.Floor,P.Account,C.UnitID,U.UUID from CommunityRoom C left join PersonalAccount P on P.RoomID=C.ID left join CommunityUnit U on U.ID=C.UnitID where P.Account in (" << nodes << ")";

    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        CommunityRoomInfo info;
        Snprintf(info.room_number, sizeof(info.room_number), query.GetRowData(0));
        Snprintf(info.floor, sizeof(info.floor), query.GetRowData(1));
        std::string node = query.GetRowData(2);
        info.unit_id = ATOI(query.GetRowData(3));
        Snprintf(info.unit_uuid, sizeof(info.unit_uuid), query.GetRowData(4));
        map.insert(std::make_pair(node, info));
    }
    ReleaseDBConn(conn);
    return 0;
}

int CommunityRoom::GetCommunityRoomByNode(const std::string &node, CommunityRoomInfo &info)
{
    int ret = -1;
    if (node.size() ==0)
    {
        return ret;
    }
    
    std::stringstream str_sql;
    str_sql << "select C.RoomName,C.Floor,P.Account from CommunityRoom C left join PersonalAccount P on P.RoomID=C.ID where P.Account='" << node << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, -1); //获取数据库连接失败时，返回-1
    CRldbQuery query(tmp_conn.get());
    query.Query(str_sql.str());
    if (query.MoveToNextRow())
    {
        Snprintf(info.room_number, sizeof(info.room_number), query.GetRowData(0));
        Snprintf(info.floor, sizeof(info.floor), query.GetRowData(1));
        ret = 0;
    }
    return ret;
}

int CommunityRoom::GetRoomIDByUnitIDAndRoomNum(uint32_t unit_id, const std::string& room_num, uint32_t& room_id)
{
    int ret = -1;
    if(room_num.size() == 0 || unit_id == 0)
    {
        return ret;
    }
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ret;
    }

    std::stringstream str_sql;
    str_sql << "select ID from CommunityRoom where UnitID = " << unit_id << " and RoomName = '" << room_num << "' limit 1";

    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    if (query.MoveToNextRow())
    {
        room_id = ATOI(query.GetRowData(0));
        ret = 0;
    }
    ReleaseDBConn(conn);
    return ret;
}


int CommunityRoom::GetCommunityRoomByProjetID(uint32_t project_id, CommunityRoomInfoMap &map)
{
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);

    std::stringstream str_sql;
    str_sql << "select C.RoomName,C.Floor,P.Account,C.UnitID,C.CommunityUnitUUID from CommunityRoom C left join PersonalAccount P on P.RoomID=C.ID where P.ParentID=" << project_id <<" and P.Role=" << ACCOUNT_ROLE_COMMUNITY_MAIN;

    CRldbQuery query(tmp_conn.get());
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        CommunityRoomInfo info;
        Snprintf(info.room_number, sizeof(info.room_number), query.GetRowData(0));
        Snprintf(info.floor, sizeof(info.floor), query.GetRowData(1));
        std::string node = query.GetRowData(2);
        info.unit_id = ATOI(query.GetRowData(3));
        Snprintf(info.unit_uuid, sizeof(info.unit_uuid), query.GetRowData(4));        
        map.insert(std::make_pair(node, info));
    }
    return 0;    
}


}

